package com.taskiq.app.viewmodel

import android.app.Application
import android.content.Context
import android.content.Intent
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.google.android.gms.auth.api.signin.GoogleSignInClient
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.android.gms.common.api.ApiException
import com.google.android.gms.common.api.Scope
import com.google.api.client.googleapis.extensions.android.gms.auth.GoogleAccountCredential
import com.google.api.client.http.javanet.NetHttpTransport
import com.google.api.client.json.gson.GsonFactory
import com.google.api.services.gmail.Gmail
import com.google.api.services.gmail.GmailScopes
import com.google.api.services.drive.DriveScopes
import com.taskiq.app.service.GmailService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import java.util.Collections
import android.accounts.Account
import android.accounts.AccountManager
import android.app.Activity
import android.content.pm.PackageManager
import android.Manifest
import android.util.Log
import androidx.core.content.ContextCompat

/**
 * ViewModel for handling Gmail authentication and API access
 */
class GmailAuthViewModel(application: Application) : AndroidViewModel(application) {

    private val TAG = "GmailAuthViewModel"

    // Default state is signed out
    private val _authState = MutableStateFlow<AuthState>(AuthState.SignedOut)
    val authState: StateFlow<AuthState> = _authState

    // Gmail account information
    private val _gmailAccount = MutableStateFlow<String?>(null)
    val gmailAccount: StateFlow<String?> = _gmailAccount

    // Available Gmail accounts on the device
    private val _availableAccounts = MutableStateFlow<List<String>>(emptyList())
    val availableAccounts: StateFlow<List<String>> = _availableAccounts

    // Google Sign-In client
    private var googleSignInClient: GoogleSignInClient? = null

    // Gmail service for API calls
    private var gmailService: GmailService? = null

    init {
        // Initialize Google Sign-In client
        initializeGoogleSignInClient()
        // Try to retrieve available accounts
        refreshAvailableAccounts()
        // Start with signed out state
        _authState.value = AuthState.SignedOut
        // Don't check for existing sign-in to avoid conflicts
    }

    /**
     * Initialize Google Sign-In client with Gmail scopes
     */
    private fun initializeGoogleSignInClient() {
        val context = getApplication<Application>()

        try {
            // Use configuration with Gmail and Google Drive scopes
            val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
                .requestEmail()
                .requestProfile()
                .requestScopes(Scope(GmailScopes.GMAIL_READONLY))
                .requestScopes(Scope(DriveScopes.DRIVE_FILE))
                .build()

            googleSignInClient = GoogleSignIn.getClient(context, gso)
            Log.d(TAG, "Google Sign-In client initialized with Gmail scopes")

            // Check if Google Play Services is available
            val availability = com.google.android.gms.common.GoogleApiAvailability.getInstance()
            val resultCode = availability.isGooglePlayServicesAvailable(context)
            if (resultCode == com.google.android.gms.common.ConnectionResult.SUCCESS) {
                Log.d(TAG, "Google Play Services is available")
            } else {
                Log.w(TAG, "Google Play Services issue: $resultCode")
                // Try to make Google Play Services available
                if (availability.isUserResolvableError(resultCode)) {
                    Log.d(TAG, "Google Play Services error is user resolvable")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing Google Sign-In client: ${e.message}", e)
        }
    }

    /**
     * Check if user is already signed in
     */
    private fun checkExistingSignIn() {
        try {
            val context = getApplication<Application>()
            val account = GoogleSignIn.getLastSignedInAccount(context)

            if (account != null && !account.isExpired) {
                Log.d(TAG, "Found existing signed-in account: ${account.email}")

                // Check if account has required scopes
                val hasGmailScope = GoogleSignIn.hasPermissions(account, Scope(GmailScopes.GMAIL_READONLY))
                val hasDriveScope = GoogleSignIn.hasPermissions(account, Scope(DriveScopes.DRIVE_FILE))

                if (hasGmailScope && hasDriveScope) {
                    Log.d(TAG, "Existing account has Gmail and Drive permissions, using it")
                    _gmailAccount.value = account.email
                    initializeGmailService(account)
                    _authState.value = AuthState.SignedIn(account.email ?: "")
                } else {
                    Log.d(TAG, "Existing account lacks Gmail or Drive permissions (Gmail: $hasGmailScope, Drive: $hasDriveScope), need to re-authenticate")
                    _authState.value = AuthState.SignedOut
                }
            } else {
                Log.d(TAG, "No existing signed-in account found")
                _authState.value = AuthState.SignedOut
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking existing sign-in: ${e.message}")
            _authState.value = AuthState.SignedOut
        }
    }
    
    /**
     * Refresh the list of available Gmail accounts on the device
     */
    fun refreshAvailableAccounts() {
        viewModelScope.launch {
            try {
                val context = getApplication<Application>()
                
                // Check if we have the GET_ACCOUNTS permission
                if (ContextCompat.checkSelfPermission(context, Manifest.permission.GET_ACCOUNTS) 
                    == PackageManager.PERMISSION_GRANTED) {
                    
                    // Get accounts from AccountManager
                    val accountManager = AccountManager.get(context)
                    val googleAccounts = accountManager.getAccountsByType("com.google")
                    
                    Log.d(TAG, "Found ${googleAccounts.size} Google accounts on device")
                    
                    // Try both Google account type and Gmail account type
                    if (googleAccounts.isEmpty()) {
                        val gmailAccounts = accountManager.getAccountsByType("com.google.android.gm")
                        Log.d(TAG, "Found ${gmailAccounts.size} Gmail accounts on device")
                        
                        if (gmailAccounts.isNotEmpty()) {
                            // Extract email addresses
                            val gmailAddresses = gmailAccounts.map { it.name }
                                .filter { it.contains("@") }
                            
                            _availableAccounts.value = gmailAddresses
                            Log.d(TAG, "Using Gmail accounts: $gmailAddresses")
                            return@launch
                        }
                    }
                    
                    // Extract email addresses from Google accounts
                    val gmailAddresses = googleAccounts.map { it.name }
                                               .filter { it.contains("@") }
                    
                    _availableAccounts.value = gmailAddresses
                    
                    Log.d(TAG, "Found ${gmailAddresses.size} Gmail accounts on device: $gmailAddresses")
                } else {
                    Log.d(TAG, "No GET_ACCOUNTS permission")
                    _availableAccounts.value = emptyList()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error getting accounts: ${e.message}", e)
                _availableAccounts.value = emptyList()
            }
        }
    }
    
    /**
     * Start Google OAuth sign-in process
     */
    fun startGoogleSignIn(): Intent? {
        Log.d(TAG, "=== STARTING GOOGLE OAUTH SIGN-IN ===")

        return try {
            _authState.value = AuthState.Loading
            Log.d(TAG, "Setting up Google OAuth configuration")

            val context = getApplication<Application>()

            // Check Google Play Services availability
            val availability = com.google.android.gms.common.GoogleApiAvailability.getInstance()
            val resultCode = availability.isGooglePlayServicesAvailable(context)

            if (resultCode != com.google.android.gms.common.ConnectionResult.SUCCESS) {
                Log.e(TAG, "Google Play Services not available: $resultCode")
                _authState.value = AuthState.Error("Google Play Services required")
                return null
            }

            // Clear any existing sign-in state to avoid conflicts
            try {
                Log.d(TAG, "Clearing existing Google Sign-In state")
                googleSignInClient?.signOut()?.addOnCompleteListener {
                    Log.d(TAG, "Previous sign-in state cleared")
                }
            } catch (e: Exception) {
                Log.w(TAG, "Could not clear existing state: ${e.message}")
            }

            // Use OAuth configuration with Gmail and Drive scopes to request permissions upfront
            Log.d(TAG, "Creating GoogleSignInOptions with Gmail and Drive scopes")

            val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
                .requestEmail()
                .requestProfile()
                .requestScopes(Scope(GmailScopes.GMAIL_READONLY))
                .requestScopes(Scope(DriveScopes.DRIVE_FILE))
                .build()

            val client = GoogleSignIn.getClient(context, gso)
            val intent = client.signInIntent
            Log.d(TAG, "Google OAuth sign-in intent created successfully")
            intent

        } catch (e: Exception) {
            Log.e(TAG, "Google OAuth setup failed: ${e.message}", e)
            _authState.value = AuthState.Error("Google Sign-In setup failed: ${e.message}")
            null
        }
    }



    /**
     * Reset auth state (useful when sign-in is cancelled)
     */
    fun resetAuthState() {
        _authState.value = AuthState.SignedOut
        Log.d(TAG, "Auth state reset to SignedOut")
    }

    /**
     * Handle Google Sign-In result
     */
    fun handleSignInResult(data: Intent?) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "=== HANDLING GOOGLE SIGN-IN RESULT ===")
                Log.d(TAG, "Intent data: $data")
                Log.d(TAG, "Intent extras: ${data?.extras}")
                Log.d(TAG, "Intent action: ${data?.action}")

                if (data == null) {
                    Log.e(TAG, "Sign-in result data is null")
                    _authState.value = AuthState.Error("Sign-in failed: No data returned")
                    return@launch
                }

                Log.d(TAG, "Getting signed-in account from intent...")
                val task = GoogleSignIn.getSignedInAccountFromIntent(data)
                Log.d(TAG, "Task created: $task")
                Log.d(TAG, "Task is successful: ${task.isSuccessful}")
                Log.d(TAG, "Task is complete: ${task.isComplete}")
                Log.d(TAG, "Task exception: ${task.exception}")

                Log.d(TAG, "Getting account from task...")
                val account = task.getResult(ApiException::class.java)
                Log.d(TAG, "Account retrieved: $account")

                if (account != null) {
                    Log.d(TAG, "Google Sign-In successful for: ${account.email}")
                    Log.d(TAG, "Account display name: ${account.displayName}")
                    Log.d(TAG, "Account granted scopes: ${account.grantedScopes}")
                    Log.d(TAG, "Account ID: ${account.id}")
                    Log.d(TAG, "Account server auth code: ${account.serverAuthCode}")

                    // Store the signed-in account
                    _gmailAccount.value = account.email

                    // Initialize Gmail service with authenticated account
                    initializeGmailService(account)

                    // Update authentication state
                    _authState.value = AuthState.SignedIn(account.email ?: "")
                    Log.d(TAG, "Auth state updated to SignedIn")

                    // Check if we have Gmail and Drive permissions (for logging purposes)
                    val hasGmailScope = GoogleSignIn.hasPermissions(account, Scope(GmailScopes.GMAIL_READONLY))
                    val hasDriveScope = GoogleSignIn.hasPermissions(account, Scope(DriveScopes.DRIVE_FILE))
                    Log.d(TAG, "Has Gmail scope: $hasGmailScope, Has Drive scope: $hasDriveScope")

                    // Connect Gmail service to global storage for real scanning
                    val currentGmailService = gmailService
                    if (currentGmailService != null) {
                        Log.d(TAG, "=== GMAIL SERVICE INITIALIZATION SUCCESS ===")
                        Log.d(TAG, "Gmail service initialized successfully, making it available for real scanning")
                        Log.d(TAG, "Gmail service details: ${currentGmailService.javaClass.simpleName}")
                        // Store the Gmail service in a way that BillService can access it
                        setGlobalGmailService(currentGmailService)
                        Log.d(TAG, "Global Gmail service set successfully")

                        // Test the Gmail service immediately
                        Log.d(TAG, "Testing Gmail service immediately after initialization...")
                        testGmailConnection()
                    } else {
                        Log.e(TAG, "=== GMAIL SERVICE INITIALIZATION FAILED ===")
                        Log.e(TAG, "Gmail service not initialized, real scanning will not be available")
                        Log.e(TAG, "This could be due to missing Gmail permissions or OAuth configuration issues")
                    }
                } else {
                    Log.e(TAG, "Google Sign-In failed: account is null")
                    _authState.value = AuthState.Error("Sign-in failed: No account returned")
                }
            } catch (e: ApiException) {
                Log.e(TAG, "Google Sign-In failed with ApiException - code: ${e.statusCode}, message: ${e.message}")
                Log.e(TAG, "ApiException details: ${e.localizedMessage}")

                // For status code 12501 (user cancelled), let's check if we actually have account data
                if (e.statusCode == 12501) {
                    Log.w(TAG, "Status code 12501 - checking if account data is available despite cancellation status")
                    try {
                        val task = GoogleSignIn.getSignedInAccountFromIntent(data)
                        val account = task.getResult()
                        if (account != null) {
                            Log.d(TAG, "Found account despite cancellation status: ${account.email}")
                            // Process the account as successful
                            _gmailAccount.value = account.email
                            initializeGmailService(account)
                            _authState.value = AuthState.SignedIn(account.email ?: "")
                            return@launch
                        }
                    } catch (ex: Exception) {
                        Log.d(TAG, "No account available, treating as actual cancellation")
                    }
                }

                when (e.statusCode) {
                    12500 -> {
                        Log.e(TAG, "=== DETAILED 12500 ERROR ANALYSIS ===")
                        Log.e(TAG, "Sign-in failed - internal error or configuration issue")
                        Log.e(TAG, "Status code: ${e.statusCode}")
                        Log.e(TAG, "Status message: ${e.status?.statusMessage}")
                        Log.e(TAG, "Status resolution: ${e.status?.resolution}")
                        Log.e(TAG, "Localized message: ${e.localizedMessage}")
                        Log.e(TAG, "Full exception: $e")

                        // Try to get more details about the configuration
                        try {
                            val context = getApplication<Application>()
                            Log.e(TAG, "Package name: ${context.packageName}")

                            // Check if there's a signed-in account
                            val lastAccount = GoogleSignIn.getLastSignedInAccount(context)
                            Log.e(TAG, "Last signed-in account: ${lastAccount?.email}")

                        } catch (ex: Exception) {
                            Log.e(TAG, "Error getting additional debug info: ${ex.message}")
                        }

                        Log.e(TAG, "This usually indicates:")
                        Log.e(TAG, "1. OAuth consent screen not properly configured")
                        Log.e(TAG, "2. App not added to test users (if in testing mode)")
                        Log.e(TAG, "3. Incorrect client ID configuration")
                        Log.e(TAG, "4. Missing or incorrect SHA-1 fingerprint")
                        Log.e(TAG, "=== END 12500 ERROR ANALYSIS ===")

                        // Log detailed error information for debugging
                        Log.e(TAG, "OAuth configuration error - this needs to be fixed in Google Cloud Console")

                        _authState.value = AuthState.Error("Google Sign-In configuration error. Please try again.")
                    }
                    12501 -> {
                        Log.w(TAG, "User cancelled the sign-in process")
                        _authState.value = AuthState.Error("Sign-in cancelled by user")
                    }
                    12502 -> {
                        Log.w(TAG, "Sign-in already in progress")
                        _authState.value = AuthState.Error("Sign-in currently in progress")
                    }
                    7 -> {
                        Log.e(TAG, "Network error during sign-in")
                        _authState.value = AuthState.Error("Network error - please check your connection")
                    }
                    10 -> {
                        Log.e(TAG, "Developer error - check Google Sign-In configuration")
                        _authState.value = AuthState.Error("Configuration error - please contact support")
                    }
                    else -> {
                        Log.e(TAG, "Unknown sign-in error: ${e.statusCode}")
                        Log.e(TAG, "Error message: ${e.message}")
                        Log.e(TAG, "Error status: ${e.status}")
                        _authState.value = AuthState.Error("Sign-in failed: ${e.message}")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error handling sign-in result: ${e.message}", e)
                _authState.value = AuthState.Error("Sign-in error: ${e.message}")
            }
        }
    }

    /**
     * Initialize Gmail service with authenticated account
     */
    private fun initializeGmailService(account: GoogleSignInAccount) {
        try {
            val context = getApplication<Application>()

            // Check if account has Gmail scope
            val hasGmailScope = GoogleSignIn.hasPermissions(account, Scope(GmailScopes.GMAIL_READONLY))
            Log.d(TAG, "Account has Gmail scope: $hasGmailScope")

            if (hasGmailScope) {
                Log.d(TAG, "Gmail scope granted - initializing Gmail service")

                // Create credential with Gmail scope
                val credential = GoogleAccountCredential.usingOAuth2(
                    context,
                    Collections.singleton(GmailScopes.GMAIL_READONLY)
                )
                credential.selectedAccount = account.account

                // Initialize Gmail service
                gmailService = GmailService(context, credential)
                Log.d(TAG, "Gmail service initialized successfully for: ${account.email}")

                // Test Gmail service connection
                testGmailConnection()
            } else {
                Log.w(TAG, "Gmail scope not granted - Gmail scanning will not be available")
                Log.w(TAG, "User may have denied Gmail permissions during OAuth flow")
                // Still allow basic email linking without Gmail API access
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing Gmail service: ${e.message}")
            // Don't fail the whole process if Gmail service fails
        }
    }

    /**
     * Request Gmail permissions after basic sign-in is successful
     */
    private fun requestGmailPermissionsAfterSignIn(account: GoogleSignInAccount) {
        viewModelScope.launch {
            try {
                val context = getApplication<Application>()
                Log.d(TAG, "Requesting Gmail permissions for: ${account.email}")

                // Create new GoogleSignInOptions with Gmail scope
                val gsoWithGmail = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
                    .requestEmail()
                    .requestProfile()
                    .requestScopes(Scope(GmailScopes.GMAIL_READONLY))
                    .build()

                val clientWithGmail = GoogleSignIn.getClient(context, gsoWithGmail)

                // Request additional permissions
                Log.d(TAG, "Requesting additional Gmail scope...")
                val task = clientWithGmail.silentSignIn()

                task.addOnSuccessListener { updatedAccount ->
                    Log.d(TAG, "Gmail permissions granted successfully")

                    // Check if Gmail scope is now available
                    val hasGmailScope = GoogleSignIn.hasPermissions(updatedAccount, Scope(GmailScopes.GMAIL_READONLY))
                    Log.d(TAG, "Gmail scope available after request: $hasGmailScope")

                    if (hasGmailScope) {
                        // Initialize Gmail service with the updated account
                        val credential = GoogleAccountCredential.usingOAuth2(
                            context,
                            Collections.singleton(GmailScopes.GMAIL_READONLY)
                        )
                        credential.selectedAccount = updatedAccount.account

                        gmailService = GmailService(context, credential)
                        setGlobalGmailService(gmailService)
                        Log.d(TAG, "Gmail service initialized successfully with permissions")

                        // Test Gmail service
                        testGmailConnection()
                    }
                }.addOnFailureListener { e ->
                    Log.w(TAG, "Could not get Gmail permissions: ${e.message}")
                    Log.w(TAG, "Gmail API features will not be available, but basic email linking works")
                }

            } catch (e: Exception) {
                Log.w(TAG, "Error requesting Gmail permissions: ${e.message}")
                Log.w(TAG, "Gmail API features will not be available, but basic email linking works")
            }
        }
    }





    /**
     * Request additional Gmail scopes if not already granted
     */
    private fun requestAdditionalScopes(account: GoogleSignInAccount) {
        try {
            val context = getApplication<Application>()
            Log.d(TAG, "Requesting additional Gmail scopes for account: ${account.email}")

            // This would typically require additional user consent
            // For now, we'll log that additional scopes are needed
            Log.w(TAG, "Additional Gmail scopes needed. User may need to re-authenticate with Gmail permissions.")
        } catch (e: Exception) {
            Log.e(TAG, "Error requesting additional scopes: ${e.message}")
        }
    }

    /**
     * Select a Gmail account to use (for device accounts)
     */
    fun selectAccount(email: String) {
        viewModelScope.launch {
            try {
                _authState.value = AuthState.Loading

                // Store the selected account
                _gmailAccount.value = email

                // Update authentication state
                _authState.value = AuthState.SignedIn(email)

                Log.d(TAG, "Account selected: $email")
            } catch (e: Exception) {
                Log.e(TAG, "Error selecting account: ${e.message}")
                _authState.value = AuthState.Error("Failed to select account: ${e.message}")
            }
        }
    }
    
    /**
     * Sign out the current account
     */
    fun signOut() {
        viewModelScope.launch {
            try {
                // Sign out from Google Sign-In
                googleSignInClient?.signOut()?.await()

                // Clear local state
                _gmailAccount.value = null
                gmailService = null
                _authState.value = AuthState.SignedOut

                Log.d(TAG, "Signed out successfully")
            } catch (e: Exception) {
                Log.e(TAG, "Error during sign out: ${e.message}")
                // Still clear local state even if sign out fails
                _gmailAccount.value = null
                gmailService = null
                _authState.value = AuthState.SignedOut
            }
        }
    }
    
    /**
     * Check if permission is granted for a certain operation
     */
    fun hasRequiredPermissions(): Boolean {
        val context = getApplication<Application>()
        return ContextCompat.checkSelfPermission(
            context, 
            Manifest.permission.GET_ACCOUNTS
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * Fetch bill emails from Gmail using real API
     */
    suspend fun fetchBillEmails() {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                _authState.value = AuthState.Loading

                gmailService?.let { service ->
                    Log.d(TAG, "Starting real Gmail API bill scan")

                    // Scan emails for bills using real Gmail API
                    val billsFound = service.scanEmailsForBills()

                    withContext(Dispatchers.Main) {
                        _authState.value = AuthState.Success(billsFound.size)
                    }

                    Log.d(TAG, "Found ${billsFound.size} bills from Gmail API")
                } ?: run {
                    Log.e(TAG, "Gmail service not initialized")
                    withContext(Dispatchers.Main) {
                        _authState.value = AuthState.Error("Gmail service not available")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error fetching bills from Gmail: ${e.message}")
                withContext(Dispatchers.Main) {
                    _authState.value = AuthState.Error("Failed to fetch emails: ${e.message}")
                }
            }
        }
    }

    /**
     * Test Gmail API connection and permissions
     */
    fun testGmailConnection() {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                gmailService?.let { service ->
                    Log.d(TAG, "Testing Gmail API connection...")

                    // Test by scanning for bills - this will test the full Gmail API functionality
                    val bills = service.scanEmailsForBills()
                    Log.d(TAG, "Gmail API test successful:")
                    Log.d(TAG, "- Found ${bills.size} bills during test scan")

                    if (bills.isNotEmpty()) {
                        Log.d(TAG, "- Sample bill: ${bills.first().title} - $${bills.first().amount}")
                    }

                } ?: run {
                    Log.e(TAG, "Gmail service not available for testing")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Gmail API test failed: ${e.message}")
            }
        }
    }

    /**
     * Get Gmail service instance
     */
    fun getGmailService(): GmailService? = gmailService
    
    // Helper method to add delay in coroutines
    private suspend fun delay(timeMillis: Long) {
        kotlinx.coroutines.delay(timeMillis)
    }

    companion object {
        const val GOOGLE_SIGN_IN_REQUEST_CODE = 9001
        const val GMAIL_PERMISSION_REQUEST_CODE = 9002
        private const val TAG = "GmailAuthViewModel"

        // Global Gmail service storage for real scanning
        @Volatile
        private var globalGmailService: com.taskiq.app.service.GmailService? = null

        /**
         * Set global Gmail service for access by other components
         */
        fun setGlobalGmailService(service: com.taskiq.app.service.GmailService?) {
            globalGmailService = service
            Log.d(TAG, "Global Gmail service ${if (service != null) "set" else "cleared"}")
        }

        /**
         * Get global Gmail service for real scanning
         */
        fun getGlobalGmailService(): com.taskiq.app.service.GmailService? {
            return globalGmailService
        }
    }
}

/**
 * Represents different authentication states
 */
sealed class AuthState {
    object SignedOut : AuthState()
    object Loading : AuthState()
    data class SignedIn(val email: String) : AuthState()
    data class Success(val messageCount: Int) : AuthState()
    data class Error(val message: String) : AuthState()
}