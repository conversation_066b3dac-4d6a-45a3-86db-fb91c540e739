package com.taskiq.app.ui.components

import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.AccountCircle
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.taskiq.app.service.EmailAuthService
import com.taskiq.app.ui.theme.cardBackgroundColor
import com.taskiq.app.ui.theme.textFieldColors
import com.taskiq.app.ui.theme.ProfessionalBlue
import com.taskiq.app.ui.theme.ProfessionalWhite

@Composable
fun EmailScanningDialog(
    emails: List<String>,
    isScanning: Boolean,
    onAddEmail: (String) -> Boolean,
    onRemoveEmail: (String) -> Unit,
    onScanEmails: () -> Unit,
    onDismiss: () -> Unit,
    onVerifyEmail: ((String, Activity) -> Boolean)? = null
) {
    var newEmail by remember { mutableStateOf("") }
    var emailError by remember { mutableStateOf("") }
    var showGmailSelection by remember { mutableStateOf(false) }
    var availableGmailAccounts by remember { mutableStateOf<List<String>>(emptyList()) }

    // Get the current context and activity for OAuth flow
    val context = LocalContext.current
    val activity = context.findActivity()

    // Initialize EmailAuthService to get device Gmail accounts
    val emailAuthService = remember { EmailAuthService(context) }

    // Load available Gmail accounts when dialog opens
    LaunchedEffect(Unit) {
        availableGmailAccounts = emailAuthService.getDeviceGmailAccounts()
        android.util.Log.d("EmailScanningDialog", "Loaded ${availableGmailAccounts.size} Gmail accounts: $availableGmailAccounts")
    }
    
    Dialog(onDismissRequest = onDismiss) {
        Surface(
            shape = MaterialTheme.shapes.medium,
            color = MaterialTheme.colorScheme.surface
        ) {
            if (showGmailSelection) {
                android.util.Log.d("EmailScanningDialog", "Showing Gmail selection screen with ${availableGmailAccounts.size} accounts")
                // Show Gmail account selection screen
                GmailSelectionContent(
                    availableAccounts = availableGmailAccounts,
                    linkedEmails = emails,
                    onAccountSelected = { email ->
                        android.util.Log.d("EmailScanningDialog", "onAccountSelected called for: $email")
                        if (onVerifyEmail != null && activity != null) {
                            android.util.Log.d("EmailScanningDialog", "Calling onVerifyEmail for: $email")
                            val verified = onVerifyEmail(email, activity)
                            android.util.Log.d("EmailScanningDialog", "Verification result for $email: $verified")
                            if (verified) {
                                emailError = "Gmail account linked successfully! Scanning for bills..."
                                // Trigger automatic bill scanning after successful linking
                                onScanEmails()
                            } else {
                                emailError = "Failed to link Gmail account"
                            }
                        } else {
                            android.util.Log.e("EmailScanningDialog", "onVerifyEmail is null or activity is null")
                        }
                        showGmailSelection = false
                    },
                    onBack = { showGmailSelection = false },
                    onDismiss = onDismiss
                )
            } else {
                // Show main email management screen
                Column(
                    modifier = Modifier
                        .padding(24.dp)
                        .fillMaxWidth()
                ) {
                    // Header with title and close button
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Linked Gmail Accounts",
                            style = MaterialTheme.typography.headlineSmall
                        )

                        IconButton(onClick = onDismiss) {
                            Icon(
                                imageVector = Icons.Default.Close,
                                contentDescription = "Close"
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Info text
                    Text(
                        text = "Manage your linked Gmail accounts for automatic bill scanning.",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                
                    Spacer(modifier = Modifier.height(16.dp))

                    // Link A New Gmail button
                    OutlinedButton(
                        onClick = {
                            android.util.Log.d("EmailScanningDialog", "Link A New Gmail button clicked")
                            showGmailSelection = true
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = null,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("Link A New Gmail")
                    }
                
                    if (emailError.isNotEmpty()) {
                        Text(
                            text = emailError,
                            color = if (emailError.contains("successfully"))
                                MaterialTheme.colorScheme.primary
                            else
                                MaterialTheme.colorScheme.error,
                            style = MaterialTheme.typography.bodySmall,
                            modifier = Modifier.padding(top = 8.dp)
                        )
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // List of linked Gmail accounts
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        shape = MaterialTheme.shapes.medium,
                        colors = CardDefaults.cardColors(
                            containerColor = cardBackgroundColor()
                        )
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = "Linked Gmail Accounts",
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Bold
                            )
                        
                            Spacer(modifier = Modifier.height(8.dp))

                            if (emails.isEmpty()) {
                                Text(
                                    text = "No Gmail accounts linked yet.\nTap 'Link A New Gmail' to get started.",
                                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                                    textAlign = TextAlign.Center,
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 16.dp)
                                )
                            } else {
                            LazyColumn(
                                modifier = Modifier.weight(1f, false)
                            ) {
                                items(emails) { email ->
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(vertical = 8.dp),
                                        horizontalArrangement = Arrangement.SpaceBetween,
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Row(
                                            verticalAlignment = Alignment.CenterVertically
                                        ) {
                                            Icon(
                                                imageVector = Icons.Default.Email,
                                                contentDescription = null,
                                                tint = MaterialTheme.colorScheme.primary
                                            )
                                            
                                            Spacer(modifier = Modifier.width(8.dp))
                                            
                                            Text(
                                                text = email,
                                                style = MaterialTheme.typography.bodyMedium
                                            )
                                            
                                            Spacer(modifier = Modifier.width(8.dp))
                                            
                                            Icon(
                                                imageVector = Icons.Default.CheckCircle,
                                                contentDescription = "Verified",
                                                tint = Color.Green,
                                                modifier = Modifier.size(16.dp)
                                            )
                                        }
                                        
                                        IconButton(
                                            onClick = { onRemoveEmail(email) }
                                        ) {
                                            Icon(
                                                imageVector = Icons.Default.Delete,
                                                contentDescription = "Remove Email",
                                                tint = Color.Red
                                            )
                                        }
                                    }
                                    
                                    HorizontalDivider()
                                }
                            }
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(24.dp))

                    // Scan button
                    Button(
                        onClick = onScanEmails,
                        modifier = Modifier.fillMaxWidth(),
                        enabled = emails.isNotEmpty() && !isScanning,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = ProfessionalBlue,
                            contentColor = ProfessionalWhite
                        )
                    ) {
                        if (isScanning) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(20.dp),
                                strokeWidth = 2.dp,
                                color = MaterialTheme.colorScheme.onPrimary
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                        } else {
                            Icon(
                                imageVector = Icons.Default.Refresh,
                                contentDescription = null
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                        }
                        Text(text = if (isScanning) "Scanning..." else "Scan Gmail for Bills")
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    // Note text
                    Text(
                        text = "We access Gmail accounts logged into your device. We only scan for bills with your permission.",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth()
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Lock,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = "Secure Account Access",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.primary,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun GmailSelectionContent(
    availableAccounts: List<String>,
    linkedEmails: List<String>,
    onAccountSelected: (String) -> Unit,
    onBack: () -> Unit,
    onDismiss: () -> Unit
) {
    Column(
        modifier = Modifier
            .padding(24.dp)
            .fillMaxWidth()
    ) {
        // Header with back button and close button
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconButton(onClick = {
                    android.util.Log.d("EmailScanningDialog", "Back button clicked in Gmail selection")
                    onBack()
                }) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "Back"
                    )
                }
                Text(
                    text = "Select Gmail Account",
                    style = MaterialTheme.typography.headlineSmall
                )
            }

            IconButton(onClick = onDismiss) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "Close"
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Info text
        Text(
            text = "Choose a Gmail account from your device to link for bill scanning.",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Available Gmail accounts
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = MaterialTheme.shapes.medium,
            colors = CardDefaults.cardColors(
                containerColor = cardBackgroundColor()
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Available Gmail Accounts",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(8.dp))

                if (availableAccounts.isEmpty()) {
                    Text(
                        text = "No Gmail accounts found on this device.\nPlease add a Gmail account in your device settings.",
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                        textAlign = TextAlign.Center,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 16.dp)
                    )
                } else {
                    // Debug text to show account count
                    Text(
                        text = "Found ${availableAccounts.size} Gmail accounts:",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                        modifier = Modifier.padding(bottom = 8.dp)
                    )

                    // Debug: Log the accounts being displayed
                    android.util.Log.d("EmailScanningDialog", "Displaying accounts: $availableAccounts")

                    LazyColumn(
                        modifier = Modifier.heightIn(max = 300.dp)
                    ) {
                        items(availableAccounts) { account ->
                            android.util.Log.d("EmailScanningDialog", "Rendering account item: $account")
                            val isAlreadyLinked = linkedEmails.contains(account)

                            Card(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 4.dp),
                                colors = CardDefaults.cardColors(
                                    containerColor = if (isAlreadyLinked)
                                        MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
                                    else
                                        MaterialTheme.colorScheme.surface
                                ),
                                onClick = if (!isAlreadyLinked) {
                                    {
                                        android.util.Log.d("EmailScanningDialog", "Card clicked for account: $account")
                                        onAccountSelected(account)
                                    }
                                } else {
                                    {}
                                }
                            ) {
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(16.dp),
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Icon(
                                            imageVector = Icons.Default.AccountCircle,
                                            contentDescription = null,
                                            tint = MaterialTheme.colorScheme.primary,
                                            modifier = Modifier.size(24.dp)
                                        )

                                        Spacer(modifier = Modifier.width(12.dp))

                                        Column {
                                            Text(
                                                text = account,
                                                style = MaterialTheme.typography.bodyMedium,
                                                fontWeight = FontWeight.Medium
                                            )
                                            if (isAlreadyLinked) {
                                                Text(
                                                    text = "Already linked",
                                                    style = MaterialTheme.typography.bodySmall,
                                                    color = MaterialTheme.colorScheme.primary
                                                )
                                            }
                                        }
                                    }

                                    if (isAlreadyLinked) {
                                        Icon(
                                            imageVector = Icons.Default.CheckCircle,
                                            contentDescription = "Already linked",
                                            tint = MaterialTheme.colorScheme.primary,
                                            modifier = Modifier.size(20.dp)
                                        )
                                    } else {
                                        TextButton(
                                            onClick = {
                                                android.util.Log.d("EmailScanningDialog", "TextButton clicked for account: $account")
                                                onAccountSelected(account)
                                            },
                                            modifier = Modifier.padding(4.dp)
                                        ) {
                                            Text(
                                                "Link",
                                                color = MaterialTheme.colorScheme.primary,
                                                fontWeight = FontWeight.Bold
                                            )
                                        }
                                    }
                                }
                            }

                            Spacer(modifier = Modifier.height(4.dp))
                        }
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Note text
        Text(
            text = "Only Gmail accounts logged into this device are shown. We use secure device authentication.",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth()
        )
    }
}

// Extension function to find the activity from a context
fun Context.findActivity(): Activity? {
    var context = this
    while (context is ContextWrapper) {
        if (context is Activity) {
            return context
        }
        context = context.baseContext
    }
    return null
}

private fun isValidEmail(email: String): Boolean {
    val emailRegex = Regex(
        "[a-zA-Z0-9\\+\\.\\_\\%\\-\\+]{1,256}" +
        "\\@" +
        "[a-zA-Z0-9][a-zA-Z0-9\\-]{0,64}" +
        "(" +
        "\\." +
        "[a-zA-Z0-9][a-zA-Z0-9\\-]{0,25}" +
        ")+"
    )
    return email.matches(emailRegex)
}