package com.taskiq.app.service

import android.app.AlarmManager
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.taskiq.app.MainActivity
import com.taskiq.app.R
import com.taskiq.app.model.Task
import com.taskiq.app.model.TaskPriority
import com.taskiq.app.model.Subtask
import com.taskiq.app.model.Bill
import com.taskiq.app.utils.NotificationHistoryManager
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.concurrent.TimeUnit
import android.util.Log
import com.taskiq.app.model.TaskFrequency
import java.time.LocalDate
import java.util.Calendar
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * Service to manage notifications for tasks and important dates.
 */
class NotificationService(private val context: Context) {
    private var hasNotificationPermission = false
    private val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
    
    private val notificationHistoryManager = NotificationHistoryManager(context)
    private val coroutineScope = CoroutineScope(Dispatchers.IO)
    
    companion object {
        // Make constants internal for accessibility within the module (e.g., by NotificationReceiver)
        internal const val CHANNEL_ID_TASKS = "task_reminders"
        internal const val CHANNEL_ID_IMPORTANT_DATES = "important_dates"
        internal const val CHANNEL_ID_TASK_COMPLETED = "task_completed"
        internal const val CHANNEL_ID_DAILY_SUMMARY = "daily_summary"
        
        private const val NOTIFICATION_ID_OFFSET_TASKS = 1000
        private const val NOTIFICATION_ID_OFFSET_DATES = 2000
        private const val NOTIFICATION_ID_OFFSET_BILLS = 4000 // Separate offset for bills
        private const val NOTIFICATION_ID_TASK_COMPLETED = 3000
        private const val NOTIFICATION_ID_DAILY_SUMMARY = 5000
        private const val REQUEST_NOTIFICATION_PERMISSION = 123
    }
    
    init {
        checkNotificationPermission()
        createNotificationChannels()
    }
    
    private fun checkNotificationPermission() {
        hasNotificationPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            context.checkSelfPermission(android.Manifest.permission.POST_NOTIFICATIONS) == android.content.pm.PackageManager.PERMISSION_GRANTED
        } else {
            true // Permission not required for Android 12 and below
        }
    }
    
    fun requestNotificationPermission(activity: android.app.Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (!hasNotificationPermission) {
                activity.requestPermissions(
                    arrayOf(android.Manifest.permission.POST_NOTIFICATIONS),
                    REQUEST_NOTIFICATION_PERMISSION
                )
            }
        }
    }
    
    fun onPermissionResult(requestCode: Int, grantResults: IntArray) {
        when (requestCode) {
            REQUEST_NOTIFICATION_PERMISSION -> {
                hasNotificationPermission = grantResults.isNotEmpty() &&
                    grantResults[0] == android.content.pm.PackageManager.PERMISSION_GRANTED
            }
        }
    }
    
    /**
     * Create notification channels for Android O and above
     */
    internal fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // Task reminders channel
            val taskChannel = NotificationChannel(
                CHANNEL_ID_TASKS,
                "TaskIQ Reminders",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Notifications for upcoming and urgent tasks"
            }
            
            // Important dates channel
            val datesChannel = NotificationChannel(
                CHANNEL_ID_IMPORTANT_DATES,
                "Important Dates",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "Notifications for important dates and events"
            }
            
            // Task completed channel
            val completedChannel = NotificationChannel(
                CHANNEL_ID_TASK_COMPLETED,
                "Task Completed",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Notifications for completed tasks"
            }

            // Daily summary channel
            val dailySummaryChannel = NotificationChannel(
                CHANNEL_ID_DAILY_SUMMARY,
                "Daily Summary",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "Daily summary of tasks, bills, and important dates"
            }

            // Register the channels
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(taskChannel)
            notificationManager.createNotificationChannel(datesChannel)
            notificationManager.createNotificationChannel(completedChannel)
            notificationManager.createNotificationChannel(dailySummaryChannel)
        }
    }
    
    // Get notification preferences from shared preferences
    private fun getNotificationPreferences(): Map<String, Boolean> {
        val sharedPrefs = context.getSharedPreferences("notification_preferences", Context.MODE_PRIVATE)
        return mapOf(
            "taskReminders" to sharedPrefs.getBoolean("taskReminders", true),
            "billDueDates" to sharedPrefs.getBoolean("billDueDates", true),
            "importantDates" to sharedPrefs.getBoolean("importantDates", true),
            "systemNotifications" to sharedPrefs.getBoolean("systemNotifications", true),
            "dailySummaryEnabled" to sharedPrefs.getBoolean("dailySummaryEnabled", true)
        )
    }

    // Get daily summary time preferences
    private fun getDailySummaryTime(): Pair<Int, Int> {
        val sharedPrefs = context.getSharedPreferences("notification_preferences", Context.MODE_PRIVATE)
        val hour = sharedPrefs.getInt("dailySummaryHour", 8)
        val minute = sharedPrefs.getInt("dailySummaryMinute", 0)
        return Pair(hour, minute)
    }
    
    /**
     * Schedule a notification for a task
     */
    fun scheduleTaskReminder(task: Task) {
        try {
            // Debug logging for high priority tasks
            if (task.priority == TaskPriority.HIGH) {
                Log.d("NotificationService", "Attempting to schedule notification for high-priority task: ${task.title}")
                Log.d("NotificationService", "Task details - DueDate: ${task.dueDate}, ReminderTime: ${task.reminderTime}, Priority: ${task.priority}")
            }
            
            // Ensure dueDate and reminderTime are not null first
            if (task.dueDate == null || task.reminderTime == null) {
                Log.d("NotificationService", "Task has no due date or reminder time, skipping: ${task.title}")
                return
            }

            // Skip completed tasks
            if (task.isCompleted) {
                Log.d("NotificationService", "Task is already completed, skipping notification: ${task.title}")
                return
            }

            // Only schedule notifications for high priority tasks
            if (task.priority != TaskPriority.HIGH) {
                Log.d("NotificationService", "Task is not high priority, skipping: ${task.title}")
                return
            }

            // Check preferences
            val preferences = getNotificationPreferences()
            Log.d("NotificationService", "Notification preferences: ${preferences}")
            
            if (!preferences["taskReminders"]!!) {
                Log.d("NotificationService", "Task reminders disabled in preferences, skipping notification for: ${task.title}")
                return
            }

            val calendar = Calendar.getInstance()
            calendar.time = task.dueDate // dueDate is confirmed non-null here

            // Handle reminderTime which is a Date object
            val reminderCalendar = Calendar.getInstance()
            reminderCalendar.time = task.reminderTime
            
            calendar.set(Calendar.HOUR_OF_DAY, reminderCalendar.get(Calendar.HOUR_OF_DAY))
            calendar.set(Calendar.MINUTE, reminderCalendar.get(Calendar.MINUTE))
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)

            val notificationTimeMillis = calendar.timeInMillis
            val currentTimeMillis = System.currentTimeMillis()
            
            Log.d("NotificationService", "Calculated notification time: ${Date(notificationTimeMillis)}, Current time: ${Date(currentTimeMillis)}")

            // Only check if time is in the past for today's tasks
            // For future dates, we should still schedule the notification
            val todayCalendar = Calendar.getInstance()
            val taskDayCalendar = Calendar.getInstance()
            taskDayCalendar.time = task.dueDate
            
            val isToday = (todayCalendar.get(Calendar.YEAR) == taskDayCalendar.get(Calendar.YEAR) &&
                todayCalendar.get(Calendar.DAY_OF_YEAR) == taskDayCalendar.get(Calendar.DAY_OF_YEAR))
            
            // Don't schedule if it's today and the time is in the past
            if (isToday && notificationTimeMillis <= currentTimeMillis) {
                Log.d("NotificationService", "Task reminder time is in the past and the task is due today, skipping: ${task.title}")
                return
            }

            val notificationId = NOTIFICATION_ID_OFFSET_TASKS + task.id.hashCode() % 1000
            val requestCode = task.id.hashCode() // Unique request code for PendingIntent

            val intent = Intent(context, NotificationReceiver::class.java).apply {
                putExtra(NotificationReceiver.EXTRA_NOTIFICATION_ID, notificationId)
                putExtra(NotificationReceiver.EXTRA_CHANNEL_ID, CHANNEL_ID_TASKS)
                putExtra(NotificationReceiver.EXTRA_REQUEST_CODE, requestCode)
                putExtra(NotificationReceiver.EXTRA_TITLE, "Task Due: ${task.title}")
                putExtra(NotificationReceiver.EXTRA_CONTENT_TEXT, SimpleDateFormat("hh:mm a", Locale.getDefault()).format(Date(notificationTimeMillis)))
                // You might want to add more relevant info or a big text style
                putExtra(NotificationReceiver.EXTRA_BIG_TEXT, "Task '${task.title}' is due at ${SimpleDateFormat("hh:mm a", Locale.getDefault()).format(Date(notificationTimeMillis))}. Priority: ${task.priority}")
            }

            val pendingIntent = PendingIntent.getBroadcast(
                context,
                requestCode,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            // Check for SCHEDULE_EXACT_ALARM permission before scheduling
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (alarmManager.canScheduleExactAlarms()) {
                    try {
                        Log.d("NotificationService", "Setting exact alarm for ${task.title} at ${Date(notificationTimeMillis)}")
                        alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, notificationTimeMillis, pendingIntent)
                        val formattedTime = SimpleDateFormat("MMM dd, yyyy 'at' hh:mm a", Locale.getDefault()).format(Date(notificationTimeMillis))
                        Log.d("NotificationService", "Scheduled exact task reminder for ${task.title} at $formattedTime")
                    } catch (e: Exception) {
                        Log.e("NotificationService", "Error setting exact alarm: ${e.message}", e)
                        // Fallback to non-exact alarm
                        alarmManager.setAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, notificationTimeMillis, pendingIntent)
                        Log.d("NotificationService", "Fallback to inexact alarm after error")
                    }
                } else {
                    Log.d("NotificationService", "Device doesn't allow exact alarms, using inexact alarm")
                    alarmManager.setAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, notificationTimeMillis, pendingIntent)
                    val formattedTime = SimpleDateFormat("MMM dd, yyyy 'at' hh:mm a", Locale.getDefault()).format(Date(notificationTimeMillis))
                    Log.d("NotificationService", "Scheduled inexact task reminder for ${task.title} at $formattedTime")
                }
            } else {
                try {
                    Log.d("NotificationService", "Setting exact alarm for older Android version: ${task.title}")
                    alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, notificationTimeMillis, pendingIntent)
                    val formattedTime = SimpleDateFormat("MMM dd, yyyy 'at' hh:mm a", Locale.getDefault()).format(Date(notificationTimeMillis))
                    Log.d("NotificationService", "Scheduled exact task reminder for ${task.title} at $formattedTime")
                } catch (e: Exception) {
                    Log.e("NotificationService", "Error setting exact alarm on older device: ${e.message}", e)
                    // Fallback to normal setAndAllowWhileIdle
                    alarmManager.setAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, notificationTimeMillis, pendingIntent)
                    Log.d("NotificationService", "Fallback to inexact alarm after error")
                }
            }
            
            Log.d("NotificationService", "Scheduled task reminder: ${task.title} for ${Date(notificationTimeMillis)}")
            
            // Save this to notification history
            saveNotificationToHistory(
                "Task Scheduled: ${task.title}",
                "Scheduled for ${SimpleDateFormat("MMM dd, yyyy 'at' hh:mm a", Locale.getDefault()).format(Date(notificationTimeMillis))}"
            )
        } catch (e: Exception) {
            Log.e("NotificationService", "Error scheduling task reminder", e)
        }
    }

    /**
     * Schedule a notification for an important date.
     * Will be shown on the date itself.
     */
    fun scheduleImportantDateReminder(task: Task) {
        try {
            // Check if notifications for important dates are enabled
            val preferences = getNotificationPreferences()
            if (!preferences["importantDates"]!!) {
                Log.d("NotificationService", "Important date notifications disabled, skipping notification for: ${task.title}")
                return
            }
            
            // Ensure we have a due date
            if (task.dueDate == null) {
                Log.d("NotificationService", "Important date has no due date, skipping: ${task.title}")
                return
            }
            
            // Skip completed tasks
            if (task.isCompleted) {
                Log.d("NotificationService", "Task is already completed, skipping notification: ${task.title}")
                return
            }
            
            // Set notification time for 9 AM on the due date
            val calendar = Calendar.getInstance()
            calendar.time = task.dueDate
            
            // If reminderTime is set, use it; otherwise default to 9 AM
            if (task.reminderTime != null) {
                val reminderCalendar = Calendar.getInstance()
                reminderCalendar.time = task.reminderTime
                
                calendar.set(Calendar.HOUR_OF_DAY, reminderCalendar.get(Calendar.HOUR_OF_DAY))
                calendar.set(Calendar.MINUTE, reminderCalendar.get(Calendar.MINUTE))
            } else {
                calendar.set(Calendar.HOUR_OF_DAY, 9) // Default to 9 AM
                calendar.set(Calendar.MINUTE, 0)
            }
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            
            val notificationTimeMillis = calendar.timeInMillis
            val currentTimeMillis = System.currentTimeMillis()
            
            // Only check if time is in the past for today's dates
            // For future dates, we should still schedule the notification
            val todayCalendar = Calendar.getInstance()
            val taskDayCalendar = Calendar.getInstance()
            taskDayCalendar.time = task.dueDate
            
            val isToday = (todayCalendar.get(Calendar.YEAR) == taskDayCalendar.get(Calendar.YEAR) &&
                todayCalendar.get(Calendar.DAY_OF_YEAR) == taskDayCalendar.get(Calendar.DAY_OF_YEAR))
            
            // Don't schedule if it's today and the time is in the past
            if (isToday && notificationTimeMillis <= currentTimeMillis) {
                Log.d("NotificationService", "Important date reminder time is in the past and the date is today, skipping: ${task.title}")
                return
            }
            
            // Calculate days until the important date
            val today = Date()
            val daysUntil = TimeUnit.MILLISECONDS.toDays(task.dueDate.time - today.time)
            
            // Craft notification message
            val title = "Important Date: ${task.title}"
            val formattedDueDate = SimpleDateFormat("EEE, MMM dd", Locale.getDefault()).format(task.dueDate)
            val contentText = if (daysUntil == 0L) "Today" else "$formattedDueDate"
            
            // Add more details if they exist
            val details = if (task.description.isNotBlank()) task.description else ""
            val bigText = if (details.isNotBlank()) "$task.title: $details\n$formattedDueDate" else "$task.title\n$formattedDueDate"
            
            // Create unique notification ID based on task ID
            val notificationId = NOTIFICATION_ID_OFFSET_DATES + task.id.hashCode() % 1000
            val requestCode = task.id.hashCode()
            
            val intent = Intent(context, NotificationReceiver::class.java).apply {
                putExtra(NotificationReceiver.EXTRA_NOTIFICATION_ID, notificationId)
                putExtra(NotificationReceiver.EXTRA_CHANNEL_ID, CHANNEL_ID_IMPORTANT_DATES)
                putExtra(NotificationReceiver.EXTRA_REQUEST_CODE, requestCode)
                putExtra(NotificationReceiver.EXTRA_TITLE, title)
                putExtra(NotificationReceiver.EXTRA_CONTENT_TEXT, contentText)
                putExtra(NotificationReceiver.EXTRA_BIG_TEXT, bigText)
            }
            
            val pendingIntent = PendingIntent.getBroadcast(
                context,
                requestCode,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            
            // Schedule the notification
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (alarmManager.canScheduleExactAlarms()) {
                    try {
                        Log.d("NotificationService", "Setting exact alarm for important date: ${task.title} at ${Date(notificationTimeMillis)}")
                        alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, notificationTimeMillis, pendingIntent)
                        val formattedTime = SimpleDateFormat("MMM dd, yyyy 'at' hh:mm a", Locale.getDefault()).format(Date(notificationTimeMillis))
                        Log.d("NotificationService", "Scheduled exact important date reminder for ${task.title} at $formattedTime")
                    } catch (e: Exception) {
                        Log.e("NotificationService", "Error setting exact alarm for important date: ${e.message}", e)
                        // Fallback to non-exact alarm
                        alarmManager.setAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, notificationTimeMillis, pendingIntent)
                        Log.d("NotificationService", "Fallback to inexact alarm after error")
                    }
                } else {
                    Log.d("NotificationService", "Device doesn't allow exact alarms, using inexact alarm for important date")
                    alarmManager.setAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, notificationTimeMillis, pendingIntent)
                    val formattedTime = SimpleDateFormat("MMM dd, yyyy 'at' hh:mm a", Locale.getDefault()).format(Date(notificationTimeMillis))
                    Log.d("NotificationService", "Scheduled inexact important date reminder for ${task.title} at $formattedTime")
                }
            } else {
                try {
                    Log.d("NotificationService", "Setting exact alarm for older Android version for important date: ${task.title}")
                    alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, notificationTimeMillis, pendingIntent)
                    val formattedTime = SimpleDateFormat("MMM dd, yyyy 'at' hh:mm a", Locale.getDefault()).format(Date(notificationTimeMillis))
                    Log.d("NotificationService", "Scheduled exact important date reminder for ${task.title} at $formattedTime")
                } catch (e: Exception) {
                    Log.e("NotificationService", "Error setting exact alarm for important date on older device: ${e.message}", e)
                    // Fallback to normal setAndAllowWhileIdle
                    alarmManager.setAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, notificationTimeMillis, pendingIntent)
                    Log.d("NotificationService", "Fallback to inexact alarm after error")
                }
            }
            
            Log.d("NotificationService", "Scheduled important date reminder: ${task.title} for ${Date(notificationTimeMillis)}")
            
            // Save this to notification history
            saveNotificationToHistory(
                "Date Reminder Scheduled: ${task.title}",
                "Scheduled for ${SimpleDateFormat("MMM dd, yyyy 'at' hh:mm a", Locale.getDefault()).format(Date(notificationTimeMillis))}"
            )
        } catch (e: Exception) {
            Log.e("NotificationService", "Error scheduling important date notification", e)
        }
    }

    /**
     * Cancel a task reminder notification
     */
    fun cancelTaskReminder(taskId: String) {
        val notificationId = NOTIFICATION_ID_OFFSET_TASKS + taskId.hashCode() % 1000
        val requestCode = taskId.hashCode()
        val intent = Intent(context, NotificationReceiver::class.java)
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            requestCode,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        alarmManager.cancel(pendingIntent)
        pendingIntent.cancel() // Also cancel the PendingIntent itself
        NotificationManagerCompat.from(context).cancel(notificationId) // Cancel if already shown
        Log.d("NotificationService", "Cancelling notification and alarm for task ID: $taskId")
    }
    
    /**
     * Cancel an important date reminder notification
     */
    fun cancelImportantDateReminder(dateId: String) {
        val notificationId = NOTIFICATION_ID_OFFSET_DATES + dateId.hashCode() % 1000
        val requestCode = dateId.hashCode()
        val intent = Intent(context, NotificationReceiver::class.java)
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            requestCode,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        alarmManager.cancel(pendingIntent)
        pendingIntent.cancel()
        NotificationManagerCompat.from(context).cancel(notificationId) // Cancel if already shown
        Log.d("NotificationService", "Cancelling notification and alarm for date ID: $dateId")
    }
    
    /**
     * Schedule a notification for a bill due date at 8 AM on the due date
     */
    fun scheduleBillReminder(bill: Bill) {
        try {
            // Check if bill notifications are enabled in preferences
            val preferences = getNotificationPreferences()
            if (!preferences["billDueDates"]!!) {
                Log.d("NotificationService", "Bill notifications disabled in preferences, skipping notification for: ${bill.title}")
                return
            }

            // Schedule for 8 AM on the due date
            val calendar = Calendar.getInstance()
            calendar.timeInMillis = bill.dueDate.atStartOfDay(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli()
            calendar.set(Calendar.HOUR_OF_DAY, 8) // 8 AM
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)

            val notificationTimeMillis = calendar.timeInMillis

            // Don't schedule if time is in the past
            if (notificationTimeMillis <= System.currentTimeMillis()) {
                Log.d("NotificationService", "Bill reminder time is in the past, skipping: ${bill.title}")
                return
            }

            val daysUntilDue = java.time.temporal.ChronoUnit.DAYS.between(LocalDate.now(), bill.dueDate)
            val isOverdue = daysUntilDue < 0
            val title = "Bills Reminder"
            val dueDateText = "Due: ${bill.dueDate.format(java.time.format.DateTimeFormatter.ofPattern("MMM dd, yyyy"))}"
            val amountText = "Amount: $${String.format("%.2f", bill.amount)}"
            val contentText = "You have pending bills due today"
            val bigText = "Today's pending bills summary:\n${bill.title} - $amountText - $dueDateText"

            val notificationId = NOTIFICATION_ID_OFFSET_BILLS + bill.id.hashCode() % 1000
            val requestCode = bill.id.hashCode()

            val intent = Intent(context, NotificationReceiver::class.java).apply {
                putExtra(NotificationReceiver.EXTRA_NOTIFICATION_ID, notificationId)
                putExtra(NotificationReceiver.EXTRA_CHANNEL_ID, CHANNEL_ID_TASKS)
                putExtra(NotificationReceiver.EXTRA_REQUEST_CODE, requestCode)
                putExtra(NotificationReceiver.EXTRA_TITLE, title)
                putExtra(NotificationReceiver.EXTRA_CONTENT_TEXT, contentText)
                putExtra(NotificationReceiver.EXTRA_BIG_TEXT, bigText)
            }

            val pendingIntent = PendingIntent.getBroadcast(
                context,
                requestCode,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            // Schedule the notification alarm
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (alarmManager.canScheduleExactAlarms()) {
                    alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, notificationTimeMillis, pendingIntent)
                    val formattedTime = SimpleDateFormat("MMM dd, yyyy 'at' hh:mm a", Locale.getDefault()).format(Date(notificationTimeMillis))
                    Log.d("NotificationService", "Scheduled exact bill reminder for ${bill.title} at $formattedTime")
                } else {
                    alarmManager.setAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, notificationTimeMillis, pendingIntent)
                    val formattedTime = SimpleDateFormat("MMM dd, yyyy 'at' hh:mm a", Locale.getDefault()).format(Date(notificationTimeMillis))
                    Log.d("NotificationService", "Scheduled inexact bill reminder for ${bill.title} at $formattedTime")
                }
            } else {
                // For Android 11 and below
                alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, notificationTimeMillis, pendingIntent)
                val formattedTime = SimpleDateFormat("MMM dd, yyyy 'at' hh:mm a", Locale.getDefault()).format(Date(notificationTimeMillis))
                Log.d("NotificationService", "Scheduled exact bill reminder for ${bill.title} at $formattedTime")
            }

            Log.d("NotificationService", "Scheduled bill reminder: ${bill.title} for ${Date(notificationTimeMillis)}")
        } catch (e: Exception) {
            // Log notification error
            Log.e("NotificationService", "Error scheduling bill notification", e)
        }
    }
    
    /**
     * Schedule a daily summary notification for pending bills at 8 AM
     */
    fun scheduleDailyBillSummary(bills: List<Bill>) {
        try {
            // Check if bill notifications are enabled in preferences
            val preferences = getNotificationPreferences()
            if (!preferences["billDueDates"]!!) {
                Log.d("NotificationService", "Bill notifications disabled in preferences, skipping daily summary")
                return
            }
            
            // If no pending bills, don't schedule a notification
            val pendingBills = bills.filter { !it.isPaid }
            if (pendingBills.isEmpty()) {
                Log.d("NotificationService", "No pending bills, skipping daily summary")
                return
            }

            // Schedule for 8 AM tomorrow
            val calendar = Calendar.getInstance()
            // If it's past 8am today, schedule for tomorrow, otherwise schedule for today
            if (calendar.get(Calendar.HOUR_OF_DAY) >= 8) {
                calendar.add(Calendar.DAY_OF_MONTH, 1)
            }
            calendar.set(Calendar.HOUR_OF_DAY, 8) // Always use 8 AM per requirements
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)

            val notificationTimeMillis = calendar.timeInMillis
            
            // Build summary text of all pending bills
            val title = "Bills Due Today"
            val contentText = "You have ${pendingBills.size} pending bills today"
            
            val billsList = pendingBills.joinToString("\n") { bill ->
                "${bill.title} - $${String.format("%.2f", bill.amount)}"
            }
            val bigText = "Today's pending bills:\n$billsList"

            val notificationId = NOTIFICATION_ID_OFFSET_BILLS
            val requestCode = "daily_bills_summary".hashCode()

            val intent = Intent(context, NotificationReceiver::class.java).apply {
                putExtra(NotificationReceiver.EXTRA_NOTIFICATION_ID, notificationId)
                putExtra(NotificationReceiver.EXTRA_CHANNEL_ID, CHANNEL_ID_TASKS)
                putExtra(NotificationReceiver.EXTRA_REQUEST_CODE, requestCode)
                putExtra(NotificationReceiver.EXTRA_TITLE, title)
                putExtra(NotificationReceiver.EXTRA_CONTENT_TEXT, contentText)
                putExtra(NotificationReceiver.EXTRA_BIG_TEXT, bigText)
            }

            val pendingIntent = PendingIntent.getBroadcast(
                context,
                requestCode,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            // Schedule the notification alarm
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (alarmManager.canScheduleExactAlarms()) {
                    alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, notificationTimeMillis, pendingIntent)
                    val formattedTime = SimpleDateFormat("MMM dd, yyyy 'at' hh:mm a", Locale.getDefault()).format(Date(notificationTimeMillis))
                    Log.d("NotificationService", "Scheduled exact daily bill summary at $formattedTime")
                } else {
                    alarmManager.setAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, notificationTimeMillis, pendingIntent)
                    val formattedTime = SimpleDateFormat("MMM dd, yyyy 'at' hh:mm a", Locale.getDefault()).format(Date(notificationTimeMillis))
                    Log.d("NotificationService", "Scheduled inexact daily bill summary at $formattedTime")
                }
            } else {
                // For Android 11 and below
                alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, notificationTimeMillis, pendingIntent)
                val formattedTime = SimpleDateFormat("MMM dd, yyyy 'at' hh:mm a", Locale.getDefault()).format(Date(notificationTimeMillis))
                Log.d("NotificationService", "Scheduled exact daily bill summary at $formattedTime")
            }

            Log.d("NotificationService", "Scheduled daily bill summary for ${pendingBills.size} bills at ${Date(notificationTimeMillis)}")
            
            // Save this to notification history
            saveNotificationToHistory(
                "Bills Summary Scheduled",
                "Scheduled for ${SimpleDateFormat("MMM dd, yyyy 'at' hh:mm a", Locale.getDefault()).format(Date(notificationTimeMillis))} - ${pendingBills.size} pending bills"
            )
        } catch (e: Exception) {
            // Log notification error
            Log.e("NotificationService", "Error scheduling daily bill summary", e)
        }
    }
    
    /**
     * Cancel a bill reminder notification
     */
    fun cancelBillReminder(billId: String) {
        val notificationId = NOTIFICATION_ID_OFFSET_BILLS + billId.hashCode() % 1000
        val requestCode = billId.hashCode()
        val intent = Intent(context, NotificationReceiver::class.java)
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            requestCode,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        alarmManager.cancel(pendingIntent)
        pendingIntent.cancel()
        NotificationManagerCompat.from(context).cancel(notificationId) // Cancel if already shown
        Log.d("NotificationService", "Cancelling notification and alarm for bill ID: $billId")
    }

    private fun getNotificationText(task: Task): String {
        val baseText = when {
            task.description.isNotBlank() -> "${task.title}: ${task.description}"
            else -> task.title
        }

        val timeText = task.dueDate?.let { dueDate ->
            val dateFormat = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())
            val timeFormat = SimpleDateFormat("hh:mm a", Locale.getDefault())
            "Due on ${dateFormat.format(dueDate)} at ${timeFormat.format(dueDate)}"
        } ?: ""

        return listOf(baseText, timeText).filter { it.isNotEmpty() }.joinToString(" - ")
    }

    private fun getNotificationId(task: Task): Int {
        // Use hashCode of task ID as notification ID, ensuring it's positive
        return task.id.hashCode() and 0x7fffffff
    }

    // Method to save notification to history
    private fun saveNotificationToHistory(title: String, message: String) {
        coroutineScope.launch {
            notificationHistoryManager.saveNotification(title, message)
            Log.d("NotificationService", "Saved notification to history: $title")
        }
    }

    /**
     * Shows a notification for task completion
     */
    fun showTaskCompletedNotification(task: Task) {
        if (!hasNotificationPermission) {
            Log.d("NotificationService", "Notification permission not granted")
            return
        }
        
        // Check preferences
        val preferences = getNotificationPreferences()
        if (!preferences["systemNotifications"]!!) {
            Log.d("NotificationService", "System notifications disabled, skipping completed notification for: ${task.title}")
            return
        }
        
        val title = "Task Completed"
        val message = "You've completed: ${task.title}"
        
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(context, CHANNEL_ID_TASK_COMPLETED)
            .setSmallIcon(R.drawable.ic_check_circle)
            .setContentTitle(title)
            .setContentText(message)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .build()
        
        try {
            with(NotificationManagerCompat.from(context)) {
                notify(NOTIFICATION_ID_TASK_COMPLETED, notification)
            }
            // Save to history
            saveNotificationToHistory(title, message)
        } catch (e: SecurityException) {
            Log.e("NotificationService", "SecurityException: ${e.message}")
        }
    }

    /**
     * Cancel the daily bill summary notification
     */
    fun cancelDailyBillSummary() {
        val notificationId = NOTIFICATION_ID_OFFSET_BILLS
        val requestCode = "daily_bills_summary".hashCode()
        val intent = Intent(context, NotificationReceiver::class.java)
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            requestCode,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        alarmManager.cancel(pendingIntent)
        pendingIntent.cancel()
        NotificationManagerCompat.from(context).cancel(notificationId) // Cancel if already shown
        Log.d("NotificationService", "Cancelling daily bill summary notification")
    }

    /**
     * Schedule daily summary notifications
     */
    fun scheduleDailySummary() {
        val preferences = getNotificationPreferences()
        if (!preferences["dailySummaryEnabled"]!!) {
            Log.d("NotificationService", "Daily summary disabled in preferences")
            return
        }

        val (hour, minute) = getDailySummaryTime()

        // Schedule for today if time hasn't passed, otherwise tomorrow
        val calendar = Calendar.getInstance().apply {
            set(Calendar.HOUR_OF_DAY, hour)
            set(Calendar.MINUTE, minute)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)

            // If time has passed today, schedule for tomorrow
            if (timeInMillis <= System.currentTimeMillis()) {
                add(Calendar.DAY_OF_MONTH, 1)
            }
        }

        val intent = Intent(context, NotificationReceiver::class.java).apply {
            action = "DAILY_SUMMARY"
        }

        val pendingIntent = PendingIntent.getBroadcast(
            context,
            "daily_summary".hashCode(),
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        alarmManager.setRepeating(
            AlarmManager.RTC_WAKEUP,
            calendar.timeInMillis,
            AlarmManager.INTERVAL_DAY,
            pendingIntent
        )

        Log.d("NotificationService", "Daily summary scheduled for ${calendar.time}")
    }

    /**
     * Show daily summary notification
     */
    fun showDailySummary() {
        val preferences = getNotificationPreferences()
        if (!preferences["dailySummaryEnabled"]!!) {
            Log.d("NotificationService", "Daily summary disabled in preferences")
            return
        }

        // Get today's data
        val today = Calendar.getInstance()
        val todayStart = Calendar.getInstance().apply {
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }
        val todayEnd = Calendar.getInstance().apply {
            set(Calendar.HOUR_OF_DAY, 23)
            set(Calendar.MINUTE, 59)
            set(Calendar.SECOND, 59)
            set(Calendar.MILLISECOND, 999)
        }

        // Count today's items (this would need to be implemented with actual data access)
        val taskCount = getTodayTaskCount()
        val billCount = getTodayBillCount()
        val dateCount = getTodayImportantDateCount()

        val title = "Daily Summary"
        val message = buildString {
            append("Today: ")
            if (taskCount > 0) append("$taskCount tasks")
            if (billCount > 0) {
                if (taskCount > 0) append(", ")
                append("$billCount bills due")
            }
            if (dateCount > 0) {
                if (taskCount > 0 || billCount > 0) append(", ")
                append("$dateCount important dates")
            }
            if (taskCount == 0 && billCount == 0 && dateCount == 0) {
                append("No items scheduled")
            }
        }

        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(context, CHANNEL_ID_DAILY_SUMMARY)
            .setSmallIcon(R.drawable.ic_summary)
            .setContentTitle(title)
            .setContentText(message)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .build()

        try {
            with(NotificationManagerCompat.from(context)) {
                notify(NOTIFICATION_ID_DAILY_SUMMARY, notification)
            }
            // Save to history
            saveNotificationToHistory(title, message)
        } catch (e: SecurityException) {
            Log.e("NotificationService", "SecurityException: ${e.message}")
        }
    }

    // Helper methods to get today's counts (these would need actual implementation)
    private fun getTodayTaskCount(): Int {
        // TODO: Implement actual task counting logic
        return 0
    }

    private fun getTodayBillCount(): Int {
        // TODO: Implement actual bill counting logic
        return 0
    }

    private fun getTodayImportantDateCount(): Int {
        // TODO: Implement actual important date counting logic
        return 0
    }
}