package com.taskiq.app.utils

import android.content.Context
import android.util.Log
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.taskiq.app.service.NotificationItem
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import java.text.SimpleDateFormat
import java.time.Instant
import java.util.Date
import java.util.Locale
import java.util.UUID

private val Context.dataStore by preferencesDataStore(name = "notification_history")
private val NOTIFICATIONS_KEY = stringPreferencesKey("notifications")

/**
 * Utility class to manage notification history retrieval and operations
 */
class NotificationHistoryManager(private val context: Context) {
    
    companion object {
        private const val TAG = "NotificationHistoryMgr"
    }
    
    private val gson = Gson()

    suspend fun saveNotification(title: String, message: String) {
        val notificationItem = NotificationItem(
            id = UUID.randomUUID().toString(),
            title = title,
            message = message,
            timestamp = Instant.now().toEpochMilli()
        )
        
        val currentNotifications = getAllNotifications().toMutableList()
        currentNotifications.add(0, notificationItem) // Add to the beginning
        
        // Limit to most recent 100 notifications
        val limitedNotifications = if (currentNotifications.size > 100) {
            currentNotifications.subList(0, 100)
        } else {
            currentNotifications
        }
        
        saveNotificationList(limitedNotifications)
    }
    
    suspend fun getAllNotifications(): List<NotificationItem> {
        return context.dataStore.data.map { preferences ->
            val notificationsJson = preferences[NOTIFICATIONS_KEY] ?: return@map emptyList<NotificationItem>()
            
            val type = object : TypeToken<List<NotificationItem>>() {}.type
            gson.fromJson<List<NotificationItem>>(notificationsJson, type)
        }.first()
    }
    
    suspend fun clearAllNotifications() {
        context.dataStore.edit { preferences ->
            preferences[NOTIFICATIONS_KEY] = gson.toJson(emptyList<NotificationItem>())
        }
    }
    
    suspend fun deleteNotification(notificationId: String) {
        val currentNotifications = getAllNotifications().toMutableList()
        val updatedList = currentNotifications.filter { it.id != notificationId }
        saveNotificationList(updatedList)
    }
    
    private suspend fun saveNotificationList(notifications: List<NotificationItem>) {
        context.dataStore.edit { preferences ->
            preferences[NOTIFICATIONS_KEY] = gson.toJson(notifications)
        }
    }
    
    /**
     * Format a timestamp for display
     */
    fun formatTimestamp(timestamp: Long): String {
        val formatter = SimpleDateFormat("MMM dd, yyyy hh:mm a", Locale.getDefault())
        return formatter.format(Date(timestamp))
    }
} 