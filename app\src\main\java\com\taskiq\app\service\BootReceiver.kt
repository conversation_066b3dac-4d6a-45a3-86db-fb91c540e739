package com.taskiq.app.service

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import com.taskiq.app.TaskIQApplication
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * BroadcastR<PERSON>eiver that handles device boot completed events.
 * Re-registers all pending notifications when the device is restarted.
 */
class BootReceiver : BroadcastReceiver() {
    companion object {
        private const val TAG = "BootReceiver"
    }

    override fun onReceive(context: Context, intent: Intent) {
        if (intent.action == Intent.ACTION_BOOT_COMPLETED ||
            intent.action == "android.intent.action.QUICKBOOT_POWERON" ||
            intent.action == "com.htc.intent.action.QUICKBOOT_POWERON") {
            
            Log.d(TAG, "Device boot completed, re-registering notifications")
            
            // Use a coroutine to handle the background work
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    // Initialize the notification registrar
                    val notificationRegistrar = NotificationRegistrar(context)
                    
                    // Register all pending notifications
                    notificationRegistrar.registerAllPendingNotifications()
                    
                    Log.d(TAG, "Successfully re-registered all notifications after boot")
                } catch (e: Exception) {
                    Log.e(TAG, "Error re-registering notifications after boot: ${e.message}", e)
                }
            }
        }
    }
} 