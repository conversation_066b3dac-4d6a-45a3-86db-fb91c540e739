package com.taskiq.app.service

import android.app.NotificationManager
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.PowerManager
import android.util.Log
import androidx.core.app.NotificationCompat
import com.taskiq.app.MainActivity
import com.taskiq.app.R
import com.taskiq.app.TaskIQApplication
import com.taskiq.app.service.NotificationService
import com.taskiq.app.utils.NotificationHistoryManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.util.*
import java.text.SimpleDateFormat
import java.util.Locale

class NotificationReceiver : BroadcastReceiver() {

    companion object {
        private const val TAG = "NotificationReceiver"
        const val EXTRA_NOTIFICATION_ID = "notification_id"
        const val EXTRA_CHANNEL_ID = "channel_id"
        const val EXTRA_TITLE = "title"
        const val EXTRA_CONTENT_TEXT = "content_text"
        const val EXTRA_BIG_TEXT = "big_text" // Optional for BigTextStyle
        const val EXTRA_REQUEST_CODE = "request_code" // Unique request code for PendingIntent
        private const val WAKE_LOCK_TIMEOUT = 10 * 60 * 1000L // 10 minutes
    }

    private val coroutineScope = CoroutineScope(Dispatchers.IO)

    override fun onReceive(context: Context, intent: Intent) {
        Log.d(TAG, "Received alarm intent: ${intent.action}")
        
        // Acquire a wake lock to ensure the device stays awake while processing
        val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
        val wakeLock = powerManager.newWakeLock(
            PowerManager.PARTIAL_WAKE_LOCK,
            "TaskReminder:NotificationWakeLock"
        )
        
        try {
            // Acquire the wake lock with a timeout
            wakeLock.acquire(WAKE_LOCK_TIMEOUT)
            
            // Handle boot completed events
            if (intent.action == Intent.ACTION_BOOT_COMPLETED ||
                intent.action == "android.intent.action.QUICKBOOT_POWERON" ||
                intent.action == "com.htc.intent.action.QUICKBOOT_POWERON") {

                // Delegate to BootReceiver
                val bootIntent = Intent(context, BootReceiver::class.java).apply {
                    action = intent.action
                }
                context.sendBroadcast(bootIntent)

                return
            }

            // Handle daily summary action
            if (intent.action == "DAILY_SUMMARY") {
                val notificationService = TaskIQApplication.getNotificationService()
                notificationService.showDailySummary()
                return
            }
            
            // Check notification permission on Android 13+ (API 33+)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                val notificationPermission = context.checkSelfPermission(android.Manifest.permission.POST_NOTIFICATIONS)
                if (notificationPermission != android.content.pm.PackageManager.PERMISSION_GRANTED) {
                    Log.w(TAG, "Notification permission not granted, can't show notification")
                    return
                }
            }
            
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

            val notificationId = intent.getIntExtra(EXTRA_NOTIFICATION_ID, 0)
            val channelId = intent.getStringExtra(EXTRA_CHANNEL_ID) ?: NotificationService.CHANNEL_ID_TASKS
            val title = intent.getStringExtra(EXTRA_TITLE) ?: "Reminder"
            val contentText = intent.getStringExtra(EXTRA_CONTENT_TEXT) ?: ""
            val bigText = intent.getStringExtra(EXTRA_BIG_TEXT) // Can be null
            val requestCode = intent.getIntExtra(EXTRA_REQUEST_CODE, notificationId)

            Log.d(TAG, "Processing notification - ID: $notificationId, Title: $title, Channel: $channelId")

            // Create an intent to launch the app when the notification is tapped
            val launchIntent = Intent(context, MainActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                // Optionally add extras to navigate to a specific screen
                // putExtra("NAVIGATE_TO", "tasks") // Example
            }

            val pendingIntent = PendingIntent.getActivity(
                context,
                requestCode,
                launchIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            // Ensure the notification icon exists
            val iconResId = R.drawable.ic_notification
            Log.d(TAG, "Using notification icon: $iconResId")

            val notificationBuilder = NotificationCompat.Builder(context, channelId)
                .setSmallIcon(iconResId)
                .setContentTitle(title)
                .setContentText(contentText)
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setContentIntent(pendingIntent)
                .setAutoCancel(true)
                .setVibrate(longArrayOf(0, 250, 250, 250)) // Add vibration
                .setDefaults(NotificationCompat.DEFAULT_SOUND) // Add sound

            // Apply BigTextStyle if bigText is provided
            if (!bigText.isNullOrEmpty()) {
                notificationBuilder.setStyle(NotificationCompat.BigTextStyle().bigText(bigText))
            }

            // Ensure notification channels exist
            val notificationService = TaskIQApplication.getNotificationService()
            notificationService.createNotificationChannels()

            // Build and show the notification
            notificationManager.notify(notificationId, notificationBuilder.build())
            
            // Save this notification to history using NotificationHistoryManager
            saveNotificationToHistory(context, title, contentText)
            
            Log.d(TAG, "Notification shown successfully - ID: $notificationId, Title: $title")
            
        } catch (e: SecurityException) {
            Log.e(TAG, "SecurityException: Missing POST_NOTIFICATIONS permission?", e)
        } catch (e: Exception) {
            Log.e(TAG, "Error showing notification: ${e.message}", e)
        } finally {
            // Release the wake lock
            if (wakeLock.isHeld) {
                wakeLock.release()
                Log.d(TAG, "Wake lock released")
            }
        }
    }
    
    private fun saveNotificationToHistory(context: Context, title: String, message: String) {
        coroutineScope.launch {
            try {
                // Save using the NotificationHistoryManager for internal tracking
                val historyManager = NotificationHistoryManager(context)
                historyManager.saveNotification(title, message)
                
                // Also save in the format expected by the NotificationScreen
                val sharedPrefs = context.getSharedPreferences("notifications_history", Context.MODE_PRIVATE)
                val notificationsJson = sharedPrefs.getString("notifications", "[]")
                
                val gson = Gson()
                val type = object : TypeToken<MutableList<com.taskiq.app.ui.screens.NotificationItem>>() {}.type
                val notifications: MutableList<com.taskiq.app.ui.screens.NotificationItem> = gson.fromJson(notificationsJson, type)
                
                // Create a notification item with the appropriate format for the UI
                // Determine the notification type based on the channel ID and title
                val notificationType = when {
                    title.contains("Task") -> com.taskiq.app.ui.screens.NotificationType.TASK
                    title.contains("Bill") -> com.taskiq.app.ui.screens.NotificationType.BILL
                    title.contains("Date") || title.contains("Important Date") -> com.taskiq.app.ui.screens.NotificationType.DATE
                    title.contains("Daily Summary") -> com.taskiq.app.ui.screens.NotificationType.TASK // Treat summary as task type
                    else -> com.taskiq.app.ui.screens.NotificationType.TASK // Default
                }
                
                val priority = title.contains("HIGH") || title.contains("high priority")
                
                val notification = com.taskiq.app.ui.screens.NotificationItem(
                    id = UUID.randomUUID().toString(),
                    title = title,
                    description = message,
                    type = notificationType,
                    date = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault()).format(Date()),
                    dateForSorting = Date(),
                    priority = priority,
                    timestamp = System.currentTimeMillis()
                )
                
                // Add the new notification
                notifications.add(notification)
                
                // Keep only the most recent MAX_NOTIFICATIONS (30)
                val recentNotifications = notifications.sortedByDescending { it.timestamp }.take(30)
                
                // Save back to SharedPreferences
                val editor = sharedPrefs.edit()
                editor.putString("notifications", gson.toJson(recentNotifications))
                editor.apply()
                
                Log.d(TAG, "Saved notification to UI history: $title")
            } catch (e: Exception) {
                Log.e(TAG, "Error saving notification to history: ${e.message}", e)
            }
        }
    }
}
