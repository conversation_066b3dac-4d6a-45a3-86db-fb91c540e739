package com.taskiq.app.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState

import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState

import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.taskiq.app.ui.theme.*

import com.taskiq.app.model.Bill
import com.taskiq.app.model.BillStatus
import com.taskiq.app.model.Task

import com.taskiq.app.model.TaskFrequency
import com.taskiq.app.ui.components.*
import com.taskiq.app.ui.theme.rootContainerModifier
import com.taskiq.app.ui.theme.contentBelowHeaderModifier
import com.taskiq.app.viewmodel.TaskViewModel
import java.time.LocalDate
import java.time.ZoneId
import java.util.*


@Composable
fun DashboardScreen(
    onLogout: () -> Unit,
    taskViewModel: TaskViewModel,
    onNavigateToTasks: () -> Unit,
    onNavigateToDates: () -> Unit,
    onNavigateToBills: () -> Unit = {}, // Optional parameter with default value
    onScrollStateChanged: (Boolean) -> Unit = {} // Callback for scroll state changes
) {
    // Force load of important dates when the screen is first composed
    LaunchedEffect(Unit) {
        taskViewModel.loadImportantDates()
        taskViewModel.forceSaveAllData() // Force a save to ensure persistence
    }
    
    var isLoading by remember { mutableStateOf(false) }
    
    // Update isLoading from taskViewModel
    LaunchedEffect(taskViewModel.isLoading) {
        isLoading = taskViewModel.isLoading
    }
    
    val tasks = taskViewModel.tasks
    val dates = taskViewModel.importantDates
    val bills by taskViewModel.bills.collectAsState()

    // Scroll state for hide-on-scroll behavior
    val listState = rememberLazyListState()
    var previousScrollOffset by remember { mutableStateOf(0) }

    val isScrollingDown by remember {
        derivedStateOf {
            val currentOffset = listState.firstVisibleItemScrollOffset
            val isScrollingDown = currentOffset > previousScrollOffset && currentOffset > 100
            previousScrollOffset = currentOffset
            isScrollingDown
        }
    }

    // Update scroll state - hide when scrolling down, show only when at top
    LaunchedEffect(listState.firstVisibleItemScrollOffset) {
        val isAtTop = listState.firstVisibleItemScrollOffset <= 50
        onScrollStateChanged(!isAtTop)
    }
    
    Box(
        modifier = rootContainerModifier
    ) {
        if (isLoading) {
            TaskLoadingState()
        } else if (tasks.isEmpty() && dates.isEmpty() && bills.isEmpty()) {
            TaskEmptyState(
                message = "Your dashboard is empty. Add tasks, bills, or important dates to see them here.",
                actionLabel = "Add Task",
                onAction = onNavigateToTasks
            )
        } else {
            // Group tasks
            val today = LocalDate.now()
            val overdueTasks = tasks.filter { task ->
                !task.isCompleted && task.dueDate?.let { dueDate ->
                    dueDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().isBefore(today)
                } ?: false
            }
            
            // Get all tasks due today including one-time and recurring tasks
            val tasksDueToday = tasks.filter { task ->
                !task.isCompleted && task.dueDate?.let { dueDate ->
                    dueDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate() == today
                } ?: false
            }
            
            // Add daily tasks to today's tasks
            val dailyTasks = tasks.filter { 
                !it.isCompleted && it.frequency == TaskFrequency.DAILY 
            }
            
            // Get important dates due today
            val datesDueToday = dates.filter { date ->
                date.dueDate?.let { dueDate ->
                    dueDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate() == today
                } ?: false
            }
            
            // Only include dates with plans in Today's Tasks section
            val datesWithPlansDueToday = datesDueToday.filter { date -> 
                date.subtasks.isNotEmpty() && !date.isCompleted
            }
            
            // Find the corresponding tasks for these dates instead of creating copies
            val tasksForDatesWithPlans = datesWithPlansDueToday.mapNotNull { date ->
                val exactTitle = "Reminder for ${date.title}"
                tasks.firstOrNull { task ->
                    !task.isCompleted &&
                    task.title.equals(exactTitle, ignoreCase = true) && 
                    (task.dueDate?.let { dueDate ->
                        date.dueDate?.let { dateDate ->
                            dueDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate() ==
                            dateDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
                        }
                    } ?: false)
                }
            }
            
            // Combine one-time and recurring tasks due today with daily tasks and tasks for dates with plans
            val allTasksDueToday = (tasksDueToday + dailyTasks + tasksForDatesWithPlans).distinctBy { it.id }
            
            // Get all upcoming dates for the next 7 days
            val sevenDaysFromNow = today.plusDays(7)
            val upcomingImportantDates = dates.filter { date ->
                !date.isCompleted && date.dueDate?.let { dueDate ->
                    val dueDateLocalDate = dueDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
                    dueDateLocalDate.isAfter(today) && dueDateLocalDate.isBefore(sevenDaysFromNow.plusDays(1)) || 
                    dueDateLocalDate.isEqual(today)
                } ?: false
            }
            
            // Get all upcoming important dates sorted by date, excluding completed tasks
            val upcomingDates = upcomingImportantDates.filter { !it.isCompleted }.sortedBy { it.dueDate }
            
            // Bills due today
            val todayDate = LocalDate.now()
            val billsDueToday = bills.filter { 
                it.status == BillStatus.PENDING && it.dueDate.equals(todayDate)
            }
            
            // Overdue bills
            val overdueBills = bills.filter {
                it.status == BillStatus.PENDING && it.dueDate.isBefore(todayDate)
            }
            
            LazyColumn(
                state = listState,
                modifier = uniformContentBelowHeaderModifier(),
                contentPadding = uniformContentPadding(),
                verticalArrangement = Arrangement.spacedBy(uniformItemSpacing())
            ) {
                // Initial spacing - uniform across all screens
                item {
                    Spacer(modifier = Modifier.height(uniformInitialPadding()))
                }
                
                // Overdue Section
                if (overdueTasks.isNotEmpty() || overdueBills.isNotEmpty()) {
                    val overdueCount = overdueTasks.size + overdueBills.size
                    
                    item {
                        TaskSectionHeader(
                            title = "Overdue",
                            count = overdueCount
                            // Removed "View All" button
                        )
                    }
                    
                    // Show overdue bills first
                    items(overdueBills) { bill ->
                        DashboardBillItem(
                            bill = bill,
                            onMarkAsPaid = { taskViewModel.markBillAsPaid(it) }
                        )
                    }
                    
                    // Then show overdue tasks
                    items(overdueTasks) { task ->
                        TaskItem(
                            task = task,
                            onTaskComplete = { taskViewModel.toggleTaskCompletion(task.id) },
                            onTaskDelete = null,
                            onTaskEdit = null,
                            onSubtaskToggle = { taskId, subtaskId -> taskViewModel.toggleSubtaskCompletion(taskId, subtaskId) },
                            onAddSubtask = null,
                            isDashboard = true,
                            disableExpand = task.subtasks.isEmpty() && !task.title.startsWith("Reminder for ", ignoreCase = true)
                        )
                    }
                }
                
                // Today's Tasks Section
                if (allTasksDueToday.isNotEmpty()) {
                    item {
                        TaskSectionHeader(
                            title = "Today's Tasks",
                            count = allTasksDueToday.size
                            // Removed "View All" button
                        )
                    }
                    items(allTasksDueToday.filter { !it.isCompleted }) { task ->
                        // Check if this is a date item (has "Reminder for" prefix)
                        val isDateItem = task.title.startsWith("Reminder for")
                        TaskItem(
                            task = task,
                            onTaskComplete = { taskViewModel.toggleTaskCompletion(task.id) },
                            onTaskDelete = null,
                            onTaskEdit = null,
                            onSubtaskToggle = { taskId, subtaskId -> taskViewModel.toggleSubtaskCompletion(taskId, subtaskId) },
                            onAddSubtask = null,
                            isDate = isDateItem,
                            isDashboard = true,
                            disableExpand = task.subtasks.isEmpty() // Allow date items with subtasks to expand
                        )
                    }
                }
                
                // Bills Due Today Section
                if (billsDueToday.isNotEmpty()) {
                    item {
                        TaskSectionHeader(
                            title = "Bills Due Today",
                            count = billsDueToday.size
                            // Removed "View All" button
                        )
                    }
                    items(billsDueToday) { bill ->
                        DashboardBillItem(
                            bill = bill,
                            onMarkAsPaid = { taskViewModel.markBillAsPaid(it) }
                        )
                    }
                }
                
                // Upcoming Dates Section
                if (upcomingDates.isNotEmpty()) {
                    item {
                        TaskSectionHeader(
                            title = "Upcoming Important Dates",
                            count = upcomingDates.size,
                            actionButtonText = "View All",
                            onActionButtonClick = onNavigateToDates
                        )
                    }
                    
                    items(upcomingDates) { date ->
                        TaskItem(
                            task = date,
                            onTaskComplete = { /* Do nothing */ },
                            onTaskDelete = null,
                            onTaskEdit = null,
                            onSubtaskToggle = { _, _ -> },
                            onAddSubtask = null,
                            isDashboard = true,
                            isDate = true,
                            disableExpand = true
                        )
                    }
                }
                
                item {
                    Spacer(modifier = Modifier.height(uniformBottomSpacing())) // For bottom padding
                }
            }
        }
    }
}