package com.taskiq.app.ui.navigation

import android.app.Application
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.taskiq.app.ui.screens.LoginScreen
import com.taskiq.app.ui.screens.MainScreen
import com.taskiq.app.ui.screens.MyProfileScreen
import com.taskiq.app.ui.screens.RegisterScreen
import com.taskiq.app.ui.screens.ForgotPasswordScreen
import com.taskiq.app.ui.screens.NotificationScreen
import com.taskiq.app.ui.screens.NotificationSettingsScreen
import com.taskiq.app.ui.screens.SettingsScreen

import com.taskiq.app.ui.screens.DataBackupScreen
import com.taskiq.app.ui.screens.GmailAuthScreen
import com.taskiq.app.viewmodel.AuthViewModel
import com.taskiq.app.viewmodel.NotificationSettingsViewModel
import com.taskiq.app.viewmodel.BackupViewModel
import com.taskiq.app.viewmodel.GmailAuthViewModel
import com.taskiq.app.viewmodel.TaskViewModel

object Routes {
    const val LOGIN = "login"
    const val REGISTER = "register"
    const val FORGOT_PASSWORD = "forgot_password"
    const val MAIN = "main"
    const val MY_PROFILE = "my_profile"
    const val SETTINGS = "settings"
    const val NOTIFICATIONS = "notifications"
    const val NOTIFICATION_SETTINGS = "notification_settings"
    const val DATA_BACKUP = "data_backup"
    const val GMAIL_AUTH = "gmail_auth"
    const val ABOUT_US = "about_us"
    const val PRIVACY_POLICY = "privacy_policy"
    const val TERMS_OF_USE = "terms_of_use"
}

@Composable
fun AppNavigation(authViewModel: AuthViewModel) {
    val navController = rememberNavController()
    val context = LocalContext.current
    val user by authViewModel.user.collectAsState()

    // Determine start destination based on authentication status
    val startDestination = if (user != null) Routes.MAIN else Routes.LOGIN

    NavHost(
        navController = navController,
        startDestination = startDestination
    ) {
        
        composable(Routes.LOGIN) {
            LoginScreen(
                navController = navController,
                viewModel = authViewModel
            )
        }
        
        composable(Routes.REGISTER) {
            RegisterScreen(
                navController = navController,
                viewModel = authViewModel
            )
        }

        composable(Routes.FORGOT_PASSWORD) {
            ForgotPasswordScreen(
                navController = navController,
                viewModel = authViewModel
            )
        }
        
        composable(Routes.MAIN) {
            MainScreen(
                authViewModel = authViewModel,
                onLogout = {
                    authViewModel.logout()
                    navigateToLogin(navController)
                },
                onNavigateToProfile = {
                    navController.navigate(Routes.MY_PROFILE)
                },
                onNavigateToSettings = {
                    navController.navigate(Routes.SETTINGS)
                }
            )
        }
        
        composable(Routes.MY_PROFILE) {
            MyProfileScreen(
                navController = navController,
                viewModel = authViewModel
            )
        }
        
        composable(Routes.SETTINGS) {
            SettingsScreen(
                navController = navController,
                viewModel = authViewModel,
                onDeleteAccount = {
                    authViewModel.deleteAccount()
                    navigateToLogin(navController)
                }
            )
        }
        
        composable(Routes.NOTIFICATIONS) {
            val taskViewModel: TaskViewModel = viewModel(
                factory = ViewModelProvider.AndroidViewModelFactory(context.applicationContext as Application)
            )
            NotificationScreen(
                navController = navController,
                taskViewModel = taskViewModel
            )
        }
        
        composable(Routes.NOTIFICATION_SETTINGS) {
            val context = LocalContext.current
            val notificationSettingsViewModel: NotificationSettingsViewModel = viewModel(
                factory = ViewModelProvider.AndroidViewModelFactory(context.applicationContext as Application)
            )
            NotificationSettingsScreen(
                navController = navController,
                viewModel = notificationSettingsViewModel
            )
        }
        
        composable(Routes.DATA_BACKUP) {
            val context = LocalContext.current
            android.util.Log.d("DATABACKUP", "AppNavigation - DATA_BACKUP route triggered with context: $context")
            
            val backupViewModel: BackupViewModel = viewModel(
                factory = ViewModelProvider.AndroidViewModelFactory(context.applicationContext as Application)
            )
            android.util.Log.d("DATABACKUP", "AppNavigation - BackupViewModel created: $backupViewModel")
            
            DataBackupScreen(
                viewModel = backupViewModel,
                onNavigateBack = { 
                    android.util.Log.d("DATABACKUP", "AppNavigation - Navigate back requested")
                    navController.popBackStack() 
                },
                navController = navController
            )
            android.util.Log.d("DATABACKUP", "AppNavigation - DataBackupScreen composable rendered")
        }
        
        composable(Routes.GMAIL_AUTH) {
            val context = LocalContext.current
            val gmailAuthViewModel: GmailAuthViewModel = viewModel(
                factory = ViewModelProvider.AndroidViewModelFactory(context.applicationContext as Application)
            )
            val taskViewModel: TaskViewModel = viewModel(
                factory = ViewModelProvider.AndroidViewModelFactory(context.applicationContext as Application)
            )

            GmailAuthScreen(
                navController = navController,
                viewModel = gmailAuthViewModel,
                taskViewModel = taskViewModel
            )
        }


    }
}

private fun navigateToLogin(navController: NavHostController) {
    navController.navigate(Routes.LOGIN) {
        popUpTo(Routes.MAIN) { inclusive = true }
    }
}

private fun navigateToRegister(navController: NavHostController) {
    navController.navigate(Routes.REGISTER)
}

private fun navigateToMain(navController: NavHostController) {
    navController.navigate(Routes.MAIN) {
        popUpTo(Routes.LOGIN) { inclusive = true }
    }
}