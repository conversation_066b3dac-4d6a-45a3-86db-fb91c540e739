package com.taskiq.app.viewmodel

/**
 * Represents the different states of a backup operation
 */
sealed class BackupState {
    /**
     * Initial state, no backup operation in progress
     */
    object Idle : BackupState()
    
    /**
     * Backup operation is in progress
     */
    object InProgress : BackupState()
    
    /**
     * Backup operation completed successfully
     */
    object Success : BackupState()
    
    /**
     * Backup operation failed with an error
     */
    data class Error(val message: String) : BackupState()
} 