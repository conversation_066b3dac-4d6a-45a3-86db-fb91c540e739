package com.taskiq.app.ui.screens

import android.Manifest
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.derivedStateOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import java.text.SimpleDateFormat
import java.util.*
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

import com.taskiq.app.model.Task
import com.taskiq.app.service.GoogleCalendarService
import com.taskiq.app.ui.components.DateItem
import com.taskiq.app.ui.components.TaskEmptyState
import com.taskiq.app.ui.components.TaskLoadingState
import com.taskiq.app.ui.screens.AddImportantDatesScreen
import com.taskiq.app.ui.navigation.Routes
import com.taskiq.app.viewmodel.TaskViewModel

import com.taskiq.app.ui.theme.rootContainerModifier
import com.taskiq.app.ui.theme.uniformContentBelowHeaderModifier
import com.taskiq.app.ui.theme.uniformContentPadding
import com.taskiq.app.ui.theme.uniformHorizontalPadding
import com.taskiq.app.ui.theme.uniformVerticalPadding
import com.taskiq.app.ui.theme.uniformInitialPadding
import com.taskiq.app.ui.theme.uniformSectionSpacing
import com.taskiq.app.ui.theme.uniformItemSpacing
import com.taskiq.app.ui.theme.uniformSmallSpacing
import com.taskiq.app.ui.theme.uniformEmptyStatePadding
import com.taskiq.app.ui.theme.uniformBottomSpacing
import com.taskiq.app.ui.theme.responsiveSpacing
import com.taskiq.app.ui.theme.ProfessionalBlue
import com.taskiq.app.ui.theme.ProfessionalWhite
import com.taskiq.app.ui.theme.cardBackgroundColor
import com.taskiq.app.ui.theme.cardTextColor
import com.taskiq.app.ui.theme.cardSecondaryTextColor
import com.taskiq.app.ui.theme.sdp

enum class DateSortOption {
    DATE_ASC, DATE_DESC, TITLE_ASC, TITLE_DESC
}

enum class DateFilterOption {
    ALL, UPCOMING, PAST
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DatesScreen(
    taskViewModel: TaskViewModel,
    onBackClick: () -> Unit,
    onShowAddDateScreen: (Boolean) -> Unit,
    onScrollStateChanged: (Boolean) -> Unit = {} // Callback for scroll state changes
) {
    // Force load of important dates when the screen is first composed
    LaunchedEffect(Unit) {
        taskViewModel.loadImportantDates()
        taskViewModel.forceSaveAllData() // Force a save to ensure persistence
    }
    
    val dates = taskViewModel.importantDates
    var isLoading by remember { mutableStateOf(false) }

    // Scroll state for hide-on-scroll behavior
    val listState = rememberLazyListState()
    var previousScrollOffset by remember { mutableStateOf(0) }

    val isScrollingDown by remember {
        derivedStateOf {
            val currentOffset = listState.firstVisibleItemScrollOffset
            val isScrollingDown = currentOffset > previousScrollOffset && currentOffset > 100
            previousScrollOffset = currentOffset
            isScrollingDown
        }
    }

    // Update scroll state - hide when scrolling down, show only when at top
    LaunchedEffect(listState.firstVisibleItemScrollOffset) {
        val isAtTop = listState.firstVisibleItemScrollOffset <= 50
        onScrollStateChanged(!isAtTop)
    }

    // Update isLoading from taskViewModel
    LaunchedEffect(taskViewModel.isLoading) {
        isLoading = taskViewModel.isLoading
    }
    
    // Use uniform padding system for consistency across all screens
    
    var showAddDateScreen by remember { mutableStateOf(false) }
    var dateToEdit by remember { mutableStateOf<Task?>(null) }
    var isGoogleCalendarSyncEnabled by remember { mutableStateOf(taskViewModel.isGoogleCalendarSyncEnabled) }
    var showSortOptions by remember { mutableStateOf(false) }
    var showFilterOptions by remember { mutableStateOf(false) }
    var sortOption by remember { mutableStateOf(DateSortOption.DATE_ASC) }
    var filterOption by remember { mutableStateOf(DateFilterOption.ALL) }

    val context = LocalContext.current

    // Calendar permission launcher
    val calendarPermissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            // Permission granted, enable sync and start syncing
            taskViewModel.updateGoogleCalendarSync(true)
            isGoogleCalendarSyncEnabled = true
        }
    }
    
    // Update parent when Add/Edit screen visibility changes
    LaunchedEffect(showAddDateScreen) {
        onShowAddDateScreen(showAddDateScreen)
    }
    
    // Group dates by month
    val sortedDates = when (sortOption) {
        DateSortOption.DATE_ASC -> dates.sortedBy { it.dueDate }
        DateSortOption.DATE_DESC -> dates.sortedByDescending { it.dueDate }
        DateSortOption.TITLE_ASC -> dates.sortedBy { it.title }
        DateSortOption.TITLE_DESC -> dates.sortedByDescending { it.title }
    }

    val filteredDates = when (filterOption) {
        DateFilterOption.ALL -> sortedDates
        DateFilterOption.UPCOMING -> sortedDates.filter { date ->
            date.dueDate?.let { dueDate ->
                dueDate.after(Date())
            } ?: false
        }
        DateFilterOption.PAST -> sortedDates.filter { date ->
            date.dueDate?.let { dueDate ->
                dueDate.before(Date())
            } ?: false
        }
    }
    
    // Separate dates into upcoming and past
    val today = Date()
    val upcomingDates = filteredDates.filter { date ->
        date.dueDate?.let { dueDate ->
            dueDate.after(today) || isSameDay(dueDate, today)
        } ?: false
    }.take(100) // Limit upcoming dates to 100

    val pastDates = filteredDates.filter { date ->
        date.dueDate?.let { dueDate ->
            dueDate.before(today) && !isSameDay(dueDate, today)
        } ?: false
    }.sortedByDescending { it.dueDate }.take(50) // Limit past dates to last 50, sorted by most recent first
    
    Box(modifier = rootContainerModifier) {
        Scaffold(
            floatingActionButton = {
                FloatingActionButton(
                    onClick = { showAddDateScreen = true },
                    contentColor = ProfessionalWhite,
                    containerColor = ProfessionalBlue,
                    modifier = Modifier.size(50.dp)
                ) {
                    Icon(Icons.Default.Add, "Add Date")
                }
            }
        ) { paddingValues ->
            Column(
                modifier = Modifier.fillMaxSize()
            ) {
                if (isLoading) {
                    TaskLoadingState()
                } else if (dates.isEmpty() && !showAddDateScreen) {
                    TaskEmptyState(
                        message = "You don't have any important dates yet. Add a date or sync important personal dates from your device calendar.",
                        actionLabel = "Add Date",
                        onAction = { showAddDateScreen = true }
                    )
                } else {
                    LazyColumn(
                        state = listState,
                        modifier = uniformContentBelowHeaderModifier(),
                        contentPadding = uniformContentPadding(),
                        verticalArrangement = Arrangement.spacedBy(uniformItemSpacing())
                    ) {
                        // Initial spacing - uniform across all screens
                        item {
                            Spacer(modifier = Modifier.height(uniformInitialPadding()))
                        }

                        // Google Calendar Sync Section
                        item {
                            Card(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(horizontal = uniformHorizontalPadding(), vertical = uniformSectionSpacing()),
                                colors = CardDefaults.cardColors(
                                    containerColor = cardBackgroundColor()
                                )
                            ) {
                                Column(
                                    modifier = Modifier.padding(uniformHorizontalPadding())
                                ) {
                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        verticalAlignment = Alignment.CenterVertically,
                                        horizontalArrangement = Arrangement.SpaceBetween
                                    ) {
                                        Column {
                                            Text(
                                                text = "Google Calendar Sync",
                                                style = MaterialTheme.typography.titleMedium,
                                                fontWeight = FontWeight.Bold,
                                                color = cardTextColor()
                                            )
                                            Text(
                                                text = if (taskViewModel.isGoogleCalendarSyncEnabled) {
                                                    "Auto-syncing important dates from your device calendar"
                                                } else {
                                                    "Sync important personal dates from your device calendar"
                                                },
                                                style = MaterialTheme.typography.bodySmall,
                                                color = cardSecondaryTextColor()
                                            )
                                        }
                                        
                                        Box(
                                            modifier = Modifier.padding(end = uniformHorizontalPadding())
                                        ) {
                                            Switch(
                                                checked = taskViewModel.isGoogleCalendarSyncEnabled,
                                                onCheckedChange = { isEnabled ->
                                                    if (isEnabled) {
                                                        // Check if we have calendar permission
                                                        val calendarService = GoogleCalendarService(context)
                                                        if (calendarService.hasCalendarPermission()) {
                                                            // Permission already granted, enable sync
                                                            taskViewModel.updateGoogleCalendarSync(isEnabled)
                                                            isGoogleCalendarSyncEnabled = isEnabled
                                                        } else {
                                                            // Request calendar permission
                                                            calendarPermissionLauncher.launch(Manifest.permission.READ_CALENDAR)
                                                        }
                                                    } else {
                                                        // Disable sync
                                                        taskViewModel.updateGoogleCalendarSync(isEnabled)
                                                        isGoogleCalendarSyncEnabled = isEnabled
                                                    }
                                                },
                                                modifier = Modifier.scale(0.75f)
                                            )
                                        }
                                    }
                                    
                                    Spacer(modifier = Modifier.height(uniformSmallSpacing()))
                                    
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = if (taskViewModel.lastSyncTime != null) 
                                                "Last sync: ${SimpleDateFormat("MMM d, yyyy HH:mm", Locale.getDefault()).format(taskViewModel.lastSyncTime!!)}" 
                                            else 
                                                "Not synced yet",
                                            style = MaterialTheme.typography.bodyMedium,
                                            color = cardSecondaryTextColor()
                                        )
                                        
                                        Spacer(modifier = Modifier.weight(1f))
                                        
                                        IconButton(
                                            onClick = {
                                                if (isGoogleCalendarSyncEnabled) {
                                                    val calendarService = GoogleCalendarService(context)
                                                    if (calendarService.hasCalendarPermission()) {
                                                        // Permission granted, sync directly
                                                        taskViewModel.syncWithGoogleCalendar()
                                                    } else {
                                                        // Request calendar permission
                                                        calendarPermissionLauncher.launch(Manifest.permission.READ_CALENDAR)
                                                    }
                                                }
                                            },
                                            enabled = isGoogleCalendarSyncEnabled && !taskViewModel.isCalendarSyncing
                                        ) {
                                            Icon(
                                                imageVector = Icons.Default.Refresh,
                                                contentDescription = "Sync personal important dates from your device calendar"
                                            )
                                        }
                                    }
                                    
                                    if (taskViewModel.isCalendarSyncing) {
                                        Spacer(modifier = Modifier.height(uniformSmallSpacing()))
                                        Row(
                                            verticalAlignment = Alignment.CenterVertically,
                                            horizontalArrangement = Arrangement.spacedBy(uniformSmallSpacing())
                                        ) {
                                            CircularProgressIndicator(
                                                modifier = Modifier.size(16.sdp()),
                                                strokeWidth = 2.sdp()
                                            )
                                            Text(
                                                text = "Syncing upcoming 30 days from your device calendar...",
                                                style = MaterialTheme.typography.bodyMedium,
                                                color = cardTextColor()
                                            )
                                        }
                                    }
                                }
                            }
                        }
                        
                        // Upcoming Dates Section
                        if (upcomingDates.isNotEmpty()) {

                            item {
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = uniformSectionSpacing()),
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Text(
                                        text = "Upcoming Dates (${upcomingDates.size})",
                                        style = MaterialTheme.typography.titleMedium,
                                        fontWeight = FontWeight.Bold
                                    )
                                    
                                    Row {
                                        // Sort dropdown
                                        Box {
                                            TextButton(
                                                onClick = { showSortOptions = true },
                                                shape = MaterialTheme.shapes.medium,
                                                modifier = Modifier.height(32.sdp()),
                                                contentPadding = PaddingValues(horizontal = uniformHorizontalPadding(), vertical = uniformSectionSpacing())
                                            ) {
                                                Text(
                                                    "Sort by",
                                                    style = MaterialTheme.typography.bodyMedium
                                                )
                                                Icon(
                                                    imageVector = Icons.Default.ArrowDropDown,
                                                    contentDescription = null,
                                                    modifier = Modifier
                                                        .padding(start = uniformSmallSpacing())
                                                        .size(16.sdp())
                                                )
                                            }
                                            
                                            DropdownMenu(
                                                expanded = showSortOptions,
                                                onDismissRequest = { showSortOptions = false }
                                            ) {
                                                DropdownMenuItem(
                                                    text = { Text("Date (Ascending)") },
                                                    onClick = { 
                                                        sortOption = DateSortOption.DATE_ASC
                                                        showSortOptions = false
                                                    }
                                                )
                                                DropdownMenuItem(
                                                    text = { Text("Date (Descending)") },
                                                    onClick = { 
                                                        sortOption = DateSortOption.DATE_DESC
                                                        showSortOptions = false
                                                    }
                                                )
                                                DropdownMenuItem(
                                                    text = { Text("Title (A-Z)") },
                                                    onClick = { 
                                                        sortOption = DateSortOption.TITLE_ASC
                                                        showSortOptions = false
                                                    }
                                                )
                                                DropdownMenuItem(
                                                    text = { Text("Title (Z-A)") },
                                                    onClick = { 
                                                        sortOption = DateSortOption.TITLE_DESC
                                                        showSortOptions = false
                                                    }
                                                )
                                            }
                                        }
                                    }
                                }
                            }
                            
                            // Display upcoming dates as a flat list
                            items(upcomingDates) { date ->
                                DateItem(
                                    date = date,
                                    onDateDelete = { taskViewModel.deleteImportantDate(date.id) },
                                    onDateEdit = { 
                                        dateToEdit = it
                                        showAddDateScreen = true 
                                    },
                                    onAddPlan = { taskId, plan -> taskViewModel.addSubtask(taskId, plan) },
                                    onPlanToggle = { taskId, planId -> taskViewModel.toggleSubtaskCompletion(taskId, planId) }
                                )
                            }
                            
                            item { Spacer(modifier = Modifier.height(uniformSectionSpacing())) }
                        }
                        
                        // Past Dates Section
                        if (pastDates.isNotEmpty()) {

                            item {
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = uniformSectionSpacing()),
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Text(
                                        text = "Past Dates (${pastDates.size})",
                                        style = MaterialTheme.typography.titleMedium,
                                        fontWeight = FontWeight.Bold
                                    )
                                }
                            }
                            
                            // Display past dates as a flat list
                            items(pastDates) { date ->
                                DateItem(
                                    date = date,
                                    onDateDelete = { taskViewModel.deleteImportantDate(date.id) },
                                    onDateEdit = { 
                                        dateToEdit = it
                                        showAddDateScreen = true 
                                    },
                                    onAddPlan = { taskId, plan -> taskViewModel.addSubtask(taskId, plan) },
                                    onPlanToggle = { taskId, planId -> taskViewModel.toggleSubtaskCompletion(taskId, planId) },
                                    isPastDate = true
                                )
                            }
                        }
                        
                        // Empty state when filters result in no dates
                        if (filteredDates.isEmpty()) {
                            item {
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = uniformEmptyStatePadding()), // Standard empty state padding
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = "No dates match your filters",
                                        style = MaterialTheme.typography.bodyLarge
                                    )
                                }
                            }
                        }
                        
                        // Bottom spacing for FAB
                        item {
                            Spacer(modifier = Modifier.height(uniformBottomSpacing()))
                        }
                    }
                }
            }
        }
        
        // Show AddDatesScreen as an overlay when needed
        if (showAddDateScreen) {
            Surface(
                modifier = Modifier
                    .fillMaxSize()
                    .zIndex(1f),
                color = MaterialTheme.colorScheme.background
            ) {
                AddImportantDatesScreen(
                    onDismiss = { showAddDateScreen = false },
                    onDateAdded = { newDate -> 
                        // Save the date using the viewModel with multiple attempts for reliability
                        try {
                            if (dateToEdit != null) {
                                // Update existing date
                                taskViewModel.updateImportantDate(newDate)
                                // Force a second save to ensure persistence
                                taskViewModel.forceSaveAllData()
                                
                                // Third save attempt after a short delay
                                MainScope().launch {
                                    delay(300)
                                    taskViewModel.forceSaveAllData()
                                    
                                    // Verify the date was saved and reload if necessary
                                    if (!taskViewModel.importantDates.any { it.id == newDate.id }) {
                                        taskViewModel.loadImportantDates()
                                    }
                                }
                            } else {
                                // Add new date
                                taskViewModel.addImportantDate(newDate)
                                // Force a second save to ensure persistence
                                taskViewModel.forceSaveAllData()
                                
                                // Third save attempt after a short delay
                                MainScope().launch {
                                    delay(300)
                                    taskViewModel.forceSaveAllData()
                                    
                                    // Verify the date was saved and reload if necessary
                                    if (!taskViewModel.importantDates.any { it.id == newDate.id }) {
                                        taskViewModel.loadImportantDates()
                                    }
                                }
                            }
                            
                            // Verify the date was actually saved by checking the list
                            val dateFound = taskViewModel.importantDates.any { it.id == newDate.id }
                            if (!dateFound) {
                                // Try one more time if the date wasn't found
                                if (dateToEdit != null) {
                                    taskViewModel.updateImportantDate(newDate)
                                } else {
                                    taskViewModel.addImportantDate(newDate)
                                }
                                taskViewModel.forceSaveAllData()
                            }
                        } catch (e: Exception) {
                            // Emergency save attempt if an exception occurs
                            android.util.Log.e("DatesScreen", "Error saving date: ${e.message}")
                            
                            if (dateToEdit != null) {
                                taskViewModel.updateImportantDate(newDate)
                            } else {
                                taskViewModel.addImportantDate(newDate)
                            }
                            
                            // Emergency force save
                            taskViewModel.forceSaveAllData()
                        } finally {
                            // Always clear dateToEdit and close screen regardless of success/failure
                            dateToEdit = null
                            showAddDateScreen = false 
                        }
                    },
                    existingDate = dateToEdit,
                    taskViewModel = taskViewModel
                )
            }
        }
    }
}

// Helper function to check if two dates are the same day
private fun isSameDay(date1: Date, date2: Date): Boolean {
    val cal1 = Calendar.getInstance()
    val cal2 = Calendar.getInstance()
    cal1.time = date1
    cal2.time = date2
    
    return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
           cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR)
}