package com.taskiq.app.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.taskiq.app.ui.theme.ProfessionalWhite
import com.taskiq.app.ui.theme.topAppBarColor
import com.taskiq.app.ui.theme.responsiveLargeSpacing
import com.taskiq.app.ui.theme.responsiveSpacing
import com.taskiq.app.ui.theme.responsiveLargeTextSize
import com.taskiq.app.ui.theme.sdp
import com.taskiq.app.ui.theme.ssp

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TermsOfUseScreen(
    navController: NavController
) {
    Scaffold(
        topBar = {
            TopAppBar(
                title = { 
                    Text(
                        text = "Terms of Use",
                        fontWeight = FontWeight.Bold,
                        fontSize = 18.ssp()
                    )
                },
                navigationIcon = {
                    IconButton(
                        onClick = { navController.popBackStack() },
                        modifier = Modifier.padding(end = 1.sdp())
                    ) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = topAppBarColor(),
                    titleContentColor = ProfessionalWhite,
                    navigationIconContentColor = ProfessionalWhite
                ),
                windowInsets = WindowInsets.statusBars
            )
        },
        contentWindowInsets = WindowInsets(0, 0, 0, 0)
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(responsiveLargeSpacing() * 3),
            verticalArrangement = Arrangement.spacedBy(responsiveLargeSpacing() * 3)
        ) {
            item {
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = "Last Updated: December 2024",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        fontWeight = FontWeight.Medium
                    )

                    Spacer(modifier = Modifier.height(responsiveLargeSpacing() * 2))

                    Text(
                        text = "Welcome to TaskIQ. These Terms of Use govern your use of our mobile application. By using TaskIQ, you agree to these terms.",
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Justify,
                        lineHeight = responsiveLargeTextSize() * 1.33f
                    )
                }
            }

            item {
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = "Acceptance of Terms",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )

                    Spacer(modifier = Modifier.height(responsiveSpacing() * 3))

                    Text(
                        text = "By downloading, installing, or using TaskIQ, you acknowledge that you have read, understood, and agree to be bound by these Terms of Use and our Privacy Policy.",
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Justify,
                        lineHeight = responsiveLargeTextSize() * 1.33f
                    )
                }
            }

            item {
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = "License to Use",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )

                    Spacer(modifier = Modifier.height(responsiveSpacing() * 3))

                    Text(
                        text = "We grant you a limited, non-exclusive, non-transferable license to use TaskIQ for personal, non-commercial purposes. You may not modify, distribute, or reverse engineer the application.",
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Justify,
                        lineHeight = responsiveLargeTextSize() * 1.33f
                    )
                }
            }

            item {
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = "User Responsibilities",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )

                    Spacer(modifier = Modifier.height(responsiveSpacing() * 3))

                    Text(
                        text = "You are responsible for:\n" +
                                "• Maintaining the security of your account\n" +
                                "• Providing accurate information\n" +
                                "• Using the app in compliance with applicable laws\n" +
                                "• Backing up your important data\n" +
                                "• Not using the app for illegal or harmful purposes",
                        style = MaterialTheme.typography.bodyLarge,
                        lineHeight = responsiveLargeTextSize() * 1.33f
                    )
                }
            }

            item {
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = "Service Availability",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )

                    Spacer(modifier = Modifier.height(responsiveSpacing() * 3))

                    Text(
                        text = "While we strive to provide reliable service, TaskIQ is provided \"as is\" without warranties. We do not guarantee uninterrupted access or error-free operation.",
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Justify,
                        lineHeight = responsiveLargeTextSize() * 1.33f
                    )
                }
            }

            item {
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = "Limitation of Liability",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )

                    Spacer(modifier = Modifier.height(responsiveSpacing() * 3))

                    Text(
                        text = "TaskIQ and its developers shall not be liable for any indirect, incidental, special, or consequential damages arising from your use of the application.",
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Justify,
                        lineHeight = responsiveLargeTextSize() * 1.33f
                    )
                }
            }

            item {
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = "Termination",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )

                    Spacer(modifier = Modifier.height(responsiveSpacing() * 3))

                    Text(
                        text = "You may stop using TaskIQ at any time. We reserve the right to suspend or terminate your access if you violate these terms.",
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Justify,
                        lineHeight = responsiveLargeTextSize() * 1.33f
                    )
                }
            }

            item {
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = "Changes to Terms",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )

                    Spacer(modifier = Modifier.height(responsiveSpacing() * 3))

                    Text(
                        text = "We may update these Terms of Use from time to time. Continued use of the app after changes constitutes acceptance of the new terms.",
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Justify,
                        lineHeight = responsiveLargeTextSize() * 1.33f
                    )
                }
            }

            item {
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = "Contact Information",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )

                    Spacer(modifier = Modifier.height(responsiveSpacing() * 3))

                    Text(
                        text = "If you have questions about these Terms of Use, please contact <NAME_EMAIL>",
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Justify,
                        lineHeight = responsiveLargeTextSize() * 1.33f
                    )
                }
            }
            
            item {
                Spacer(modifier = Modifier.height(responsiveLargeSpacing() * 2))
            }
        }
    }
}
