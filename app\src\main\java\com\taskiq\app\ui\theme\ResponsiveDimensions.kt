package com.taskiq.app.ui.theme

import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * Responsive dimension utilities using SDP (Scalable DP) approach
 * This provides better scaling across different screen sizes and densities
 */

enum class ScreenSize {
    SMALL,   // < 600dp width (phones in portrait)
    MEDIUM,  // 600-840dp width (large phones, small tablets)
    LARGE    // > 840dp width (tablets, phones in landscape)
}



/**
 * Manual SDP calculation for when we don't have resource files
 * Based on the SDP library's algorithm
 */
@Composable
fun Int.sdp(): Dp {
    val context = LocalContext.current
    val displayMetrics = context.resources.displayMetrics

    // Get screen dimensions in dp
    val screenWidthDp = displayMetrics.widthPixels / displayMetrics.density
    val screenHeightDp = displayMetrics.heightPixels / displayMetrics.density

    // Use smallest width as base (similar to SDP library)
    val smallestWidthDp = minOf(screenWidthDp, screenHeightDp)

    // SDP scaling factor based on 320dp baseline
    val scaleFactor = smallestWidthDp / 320f

    // Apply scaling with minimum threshold to prevent too small values
    val scaledValue = this * maxOf(scaleFactor, 0.75f)

    return scaledValue.dp
}

/**
 * Manual SSP calculation for text scaling - returns TextUnit for font sizes
 */
@Composable
fun Int.ssp(): androidx.compose.ui.unit.TextUnit {
    val context = LocalContext.current
    val displayMetrics = context.resources.displayMetrics

    val screenWidthDp = displayMetrics.widthPixels / displayMetrics.density
    val screenHeightDp = displayMetrics.heightPixels / displayMetrics.density
    val smallestWidthDp = minOf(screenWidthDp, screenHeightDp)

    val scaleFactor = smallestWidthDp / 320f
    val scaledValue = this * maxOf(scaleFactor, 0.8f) // Slightly higher minimum for text

    return scaledValue.sp
}

@Composable
fun getScreenSize(): ScreenSize {
    val configuration = LocalConfiguration.current
    val screenWidth = configuration.screenWidthDp

    return when {
        screenWidth < 600 -> ScreenSize.SMALL
        screenWidth < 840 -> ScreenSize.MEDIUM
        else -> ScreenSize.LARGE
    }
}

@Composable
fun getScreenWidth(): Dp {
    val configuration = LocalConfiguration.current
    return configuration.screenWidthDp.dp
}

@Composable
fun getScreenHeight(): Dp {
    val configuration = LocalConfiguration.current
    return configuration.screenHeightDp.dp
}

/**
 * Responsive padding using SDP scaling - optimized for small phones
 */
@Composable
fun responsiveHorizontalPadding(): Dp = 6.sdp()

@Composable
fun responsiveVerticalPadding(): Dp = 2.sdp()

/**
 * Responsive content padding for lists
 */
@Composable
fun responsiveContentPadding(): Dp = 6.sdp()

/**
 * Responsive spacing between elements - standard Dashboard reference
 */
@Composable
fun responsiveSpacing(): Dp = 2.sdp()

/**
 * Responsive spacing for small gaps - standard Dashboard reference
 */
@Composable
fun responsiveSmallSpacing(): Dp = 2.sdp()

/**
 * Responsive spacing for large gaps - standard Dashboard reference (8.sdp for card spacing)
 */
@Composable
fun responsiveLargeSpacing(): Dp = 8.sdp()

/**
 * Responsive icon sizes using SDP scaling - optimized for small phones
 */
@Composable
fun responsiveIconSize(): Dp = 20.sdp()

@Composable
fun responsiveSmallIconSize(): Dp = 14.sdp()

/**
 * Responsive logo/image sizes
 */
@Composable
fun responsiveLogoSize(): Dp = 60.sdp()

/**
 * Responsive button heights
 */
@Composable
fun responsiveButtonHeight(): Dp = 40.sdp()

/**
 * Responsive text field heights
 */
@Composable
fun responsiveTextFieldHeight(): Dp = 44.sdp()

/**
 * Responsive card elevation
 */
@Composable
fun responsiveCardElevation(): Dp = 1.sdp()

/**
 * Responsive corner radius
 */
@Composable
fun responsiveCornerRadius(): Dp = 6.sdp()

/**
 * Responsive maximum width for content (prevents content from being too wide on large screens)
 */
@Composable
fun responsiveMaxContentWidth(): Dp {
    val screenWidth = getScreenWidth()
    val screenSize = getScreenSize()

    return when (screenSize) {
        ScreenSize.SMALL -> screenWidth
        ScreenSize.MEDIUM -> minOf(screenWidth, 600.sdp())
        ScreenSize.LARGE -> minOf(screenWidth, 800.sdp())
    }
}



/**
 * Additional responsive utilities for better small phone support
 */

/**
 * Responsive text sizes using SSP scaling
 */
@Composable
fun responsiveTextSize(): androidx.compose.ui.unit.TextUnit = 14.ssp()

@Composable
fun responsiveSmallTextSize(): androidx.compose.ui.unit.TextUnit = 12.ssp()

@Composable
fun responsiveLargeTextSize(): androidx.compose.ui.unit.TextUnit = 18.ssp()

@Composable
fun responsiveTitleTextSize(): androidx.compose.ui.unit.TextUnit = 20.ssp()



/**
 * Responsive card padding
 */
@Composable
fun responsiveCardPadding(): Dp = 6.sdp()

/**
 * Responsive list item padding
 */
@Composable
fun responsiveListItemPadding(): Dp = 2.sdp()

/**
 * Check if current screen is small phone
 */
@Composable
fun isSmallPhone(): Boolean {
    val screenSize = getScreenSize()
    val screenWidth = getScreenWidth()
    return screenSize == ScreenSize.SMALL && screenWidth < 360.sdp()
}

/**
 * Adaptive padding based on screen size with special handling for small phones
 */
@Composable
fun adaptivePadding(): Dp {
    return if (isSmallPhone()) {
        8.sdp()
    } else {
        12.sdp()
    }
}

/**
 * Adaptive spacing with special handling for small phones - standard Dashboard reference
 */
@Composable
fun adaptiveSpacing(): Dp {
    return if (isSmallPhone()) {
        2.sdp()
    } else {
        4.sdp()
    }
}




