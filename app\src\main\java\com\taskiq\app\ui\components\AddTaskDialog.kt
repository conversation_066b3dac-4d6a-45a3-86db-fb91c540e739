package com.taskiq.app.ui.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Checkbox
import androidx.compose.material3.DatePicker
import androidx.compose.material3.DatePickerDialog
import androidx.compose.material3.Divider
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.SelectableDates
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TimePicker
import androidx.compose.material3.rememberDatePickerState
import androidx.compose.material3.rememberTimePickerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.taskiq.app.ui.theme.*
import androidx.compose.ui.window.Dialog
import com.taskiq.app.model.Subtask
import com.taskiq.app.model.Task
import com.taskiq.app.model.TaskFrequency
import com.taskiq.app.model.TaskPriority
import com.taskiq.app.viewmodel.TaskViewModel
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.UUID
import androidx.compose.foundation.text.KeyboardOptions
import com.taskiq.app.model.getRepeatText

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddTaskDialog(
    onDismiss: () -> Unit,
    onAddTask: (Task) -> Unit,
    taskViewModel: TaskViewModel,
    editingTask: Task? = null,
    allowTemplate: Boolean = true
) {
    var title by remember { mutableStateOf(editingTask?.title ?: "") }
    var description by remember { mutableStateOf(editingTask?.description ?: "") }
    var dueDate by remember { mutableStateOf(editingTask?.dueDate ?: Date(System.currentTimeMillis() + 86400000)) }
    var priority by remember { mutableStateOf(editingTask?.priority ?: TaskPriority.MEDIUM) }
    var frequency by remember { mutableStateOf(editingTask?.frequency ?: TaskFrequency.ONE_TIME) }
    var customDays by remember { mutableStateOf(editingTask?.customFrequencyDays?.toString() ?: "28") }
    var customHours by remember { mutableStateOf(editingTask?.customFrequencyHours?.toString() ?: "0") }
    var showDatePicker by remember { mutableStateOf(false) }
    var showTimePicker by remember { mutableStateOf(false) }
    var showPriorityMenu by remember { mutableStateOf(false) }
    var showFrequencyMenu by remember { mutableStateOf(false) }
    var hasNoDueDate by remember { mutableStateOf(editingTask?.dueDate == null) }
    
    // Subtasks handling - keep this for template loading and editing, but don't show UI for adding new subtasks
    val subtasks = remember { mutableStateListOf<Subtask>() }
    
    // Templates handling
    var isTemplate by remember { mutableStateOf(editingTask?.isTemplate ?: false) }
    var showTemplateSelection by remember { mutableStateOf(false) }
    var selectedTemplateId by remember { mutableStateOf<String?>(editingTask?.templateId) }
    
    // Load existing subtasks if editing
    LaunchedEffect(editingTask) {
        editingTask?.subtasks?.let {
            subtasks.clear()
            subtasks.addAll(it)
        }
    }
    
    // Get templates for selection
    val templates = taskViewModel.tasks.filter { it.isTemplate }
    
    val dateFormatter = remember { SimpleDateFormat("E, MMM d, yyyy", Locale.getDefault()) }
    val timeFormatter = remember { SimpleDateFormat("h:mm a", Locale.getDefault()) }
    
    val datePickerState = rememberDatePickerState(
        initialSelectedDateMillis = dueDate.time
    )
    
    val calendar = remember { Calendar.getInstance() }
    val hour = calendar.get(Calendar.HOUR_OF_DAY)
    val minute = calendar.get(Calendar.MINUTE)
    val timePickerState = rememberTimePickerState(
        initialHour = hour,
        initialMinute = minute
    )
    
    // Load a template if selected
    LaunchedEffect(selectedTemplateId) {
        if (selectedTemplateId != null) {
            val template = taskViewModel.tasks.find { it.id == selectedTemplateId }
            if (template != null) {
                title = template.title
                description = template.description
                priority = template.priority
                frequency = template.frequency
                customDays = template.customFrequencyDays?.toString() ?: "28"
                customHours = template.customFrequencyHours?.toString() ?: "0"
                
                // Copy subtasks but not their completion status
                subtasks.clear()
                subtasks.addAll(template.subtasks.map { it.copy(isCompleted = false) })
            }
        }
    }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { 
            Text(
                when {
                    editingTask != null && editingTask.isTemplate -> "Edit Template"
                    editingTask != null -> "Edit Task"
                    isTemplate -> "Create Template"
                    else -> "Add New Task"
                }
            ) 
        },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = responsiveSmallSpacing())
                    .verticalScroll(rememberScrollState()),
                verticalArrangement = Arrangement.spacedBy(responsiveSpacing())
            ) {
                // Title
                OutlinedTextField(
                    value = title,
                    onValueChange = { title = it },
                    label = { Text("Title") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )
                
                // Description
                OutlinedTextField(
                    value = description,
                    onValueChange = { description = it },
                    label = { Text("Description (Optional)") },
                    modifier = Modifier.fillMaxWidth(),
                    minLines = 2,
                    maxLines = 4
                )
                
                // No Due Date Checkbox
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { hasNoDueDate = !hasNoDueDate },
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Checkbox(
                        checked = hasNoDueDate,
                        onCheckedChange = { hasNoDueDate = it }
                    )
                    Text(
                        text = "No due date",
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(start = responsiveSpacing())
                    )
                }
                
                // Due Date (only show if hasNoDueDate is false)
                if (!hasNoDueDate) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        OutlinedTextField(
                            value = dateFormatter.format(dueDate),
                            onValueChange = { },
                            readOnly = true,
                            label = { Text("Due Date") },
                            modifier = Modifier.weight(1f),
                            trailingIcon = {
                                IconButton(onClick = { showDatePicker = true }) {
                                    Icon(
                                        imageVector = Icons.Default.DateRange,
                                        contentDescription = "Select Date"
                                    )
                                }
                            }
                        )
                    }
                }
                
                // Priority
                Box(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    OutlinedTextField(
                        value = priority.name.lowercase().capitalize(),
                        onValueChange = { },
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { showPriorityMenu = true },
                        readOnly = true,
                        label = { Text("Priority") },
                        trailingIcon = { 
                            IconButton(onClick = { showPriorityMenu = true }) {
                                Icon(
                                    imageVector = Icons.Filled.ArrowDropDown,
                                    contentDescription = "Select priority"
                                )
                            }
                        }
                    )
                    
                    DropdownMenu(
                        expanded = showPriorityMenu,
                        onDismissRequest = { showPriorityMenu = false }
                    ) {
                        TaskPriority.values().forEach { priorityOption ->
                            DropdownMenuItem(
                                text = { Text(priorityOption.name.lowercase().capitalize()) },
                                onClick = {
                                    priority = priorityOption
                                    showPriorityMenu = false
                                }
                            )
                        }
                    }
                }
                
                // Frequency - Only show when hasNoDueDate is false
                if (!hasNoDueDate) {
                    Box(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        OutlinedTextField(
                            value = frequency.getRepeatText(),
                            onValueChange = { },
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable { showFrequencyMenu = true },
                            readOnly = true,
                            label = { Text("Repeat") },
                            trailingIcon = { 
                                IconButton(onClick = { showFrequencyMenu = true }) {
                                    Icon(
                                        imageVector = Icons.Filled.ArrowDropDown,
                                        contentDescription = "Select frequency"
                                    )
                                }
                            }
                        )
                        
                        DropdownMenu(
                            expanded = showFrequencyMenu,
                            onDismissRequest = { showFrequencyMenu = false }
                        ) {
                            // Updated frequency options - only showing the 4 required options
                            DropdownMenuItem(
                                text = { Text("Daily") },
                                onClick = {
                                    frequency = TaskFrequency.DAILY
                                    showFrequencyMenu = false
                                }
                            )
                            DropdownMenuItem(
                                text = { Text("Monthly") },
                                onClick = {
                                    frequency = TaskFrequency.MONTHLY
                                    showFrequencyMenu = false
                                }
                            )
                            DropdownMenuItem(
                                text = { Text("Yearly") },
                                onClick = {
                                    frequency = TaskFrequency.YEARLY
                                    showFrequencyMenu = false
                                }
                            )
                            DropdownMenuItem(
                                text = { Text("Custom") },
                                onClick = {
                                    frequency = TaskFrequency.CUSTOM
                                    showFrequencyMenu = false
                                }
                            )
                        }
                    }
                }
                
                if (frequency == TaskFrequency.CUSTOM && !hasNoDueDate) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(responsiveSpacing())
                    ) {
                        OutlinedTextField(
                            value = customDays,
                            onValueChange = { 
                                if (it.isEmpty() || it.toIntOrNull() != null) {
                                    customDays = it
                                }
                            },
                            label = { Text("Number of Days") },
                            modifier = Modifier.weight(1f),
                            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
                        )
                        
                        OutlinedTextField(
                            value = customHours,
                            onValueChange = { 
                                if (it.isEmpty() || it.toIntOrNull() != null) {
                                    customHours = it
                                }
                            },
                            label = { Text("Number of Hours") },
                            modifier = Modifier.weight(1f),
                            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
                        )
                    }
                }
                
                // Note about subtasks (replaces the subtasks section)
                if (!isTemplate && editingTask == null) {
                    Text(
                        text = "Note: You can add subtasks after creating the task by clicking on it.",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.padding(vertical = responsiveSpacing())
                    )
                }
                
                // Only show existing subtasks when editing
                if (editingTask != null && subtasks.isNotEmpty()) {
                    Text(
                        text = "Subtasks",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.SemiBold,
                        modifier = Modifier.padding(top = responsiveSpacing())
                    )
                    
                    Spacer(modifier = Modifier.height(responsiveSpacing()))
                    
                    // List of existing subtasks when editing
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.spacedBy(responsiveSpacing())
                    ) {
                        subtasks.forEachIndexed { index, subtask ->
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                Checkbox(
                                    checked = subtask.isCompleted,
                                    onCheckedChange = { isChecked ->
                                        subtasks[index] = subtask.copy(isCompleted = isChecked)
                                    }
                                )
                                
                                Text(
                                    text = subtask.title,
                                    modifier = Modifier.weight(1f),
                                    style = MaterialTheme.typography.bodyMedium
                                )
                                
                                IconButton(
                                    onClick = { subtasks.removeAt(index) }
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Delete,
                                        contentDescription = "Remove Subtask"
                                    )
                                }
                            }
                        }
                    }
                }
                
                // Template functionality removed as per requirements
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val task = Task(
                        id = editingTask?.id ?: UUID.randomUUID().toString(),
                        title = title,
                        description = description,
                        dueDate = if (hasNoDueDate) null else dueDate,
                        priority = priority,
                        frequency = frequency,
                        customFrequencyDays = if (frequency == TaskFrequency.CUSTOM) customDays.toIntOrNull() ?: 28 else null,
                        customFrequencyHours = if (frequency == TaskFrequency.CUSTOM) customHours.toIntOrNull() ?: 0 else null,
                        isCompleted = editingTask?.isCompleted ?: false,
                        subtasks = subtasks.toList(),
                        isTemplate = isTemplate,
                        templateId = if (!isTemplate) selectedTemplateId else null,
                        createdAt = editingTask?.createdAt ?: Date(),
                        completedAt = editingTask?.completedAt,
                        userId = "user123", // Default user ID for now
                        reminderTime = editingTask?.reminderTime
                    )
                    onAddTask(task)
                    onDismiss()
                },
                enabled = title.isNotBlank()
            ) {
                Text(
                    when {
                        editingTask != null -> "Save"
                        isTemplate -> "Save Template" 
                        else -> "Add Task"
                    }
                )
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
    
    // Date Picker Dialog
    if (showDatePicker) {
        DatePickerDialog(
            onDismissRequest = { showDatePicker = false },
            confirmButton = {
                TextButton(
                    onClick = {
                        datePickerState.selectedDateMillis?.let { millis ->
                            val newDate = Calendar.getInstance().apply {
                                timeInMillis = millis
                                set(Calendar.HOUR_OF_DAY, calendar.get(Calendar.HOUR_OF_DAY))
                                set(Calendar.MINUTE, calendar.get(Calendar.MINUTE))
                            }
                            dueDate = newDate.time
                        }
                        showDatePicker = false
                    }
                ) {
                    Text("OK")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDatePicker = false }
                ) {
                    Text("Cancel")
                }
            }
        ) {
            DatePicker(
                state = datePickerState
            )
        }
    }
}

// Helper function for the time picker dialog
@Composable
fun CustomTimePickerDialog(
    onCancel: () -> Unit,
    onConfirm: () -> Unit,
    content: @Composable () -> Unit
) {
    Dialog(onDismissRequest = onCancel) {
        Card(
            shape = MaterialTheme.shapes.medium,
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            Column(
                modifier = Modifier.padding(responsiveLargeSpacing()),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Select Time",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.SemiBold
                )
                
                Spacer(modifier = Modifier.height(responsiveLargeSpacing()))
                
                content()
                
                Spacer(modifier = Modifier.height(responsiveSpacing()))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onCancel) {
                        Text("Cancel")
                    }
                    
                    Spacer(modifier = Modifier.width(responsiveSpacing()))
                    
                    Button(onClick = onConfirm) {
                        Text("OK")
                    }
                }
            }
        }
    }
}

// Helper function to capitalize a string
private fun String.capitalize(): String {
    return this.replaceFirstChar { if (it.isLowerCase()) it.titlecase(Locale.getDefault()) else it.toString() }
}