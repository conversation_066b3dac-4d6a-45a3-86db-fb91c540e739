<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AppInsightsSettings">
    <option name="selectedTabId" value="Firebase Crashlytics" />
    <option name="tabSettings">
      <map>
        <entry key="Firebase Crashlytics">
          <value>
            <InsightsFilterSettings>
              <option name="connection">
                <ConnectionSetting>
                  <option name="appId" value="com.taskiq.app" />
                  <option name="mobileSdkAppId" value="1:923766663449:android:46b297660eda892e987d88" />
                  <option name="projectId" value="task-reminder-app-bb81d" />
                  <option name="projectNumber" value="923766663449" />
                </ConnectionSetting>
              </option>
              <option name="signal" value="SIGNAL_UNSPECIFIED" />
              <option name="timeIntervalDays" value="THIRTY_DAYS" />
              <option name="visibilityType" value="ALL" />
            </InsightsFilterSettings>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>