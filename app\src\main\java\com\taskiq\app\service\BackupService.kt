package com.taskiq.app.service

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.google.gson.GsonBuilder
import java.io.File
import java.util.Date
import java.util.zip.ZipEntry
import java.util.zip.ZipInputStream
import java.util.zip.ZipOutputStream
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.delay

/**
 * Service to manage data backup and restore
 */
class BackupService(private val context: Context) {
    
    private val gson = GsonBuilder().setDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").create()
    private val sharedPreferences = context.getSharedPreferences(BACKUP_PREFS, Context.MODE_PRIVATE)
    private var includeVideos = false
    
    companion object {
        private const val TAG = "BackupService"
        private const val BACKUP_PREFS = "backup_preferences"
        private const val LAST_BACKUP_DATE = "last_backup_date"
        private const val LAST_BACKUP_SIZE = "last_backup_size"
        private const val KEY_AUTO_BACKUP = "auto_backup"
        private const val KEY_INCLUDE_VIDEOS = "include_videos"
    }
    
    fun getLastBackupDate(): Date? {
        val lastBackupTime = sharedPreferences.getLong(LAST_BACKUP_DATE, 0)
        return if (lastBackupTime > 0) Date(lastBackupTime) else null
    }
    
    fun getLastBackupSize(): Long {
        return sharedPreferences.getLong(LAST_BACKUP_SIZE, 0)
    }
    
    fun isAutoBackupEnabled(): Boolean {
        return sharedPreferences.getBoolean(KEY_AUTO_BACKUP, false)
    }
    
    fun setAutoBackup(enabled: Boolean) {
        try {
            sharedPreferences.edit().putBoolean(KEY_AUTO_BACKUP, enabled).apply()
        } catch (e: Exception) {
            Log.e(TAG, "Error setting auto backup: ${e.message}")
        }
    }
    
    fun isIncludeVideosEnabled(): Boolean {
        return sharedPreferences.getBoolean(KEY_INCLUDE_VIDEOS, false)
    }
    
    fun setIncludeVideos(enabled: Boolean) {
        try {
            includeVideos = enabled
            sharedPreferences.edit().putBoolean(KEY_INCLUDE_VIDEOS, enabled).apply()
        } catch (e: Exception) {
            Log.e(TAG, "Error setting include videos: ${e.message}")
        }
    }
    
    suspend fun createBackup(): BackupResult<File> = withContext(Dispatchers.IO) {
        try {
            // Create backup directory
            val backupDir = File(context.filesDir, "backups")
            if (!backupDir.exists() && !backupDir.mkdirs()) {
                Log.e(TAG, "Failed to create backup directory")
                return@withContext BackupResult.Error("Failed to create backup directory")
            }
            
            // Create backup file
            val backupFile = File(backupDir, "backup_${System.currentTimeMillis()}.zip")
            
            try {
                // Create zip file with contents
                ZipOutputStream(FileOutputStream(backupFile)).use { zipOut ->
                    try {
                        addPreferencesToZip(zipOut)
                        addDatabaseToZip(zipOut)
                        if (isIncludeVideosEnabled()) {
                            addVideosToZip(zipOut)
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error adding files to backup: ${e.message}")
                        // Continue with backup even if some files fail
                    }
                }
                
                // Update backup info
                updateLastBackupInfo(backupFile)
                return@withContext BackupResult.Success(backupFile)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to create backup file: ${e.message}")
                return@withContext BackupResult.Error("Failed to create backup file: ${e.message}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Unknown backup error: ${e.message}")
            return@withContext BackupResult.Error(e.message ?: "Unknown error during backup")
        }
    }
    
    private fun addPreferencesToZip(zipOut: ZipOutputStream) {
        try {
            val prefsDir = File(context.applicationInfo.dataDir, "shared_prefs")
            if (!prefsDir.exists() || !prefsDir.isDirectory) {
                Log.w(TAG, "Shared preferences directory not found")
                return
            }
            
            prefsDir.listFiles()?.forEach { prefsFile ->
                try {
                    if (prefsFile.extension == "xml") {
                        val entry = ZipEntry("shared_prefs/${prefsFile.name}")
                        zipOut.putNextEntry(entry)
                        prefsFile.inputStream().use { input ->
                            input.copyTo(zipOut)
                        }
                        zipOut.closeEntry()
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error adding preference file ${prefsFile.name}: ${e.message}")
                    // Skip this file and continue
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error adding preferences to zip: ${e.message}")
            // Continue with backup process
        }
    }
    
    private fun addDatabaseToZip(zipOut: ZipOutputStream) {
        try {
            val dbFile = context.getDatabasePath("task_reminder.db")
            if (!dbFile.exists()) {
                Log.w(TAG, "Database file not found")
                return
            }
            
            val entry = ZipEntry("database/task_reminder.db")
            zipOut.putNextEntry(entry)
            dbFile.inputStream().use { input ->
                input.copyTo(zipOut)
            }
            zipOut.closeEntry()
        } catch (e: Exception) {
            Log.e(TAG, "Error adding database to zip: ${e.message}")
            // Continue with backup process
        }
    }
    
    private fun addVideosToZip(zipOut: ZipOutputStream) {
        try {
            val videosDir = File(context.filesDir, "videos")
            if (!videosDir.exists() || !videosDir.isDirectory) {
                Log.w(TAG, "Videos directory not found")
                return
            }
            
            videosDir.listFiles()?.forEach { videoFile ->
                try {
                    if (videoFile.extension in listOf("mp4", "3gp", "mkv")) {
                        val entry = ZipEntry("videos/${videoFile.name}")
                        zipOut.putNextEntry(entry)
                        videoFile.inputStream().use { input ->
                            input.copyTo(zipOut)
                        }
                        zipOut.closeEntry()
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error adding video file ${videoFile.name}: ${e.message}")
                    // Skip this file and continue
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error adding videos to zip: ${e.message}")
            // Continue with backup process
        }
    }
    
    private fun updateLastBackupInfo(backupFile: File) {
        try {
            val editor = sharedPreferences.edit()
            editor.putLong(LAST_BACKUP_DATE, System.currentTimeMillis())
            editor.putLong(LAST_BACKUP_SIZE, backupFile.length())
            editor.apply()
        } catch (e: Exception) {
            Log.e(TAG, "Error updating backup info: ${e.message}")
        }
    }
    
    suspend fun restoreFromBackup(backupFile: File): BackupResult<Boolean> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "=== STARTING BACKUP RESTORE ===")
            Log.d(TAG, "Backup file: ${backupFile.name}, size: ${backupFile.length()} bytes")

            if (!backupFile.exists()) {
                Log.e(TAG, "Backup file does not exist: ${backupFile.absolutePath}")
                return@withContext BackupResult.Error("Backup file not found")
            }

            // Create temporary directory for extraction
            val tempDir = File(context.cacheDir, "restore_temp_${System.currentTimeMillis()}")
            if (!tempDir.mkdirs()) {
                Log.e(TAG, "Failed to create temporary directory")
                return@withContext BackupResult.Error("Failed to create temporary directory")
            }

            try {
                // Extract the backup zip file
                ZipInputStream(FileInputStream(backupFile)).use { zipIn ->
                    var entry = zipIn.nextEntry
                    while (entry != null) {
                        val extractedFile = File(tempDir, entry.name)

                        // Ensure parent directories exist
                        extractedFile.parentFile?.mkdirs()

                        if (!entry.isDirectory) {
                            FileOutputStream(extractedFile).use { output ->
                                zipIn.copyTo(output)
                            }
                            Log.d(TAG, "Extracted: ${entry.name}")
                        }

                        zipIn.closeEntry()
                        entry = zipIn.nextEntry
                    }
                }

                // Restore shared preferences
                restoreSharedPreferences(tempDir)

                // Restore database
                restoreDatabase(tempDir)

                // Restore videos if they exist
                restoreVideos(tempDir)

                // Clean up temporary directory
                tempDir.deleteRecursively()

                Log.d(TAG, "=== BACKUP RESTORE COMPLETED SUCCESSFULLY ===")
                return@withContext BackupResult.Success(true)

            } catch (e: Exception) {
                // Clean up on error
                tempDir.deleteRecursively()
                throw e
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error restoring backup: ${e.message}", e)
            return@withContext BackupResult.Error("Failed to restore backup: ${e.message}")
        }
    }

    private fun restoreSharedPreferences(tempDir: File) {
        try {
            val prefsBackupDir = File(tempDir, "shared_prefs")
            if (!prefsBackupDir.exists()) {
                Log.w(TAG, "No shared preferences found in backup")
                return
            }

            val appPrefsDir = File(context.applicationInfo.dataDir, "shared_prefs")
            if (!appPrefsDir.exists()) {
                appPrefsDir.mkdirs()
            }

            prefsBackupDir.listFiles()?.forEach { backupPrefsFile ->
                try {
                    val targetFile = File(appPrefsDir, backupPrefsFile.name)
                    backupPrefsFile.copyTo(targetFile, overwrite = true)
                    Log.d(TAG, "Restored preferences: ${backupPrefsFile.name}")
                } catch (e: Exception) {
                    Log.e(TAG, "Error restoring preferences file ${backupPrefsFile.name}: ${e.message}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error restoring shared preferences: ${e.message}")
        }
    }

    private fun restoreDatabase(tempDir: File) {
        try {
            val backupDbFile = File(tempDir, "database/task_reminder.db")
            if (!backupDbFile.exists()) {
                Log.w(TAG, "No database found in backup")
                return
            }

            val appDbFile = context.getDatabasePath("task_reminder.db")

            // Close any existing database connections
            // Note: In a real app, you'd want to close the database properly

            // Copy the backup database
            backupDbFile.copyTo(appDbFile, overwrite = true)
            Log.d(TAG, "Restored database: task_reminder.db")

        } catch (e: Exception) {
            Log.e(TAG, "Error restoring database: ${e.message}")
        }
    }

    private fun restoreVideos(tempDir: File) {
        try {
            val videosBackupDir = File(tempDir, "videos")
            if (!videosBackupDir.exists()) {
                Log.w(TAG, "No videos found in backup")
                return
            }

            val appVideosDir = File(context.filesDir, "videos")
            if (!appVideosDir.exists()) {
                appVideosDir.mkdirs()
            }

            videosBackupDir.listFiles()?.forEach { backupVideoFile ->
                try {
                    val targetFile = File(appVideosDir, backupVideoFile.name)
                    backupVideoFile.copyTo(targetFile, overwrite = true)
                    Log.d(TAG, "Restored video: ${backupVideoFile.name}")
                } catch (e: Exception) {
                    Log.e(TAG, "Error restoring video file ${backupVideoFile.name}: ${e.message}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error restoring videos: ${e.message}")
        }
    }
    
    // Real Google Drive backup implementation with simplified authentication
    suspend fun backupToGoogleDrive(backupFile: File, accountEmail: String): BackupResult<String> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "=== STARTING GOOGLE DRIVE BACKUP WITH SIMPLIFIED AUTH ===")
            Log.d(TAG, "Account: $accountEmail")
            Log.d(TAG, "Backup file: ${backupFile.name}, size: ${backupFile.length()} bytes")

            // Create Google Drive auth service for simplified authentication
            val authService = GoogleDriveAuthService(context)

            // Authenticate the account using device account authentication
            when (val authResult = authService.authenticateDeviceAccount(accountEmail)) {
                is AuthResult.Success -> {
                    Log.d(TAG, "Device account authentication successful")

                    // Create Google Drive service with authenticated account
                    val driveService = GoogleDriveService.create(context, accountEmail)

                    // Upload the backup file
                    when (val result = driveService.uploadBackup(backupFile)) {
                        is DriveResult.Success -> {
                            Log.d(TAG, "=== GOOGLE DRIVE BACKUP COMPLETED SUCCESSFULLY ===")
                            Log.d(TAG, "Uploaded file ID: ${result.data}")
                            BackupResult.Success(result.data)
                        }
                        is DriveResult.Error -> {
                            Log.e(TAG, "Google Drive backup failed: ${result.message}")
                            BackupResult.Error(result.message)
                        }
                    }
                }
                is AuthResult.Error -> {
                    Log.e(TAG, "Device account authentication failed: ${authResult.message}")
                    BackupResult.Error("Authentication failed: ${authResult.message}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error backing up to Google Drive: ${e.message}", e)
            BackupResult.Error(e.message ?: "Failed to upload to Google Drive")
        }
    }

    // List available backups from Google Drive with simplified authentication
    suspend fun listGoogleDriveBackups(accountEmail: String): BackupResult<List<DriveBackupInfo>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "=== LISTING GOOGLE DRIVE BACKUPS WITH SIMPLIFIED AUTH ===")
            Log.d(TAG, "Account: $accountEmail")

            // Authenticate using simplified device account authentication
            val authService = GoogleDriveAuthService(context)

            when (val authResult = authService.authenticateDeviceAccount(accountEmail)) {
                is AuthResult.Success -> {
                    Log.d(TAG, "Device account authentication successful for listing")

                    val driveService = GoogleDriveService.create(context, accountEmail)

                    when (val result = driveService.listBackups()) {
                        is DriveResult.Success -> {
                            Log.d(TAG, "Found ${result.data.size} backups in Google Drive")
                            BackupResult.Success(result.data)
                        }
                        is DriveResult.Error -> {
                            Log.e(TAG, "Failed to list Google Drive backups: ${result.message}")
                            BackupResult.Error(result.message)
                        }
                    }
                }
                is AuthResult.Error -> {
                    Log.e(TAG, "Authentication failed for listing: ${authResult.message}")
                    BackupResult.Error("Authentication failed: ${authResult.message}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error listing Google Drive backups: ${e.message}", e)
            BackupResult.Error(e.message ?: "Failed to list Google Drive backups")
        }
    }

    // Download backup from Google Drive with simplified authentication
    suspend fun downloadGoogleDriveBackup(fileId: String, accountEmail: String): BackupResult<File> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "=== DOWNLOADING BACKUP FROM GOOGLE DRIVE WITH SIMPLIFIED AUTH ===")
            Log.d(TAG, "File ID: $fileId, Account: $accountEmail")

            // Authenticate using simplified device account authentication
            val authService = GoogleDriveAuthService(context)

            when (val authResult = authService.authenticateDeviceAccount(accountEmail)) {
                is AuthResult.Success -> {
                    Log.d(TAG, "Device account authentication successful for download")

                    val driveService = GoogleDriveService.create(context, accountEmail)

                    // Create destination file
                    val backupDir = File(context.filesDir, "backups")
                    if (!backupDir.exists()) backupDir.mkdirs()
                    val destinationFile = File(backupDir, "downloaded_backup_${System.currentTimeMillis()}.zip")

                    when (val result = driveService.downloadBackup(fileId, destinationFile)) {
                        is DriveResult.Success -> {
                            Log.d(TAG, "=== GOOGLE DRIVE DOWNLOAD COMPLETED ===")
                            BackupResult.Success(result.data)
                        }
                        is DriveResult.Error -> {
                            Log.e(TAG, "Failed to download from Google Drive: ${result.message}")
                            BackupResult.Error(result.message)
                        }
                    }
                }
                is AuthResult.Error -> {
                    Log.e(TAG, "Authentication failed for download: ${authResult.message}")
                    BackupResult.Error("Authentication failed: ${authResult.message}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error downloading from Google Drive: ${e.message}", e)
            BackupResult.Error(e.message ?: "Failed to download from Google Drive")
        }
    }

    // Delete backup from Google Drive with simplified authentication
    suspend fun deleteGoogleDriveBackup(fileId: String, accountEmail: String): BackupResult<Unit> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "=== DELETING BACKUP FROM GOOGLE DRIVE WITH SIMPLIFIED AUTH ===")
            Log.d(TAG, "File ID: $fileId, Account: $accountEmail")

            // Authenticate using simplified device account authentication
            val authService = GoogleDriveAuthService(context)

            when (val authResult = authService.authenticateDeviceAccount(accountEmail)) {
                is AuthResult.Success -> {
                    Log.d(TAG, "Device account authentication successful for deletion")

                    // Create Google Drive service with authenticated account
                    val driveService = GoogleDriveService.create(context, accountEmail)

                    // Delete the backup file
                    when (val result = driveService.deleteBackup(fileId)) {
                        is DriveResult.Success -> {
                            Log.d(TAG, "=== GOOGLE DRIVE BACKUP DELETION COMPLETED SUCCESSFULLY ===")
                            BackupResult.Success(Unit)
                        }
                        is DriveResult.Error -> {
                            Log.e(TAG, "Google Drive backup deletion failed: ${result.message}")
                            BackupResult.Error(result.message)
                        }
                    }
                }
                is AuthResult.Error -> {
                    Log.e(TAG, "Device account authentication failed for deletion: ${authResult.message}")
                    BackupResult.Error("Authentication failed: ${authResult.message}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting Google Drive backup: ${e.message}", e)
            BackupResult.Error(e.message ?: "Failed to delete Google Drive backup")
        }
    }
}

sealed class BackupResult<out T> {
    data class Success<T>(val data: T) : BackupResult<T>()
    data class Error(val message: String) : BackupResult<Nothing>()
} 