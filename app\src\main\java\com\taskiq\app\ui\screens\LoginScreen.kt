package com.taskiq.app.ui.screens

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.*
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.taskiq.app.R
import com.taskiq.app.ui.navigation.Routes
import com.taskiq.app.ui.components.AuthButton
import com.taskiq.app.ui.components.AuthTextField
import com.taskiq.app.ui.theme.contentModifier
import com.taskiq.app.ui.theme.rootContainerModifier
import com.taskiq.app.ui.theme.standardHorizontalPadding
import com.taskiq.app.ui.theme.uniformHorizontalPadding
import com.taskiq.app.ui.theme.uniformVerticalPadding
import com.taskiq.app.ui.theme.uniformSectionSpacing
import com.taskiq.app.ui.theme.uniformSmallSpacing
import com.taskiq.app.ui.theme.uniformLargeSpacing
import com.taskiq.app.ui.theme.ProfessionalBlue
import com.taskiq.app.ui.theme.ProfessionalWhite
import com.taskiq.app.ui.theme.Blue80
import com.taskiq.app.ui.theme.*
import com.taskiq.app.viewmodel.AuthViewModel
import androidx.compose.ui.platform.LocalContext
import androidx.compose.runtime.rememberCoroutineScope

@Composable
fun LoginScreen(
    navController: NavController,
    viewModel: AuthViewModel
) {
    var email by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    var emailError by remember { mutableStateOf<String?>(null) }
    var passwordError by remember { mutableStateOf<String?>(null) }

    val loginState by viewModel.loginState.collectAsState()
    val error by viewModel.error.collectAsState()



    // Validation functions
    fun validateEmail(email: String): String? {
        return when {
            email.isBlank() -> "Email is required"
            !android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches() -> "Please enter a valid email"
            else -> null
        }
    }

    fun validatePassword(password: String): String? {
        return when {
            password.isBlank() -> "Password is required"
            password.length < 6 -> "Password must be at least 6 characters"
            else -> null
        }
    }

    LaunchedEffect(key1 = loginState) {
        if (loginState == AuthViewModel.AuthState.SUCCESS) {
            navController.navigate(Routes.MAIN) {
                popUpTo(Routes.LOGIN) { inclusive = true }
            }
        }
    }



    Surface(
        color = ProfessionalBlue,
        modifier = rootContainerModifier
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
                    .padding(horizontal = uniformHorizontalPadding() * 2) // Uniform padding for auth screens
                    .widthIn(max = responsiveMaxContentWidth()),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                // App Logo
                Image(
                    painter = painterResource(id = R.drawable.taskiq),
                    contentDescription = "App Logo",
                    modifier = Modifier
                        .size(if (isSmallPhone()) 60.sdp() else responsiveLogoSize())
                        .padding(bottom = uniformSectionSpacing())
                )

                // Title section
                Text(
                    text = "Welcome Back!",
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = ProfessionalWhite,
                    modifier = Modifier.padding(bottom = uniformSmallSpacing())
                )

                Text(
                    text = "Sign in to continue your productivity journey",
                    style = MaterialTheme.typography.bodyLarge,
                    color = ProfessionalWhite.copy(alpha = 0.9f),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(bottom = uniformSectionSpacing())
                )

                // Login Form
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = uniformSmallSpacing())
                ) {
                        // Email Field
                        AuthTextField(
                            value = email,
                            onValueChange = {
                                email = it
                                emailError = validateEmail(it)
                            },
                            label = "Email Address",
                            leadingIcon = Icons.Default.Email,
                            keyboardType = KeyboardType.Email,
                            isError = emailError != null,
                            errorMessage = emailError
                        )

                        Spacer(modifier = Modifier.height(uniformSectionSpacing()))

                        // Password Field
                        AuthTextField(
                            value = password,
                            onValueChange = {
                                password = it
                                passwordError = validatePassword(it)
                            },
                            label = "Password",
                            leadingIcon = Icons.Default.Lock,
                            isPassword = true,
                            isError = passwordError != null,
                            errorMessage = passwordError
                        )

                        // Forgot Password Link
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = uniformSmallSpacing()),
                            horizontalArrangement = Arrangement.Start
                        ) {
                            TextButton(
                                onClick = { navController.navigate(Routes.FORGOT_PASSWORD) }
                            ) {
                                Text(
                                    text = "Forgot Password?",
                                    color = Color.White,
                                    fontSize = responsiveSmallTextSize()
                                )
                            }
                        }

                        Spacer(modifier = Modifier.height(uniformSectionSpacing()))

                        // Login Button
                        AuthButton(
                            text = "Sign In",
                            onClick = {
                                emailError = validateEmail(email)
                                passwordError = validatePassword(password)

                                if (emailError == null && passwordError == null) {
                                    viewModel.login(email, password)
                                }
                            },
                            isLoading = loginState == AuthViewModel.AuthState.LOADING,
                            enabled = email.isNotBlank() && password.isNotBlank()
                        )

                        // Error message
                        AnimatedVisibility(
                            visible = loginState == AuthViewModel.AuthState.ERROR && error != null
                        ) {
                            Card(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(top = uniformLargeSpacing()),
                                colors = CardDefaults.cardColors(
                                    containerColor = MaterialTheme.colorScheme.errorContainer
                                ),
                                shape = RoundedCornerShape(responsiveCornerRadius())
                            ) {
                                Text(
                                    text = error ?: "Invalid email or password",
                                    color = MaterialTheme.colorScheme.onErrorContainer,
                                    style = MaterialTheme.typography.bodyMedium,
                                    modifier = Modifier.padding(uniformSectionSpacing())
                                )
                            }
                }
            }

                Spacer(modifier = Modifier.height(uniformSectionSpacing()))

                // Sign Up Link
                Row(
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                Text(
                    text = "Don't have an account? ",
                    color = ProfessionalWhite.copy(alpha = 0.9f)
                )
                TextButton(
                    onClick = { navController.navigate(Routes.REGISTER) }
                ) {
                    Text(
                        text = "Sign Up",
                        color = ProfessionalWhite,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
            }
        }
    }
}

