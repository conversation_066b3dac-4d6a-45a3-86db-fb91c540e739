package com.taskiq.app.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.automirrored.filled.ArrowForward
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.History
import androidx.compose.material.icons.filled.Notifications
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.taskiq.app.ui.navigation.Routes
import com.taskiq.app.ui.theme.ProfessionalBlue
import com.taskiq.app.ui.theme.ProfessionalWhite
import com.taskiq.app.viewmodel.AuthViewModel
import com.taskiq.app.viewmodel.TaskViewModel
import kotlinx.coroutines.launch
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.clickable
import com.taskiq.app.ui.theme.contentModifier
import com.taskiq.app.ui.theme.fullWidthContentModifier
import com.taskiq.app.ui.theme.rootContainerModifier
import com.taskiq.app.ui.theme.contentBelowHeaderModifier
import com.taskiq.app.ui.theme.uniformHorizontalPadding
import com.taskiq.app.ui.theme.uniformInitialPadding
import com.taskiq.app.ui.theme.uniformSectionSpacing
import com.taskiq.app.ui.theme.uniformSmallSpacing
import com.taskiq.app.ui.theme.responsiveIconSize
import com.taskiq.app.ui.theme.responsiveLargeTextSize
import com.taskiq.app.ui.theme.topAppBarColor
import com.taskiq.app.ui.theme.sdp
import com.taskiq.app.ui.theme.ssp

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    navController: NavController,
    viewModel: AuthViewModel,
    onDeleteAccount: () -> Unit
) {
    val isDarkMode by viewModel.isDarkMode.collectAsState()
    var showDeleteDialog by remember { mutableStateOf(false) }
    
    // Snackbar setup
    val snackbarHostState = remember { SnackbarHostState() }
    val scope = rememberCoroutineScope()
    
    Box(
        modifier = rootContainerModifier
    ) {
        Scaffold(
            snackbarHost = { SnackbarHost(snackbarHostState) },
            topBar = {
                TopAppBar(
                    title = {
                        Text(
                            text = "Settings",
                            color = ProfessionalWhite,
                            fontWeight = FontWeight.Bold,
                            fontSize = 18.ssp()
                        )
                    },
                    navigationIcon = {
                        IconButton(onClick = { navController.popBackStack() }) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "Back",
                                tint = ProfessionalWhite
                            )
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = topAppBarColor(),
                        titleContentColor = ProfessionalWhite,
                        navigationIconContentColor = ProfessionalWhite
                    ),
                    windowInsets = WindowInsets.statusBars
                )
            },
            contentWindowInsets = WindowInsets(0, 0, 0, 0)
        ) { paddingValues ->
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .padding(horizontal = uniformHorizontalPadding()),
                verticalArrangement = Arrangement.Top
            ) {
                Spacer(modifier = Modifier.height(uniformInitialPadding()))
                
                // Dark Mode Setting
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = uniformSectionSpacing()),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // Replace icon with blank space
                        Spacer(modifier = Modifier.width(responsiveIconSize() * 2))
                        
                        Text(
                            text = "Enable Dark Mode",
                            fontSize = responsiveLargeTextSize(),
                            modifier = Modifier.padding(start = uniformSmallSpacing())
                        )
                    }
                    
                    Switch(
                        checked = isDarkMode,
                        onCheckedChange = { viewModel.toggleDarkMode() }
                    )
                }
                
                // Notification Settings
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable {
                            navController.navigate(Routes.NOTIFICATION_SETTINGS)
                        }
                        .padding(vertical = uniformSectionSpacing()),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // Replace icon with blank space
                        Spacer(modifier = Modifier.width(responsiveIconSize() * 2))

                        Text(
                            text = "Notification Settings",
                            fontSize = responsiveLargeTextSize(),
                            modifier = Modifier.padding(start = uniformSmallSpacing())
                        )
                    }

                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.ArrowForward,
                        contentDescription = "Go to Notification Settings",
                        modifier = Modifier.size(24.sdp()),
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                // Language Settings
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable {
                            // TODO: Navigate to language selection screen or show language dialog
                            scope.launch {
                                snackbarHostState.showSnackbar("Language settings coming soon!")
                            }
                        }
                        .padding(vertical = uniformSectionSpacing()),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // Replace icon with blank space
                        Spacer(modifier = Modifier.width(responsiveIconSize() * 2))

                        Text(
                            text = "Language",
                            fontSize = responsiveLargeTextSize(),
                            modifier = Modifier.padding(start = uniformSmallSpacing())
                        )
                    }

                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.ArrowForward,
                        contentDescription = "Go to Language Settings",
                        modifier = Modifier.size(24.sdp()),
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                // Delete Account
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = uniformSectionSpacing()),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // Replace icon with blank space
                        Spacer(modifier = Modifier.width(responsiveIconSize() * 2))
                        
                        Text(
                            text = "Delete Account",
                            fontSize = responsiveLargeTextSize(),
                            modifier = Modifier.padding(start = uniformSmallSpacing())
                        )
                    }
                    
                    IconButton(
                        onClick = { showDeleteDialog = true }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "Delete Account",
                            tint = MaterialTheme.colorScheme.error
                        )
                    }
                }
            }
            
            // Confirmation Dialog for Account Deletion
            if (showDeleteDialog) {
                AlertDialog(
                    onDismissRequest = { showDeleteDialog = false },
                    title = { Text("Delete Account") },
                    text = { 
                        Text(
                            "Are you sure you want to delete your account? This action cannot be undone and all your data will be lost."
                        ) 
                    },
                    confirmButton = {
                        Button(
                            onClick = {
                                showDeleteDialog = false
                                onDeleteAccount()
                            },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = MaterialTheme.colorScheme.error
                            )
                        ) {
                            Text("Delete")
                        }
                    },
                    dismissButton = {
                        TextButton(
                            onClick = { showDeleteDialog = false }
                        ) {
                            Text("Cancel")
                        }
                    }
                )
            }
        }
    }
} 
