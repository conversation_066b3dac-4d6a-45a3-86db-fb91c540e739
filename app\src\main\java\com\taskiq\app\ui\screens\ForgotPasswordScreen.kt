package com.taskiq.app.ui.screens

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import kotlinx.coroutines.delay
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.taskiq.app.R
import com.taskiq.app.ui.components.AuthButton
import com.taskiq.app.ui.components.AuthTextField
import com.taskiq.app.ui.theme.rootContainerModifier
import com.taskiq.app.ui.theme.ProfessionalBlue
import com.taskiq.app.ui.theme.ProfessionalWhite
import com.taskiq.app.ui.theme.Blue80
import com.taskiq.app.ui.theme.uniformHorizontalPadding
import com.taskiq.app.ui.theme.uniformSectionSpacing
import com.taskiq.app.ui.theme.uniformSmallSpacing
import com.taskiq.app.ui.theme.uniformLargeSpacing
import com.taskiq.app.viewmodel.AuthViewModel
import com.taskiq.app.ui.theme.responsiveLogoSize
import com.taskiq.app.ui.theme.responsiveSpacing
import com.taskiq.app.ui.theme.responsiveVerticalPadding
import com.taskiq.app.ui.theme.responsiveLargeSpacing
import com.taskiq.app.ui.theme.responsiveCardElevation
import com.taskiq.app.ui.theme.responsiveIconSize
import com.taskiq.app.ui.theme.responsiveCornerRadius
import com.taskiq.app.ui.theme.responsiveSmallSpacing

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ForgotPasswordScreen(
    navController: NavController,
    viewModel: AuthViewModel
) {
    var email by remember { mutableStateOf("") }
    var emailError by remember { mutableStateOf<String?>(null) }
    
    val resetPasswordState by viewModel.resetPasswordState.collectAsState()
    val error by viewModel.error.collectAsState()

    // Email validation
    fun validateEmail(email: String): String? {
        return when {
            email.isBlank() -> "Email is required"
            !android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches() -> "Please enter a valid email address"
            else -> null
        }
    }

    // Handle state changes
    LaunchedEffect(resetPasswordState) {
        if (resetPasswordState == AuthViewModel.AuthState.SUCCESS) {
            // Reset will be handled by showing success message
        }
    }
    
    Surface(
        color = ProfessionalBlue,
        modifier = rootContainerModifier
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(horizontal = uniformHorizontalPadding() * 2),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
                
                    // Success State
                    if (resetPasswordState == AuthViewModel.AuthState.SUCCESS) {
                        Card(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(uniformSectionSpacing()),
                            shape = RoundedCornerShape(responsiveLargeSpacing() * 3),
                            colors = CardDefaults.cardColors(
                                containerColor = MaterialTheme.colorScheme.primaryContainer
                            ),
                            elevation = CardDefaults.cardElevation(defaultElevation = responsiveCardElevation() * 8)
                        ) {
                            Column(
                                modifier = Modifier.padding(uniformSectionSpacing() * 2),
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                Icon(
                                    imageVector = Icons.Default.CheckCircle,
                                    contentDescription = null,
                                    tint = MaterialTheme.colorScheme.primary,
                                    modifier = Modifier.size(responsiveIconSize() * 3.6f)
                                )

                                Spacer(modifier = Modifier.height(uniformSectionSpacing()))

                                Text(
                                    text = "Email Sent!",
                                    style = MaterialTheme.typography.headlineSmall,
                                    fontWeight = FontWeight.Bold,
                                    color = MaterialTheme.colorScheme.onPrimaryContainer
                                )

                                Spacer(modifier = Modifier.height(uniformSmallSpacing()))

                                Text(
                                    text = "We've sent a password reset link to $email. Please check your email and follow the instructions to reset your password.",
                                    style = MaterialTheme.typography.bodyMedium,
                                    textAlign = TextAlign.Center,
                                    color = MaterialTheme.colorScheme.onPrimaryContainer
                                )

                                Spacer(modifier = Modifier.height(uniformLargeSpacing()))

                                Button(
                                    onClick = { navController.popBackStack() },
                                    modifier = Modifier.fillMaxWidth(),
                                    shape = RoundedCornerShape(responsiveCornerRadius())
                                ) {
                                    Text("Back to Login")
                                }
                            }
                        }
                    } else {
                        // Reset Form
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            // App Logo
                            Image(
                                painter = painterResource(id = R.drawable.taskiq),
                                contentDescription = "App Logo",
                                modifier = Modifier
                                    .size(responsiveLogoSize() * 1.67f)
                                    .padding(bottom = uniformSectionSpacing())
                            )

                            Text(
                                text = "Forgot Password?",
                                style = MaterialTheme.typography.headlineMedium,
                                fontWeight = FontWeight.ExtraBold,
                                color = ProfessionalWhite,
                                modifier = Modifier.padding(bottom = uniformSmallSpacing())
                            )

                            Text(
                                text = "Don't worry! Enter your email address and we'll send you a link to reset your password.",
                                style = MaterialTheme.typography.bodyLarge,
                                color = ProfessionalWhite.copy(alpha = 0.9f),
                                textAlign = TextAlign.Center,
                                modifier = Modifier.padding(bottom = uniformSectionSpacing())
                            )
                    
                            // Form
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(horizontal = uniformSmallSpacing())
                            ) {
                                    // Email Field
                                    AuthTextField(
                                        value = email,
                                        onValueChange = {
                                            email = it
                                            emailError = validateEmail(it)
                                        },
                                        label = "Email Address",
                                        leadingIcon = Icons.Default.Email,
                                        keyboardType = KeyboardType.Email,
                                        isError = emailError != null,
                                        errorMessage = emailError,
                                        modifier = Modifier.padding(bottom = uniformSectionSpacing())
                                    )

                                    // Reset Button
                                    AuthButton(
                                        text = "Send Reset Link",
                                        onClick = {
                                            emailError = validateEmail(email)
                                            if (emailError == null) {
                                                viewModel.resetPassword(email)
                                            }
                                        },
                                        isLoading = resetPasswordState == AuthViewModel.AuthState.LOADING,
                                        enabled = email.isNotBlank() && emailError == null
                                    )
                            }

                            // Error message
                            if (resetPasswordState == AuthViewModel.AuthState.ERROR && error != null) {
                                Card(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(top = uniformLargeSpacing()),
                                    colors = CardDefaults.cardColors(
                                        containerColor = MaterialTheme.colorScheme.errorContainer
                                    ),
                                    shape = RoundedCornerShape(responsiveCornerRadius())
                                ) {
                                    Text(
                                        text = error ?: "An error occurred",
                                        color = MaterialTheme.colorScheme.onErrorContainer,
                                        style = MaterialTheme.typography.bodyMedium,
                                        modifier = Modifier.padding(uniformSectionSpacing())
                                    )
                                }
                            }

                            Spacer(modifier = Modifier.height(uniformSectionSpacing()))

                            // Back to Login
                            TextButton(
                                onClick = { navController.popBackStack() }
                            ) {
                                Text(
                                    text = "Back to Login",
                                    color = ProfessionalWhite,
                                    fontWeight = FontWeight.Medium
                                )
                            }
                        }
                    }
        }
    }
}
