package com.taskiq.app.service

import android.content.Context
import android.util.Log
import com.taskiq.app.model.User
import com.google.firebase.auth.FirebaseAuth
import kotlinx.coroutines.tasks.await
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.coroutines.suspendCoroutine
import com.google.gson.Gson

class AuthService(private val context: Context) {
    private val auth: FirebaseAuth = FirebaseAuth.getInstance()

    suspend fun signUp(firstName: String, lastName: String, email: String, password: String): User {
        return suspendCoroutine { continuation ->
            auth.createUserWithEmailAndPassword(email, password)
                .addOnSuccessListener {
                    val user = User(firstName, lastName, email, true)
                    continuation.resume(user)
                }
                .addOnFailureListener { exception ->
                    continuation.resumeWithException(exception)
                }
        }
    }

    suspend fun login(email: String, password: String): User {
        return suspendCoroutine { continuation ->
            auth.signInWithEmailAndPassword(email, password)
                .addOnSuccessListener {
                    // In a real app, you would fetch the user details from Firestore or another database
                    val user = User(email = email, isLoggedIn = true)
                    continuation.resume(user)
                }
                .addOnFailureListener { exception ->
                    continuation.resumeWithException(exception)
                }
        }
    }

    fun logout() {
        auth.signOut()
    }

    fun getCurrentUser(): User? {
        val firebaseUser = auth.currentUser
        
        if (firebaseUser != null) {
            // First try to load from SharedPreferences
            val sharedPrefs = context.getSharedPreferences("user_data", Context.MODE_PRIVATE)
            val userJson = sharedPrefs.getString("current_user", null)
            
            // If we have stored user data, use it
            if (userJson != null) {
                try {
                    val gson = Gson()
                    return gson.fromJson(userJson, User::class.java)
                } catch (e: Exception) {
                    Log.e("AuthService", "Error loading user: ${e.message}")
                }
            }
            
            // Fallback to default user data if nothing in SharedPreferences
            return User(firstName = "Sanjay", lastName = "Mandal", email = firebaseUser.email ?: "", isLoggedIn = true)
        }
        
        return null
    }
    
    suspend fun updatePassword(currentPassword: String, newPassword: String): Boolean {
        return suspendCoroutine { continuation ->
            val user = auth.currentUser
            if (user != null && user.email != null) {
                // First reauthenticate with current password
                auth.signInWithEmailAndPassword(user.email!!, currentPassword)
                    .addOnSuccessListener {
                        // Then update password
                        user.updatePassword(newPassword)
                            .addOnSuccessListener {
                                continuation.resume(true)
                            }
                            .addOnFailureListener { exception ->
                                continuation.resumeWithException(exception)
                            }
                    }
                    .addOnFailureListener { exception ->
                        continuation.resumeWithException(exception)
                    }
            } else {
                continuation.resumeWithException(Exception("User is not logged in"))
            }
        }
    }
    
    suspend fun updateUserProfile(name: String, email: String, gender: String): User {
        return suspendCoroutine { continuation ->
            val user = auth.currentUser
            if (user != null) {
                try {
                    // Split name into first and last name
                    val firstName = name.split(" ").firstOrNull() ?: ""
                    val lastName = name.split(" ").drop(1).joinToString(" ")
                    
                    // Create updated user object with the provided gender
                    val updatedUser = User(
                        firstName = firstName,
                        lastName = lastName,
                        email = email,
                        isLoggedIn = true,
                        gender = gender // Use the provided gender value
                    )
                    
                    Log.d("AuthService", "Updating user profile with gender: $gender")
                    
                    // Save user data to SharedPreferences - Use commit() for immediate persistence
                    val sharedPrefs = context.getSharedPreferences("user_data", Context.MODE_PRIVATE)
                    val gson = Gson()
                    val userJson = gson.toJson(updatedUser)
                    
                    val saveSuccess = sharedPrefs.edit()
                        .putString("current_user", userJson)
                        .commit() // Use commit instead of apply for immediate persistence
                    
                    if (saveSuccess) {
                        Log.d("AuthService", "Successfully saved user profile to SharedPreferences")
                    } else {
                        Log.e("AuthService", "Failed to save user profile to SharedPreferences")
                    }
                    
                    continuation.resume(updatedUser)
                } catch (e: Exception) {
                    Log.e("AuthService", "Error updating user profile: ${e.message}", e)
                    continuation.resumeWithException(e)
                }
            } else {
                continuation.resumeWithException(Exception("User is not logged in"))
            }
        }
    }
    
    suspend fun deleteAccount(): Boolean {
        return suspendCoroutine { continuation ->
            val user = auth.currentUser
            if (user != null) {
                // In a real implementation, you'd also clean up user data in Firestore/other storage
                user.delete()
                    .addOnSuccessListener {
                        continuation.resume(true)
                    }
                    .addOnFailureListener { exception ->
                        continuation.resumeWithException(exception)
                    }
            } else {
                continuation.resumeWithException(Exception("User is not logged in"))
            }
        }
    }

    suspend fun resetPassword(email: String): Boolean {
        return suspendCoroutine { continuation ->
            auth.sendPasswordResetEmail(email)
                .addOnSuccessListener {
                    Log.d("AuthService", "Password reset email sent to: $email")
                    continuation.resume(true)
                }
                .addOnFailureListener { exception ->
                    Log.e("AuthService", "Failed to send password reset email: ${exception.message}")
                    continuation.resumeWithException(exception)
                }
        }
    }
}