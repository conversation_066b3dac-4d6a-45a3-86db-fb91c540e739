package com.taskiq.app.worker

import android.content.Context
import android.util.Log
import com.taskiq.app.TaskIQApplication
import com.taskiq.app.model.Task
import com.taskiq.app.model.TaskFrequency
import com.taskiq.app.model.TaskPriority
import com.taskiq.app.service.NotificationService
import java.util.concurrent.TimeUnit

/**
 * Manager class for scheduling task reminders.
 * This is a simplified implementation without actual WorkManager usage.
 */
class TaskWorkerManager(private val context: Context) {
    
    private val notificationService: NotificationService = TaskIQApplication.getNotificationService()
    private val TAG = "TaskWorkerManager"
    
    /**
     * Schedule a reminder for a task based on its frequency and priority.
     */
    fun scheduleTaskReminder(task: Task) {
        Log.d(TAG, "Scheduling reminder for task: ${task.title} with priority ${task.priority}")
        
        // If it's a high priority task, ensure we create notifications
        if (task.priority == TaskPriority.HIGH) {
            Log.d(TAG, "High priority task detected - ensuring notification is scheduled: ${task.title}")
            
            // Schedule immediate notification if due date is today or tomorrow
            val now = System.currentTimeMillis()
            val dayInMillis = TimeUnit.DAYS.toMillis(1)
            
            if (task.dueDate != null) {
                val daysUntilDue = (task.dueDate.time - now) / dayInMillis
                
                if (daysUntilDue <= 1) {
                    Log.d(TAG, "Task is due soon (within 24-48 hours), scheduling priority notification: ${task.title}")
                    // Add high priority indicator to title
                    val modifiedTask = task.copy(
                        title = "HIGH PRIORITY: ${task.title}"
                    )
                    notificationService.scheduleTaskReminder(modifiedTask)
                } else {
                    notificationService.scheduleTaskReminder(task)
                }
            } else {
                notificationService.scheduleTaskReminder(task)
            }
        } else {
            // Regular priority task
            notificationService.scheduleTaskReminder(task)
        }
    }
    
    /**
     * Cancel a task reminder.
     */
    fun cancelTaskReminder(taskId: String) {
        Log.d(TAG, "Cancelling reminder for task: $taskId")
        
        // Cancel any notifications
        notificationService.cancelTaskReminder(taskId)
    }
    
    /**
     * Cancel all task reminders.
     */
    fun cancelAllTaskReminders() {
        Log.d(TAG, "Cancelling all task reminders")
    }
} 