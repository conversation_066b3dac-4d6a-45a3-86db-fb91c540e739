package com.taskiq.app.ui.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.List
import androidx.compose.material3.Button
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.taskiq.app.ui.theme.contentModifier
import com.taskiq.app.ui.theme.sdp
import kotlin.math.cos
import kotlin.math.sin

@Composable
fun TaskEmptyState(
    message: String = "You don't have any tasks yet. Add a task to get started!",
    actionLabel: String = "Add Task",
    onAction: () -> Unit
) {
    Column(
        modifier = contentModifier(),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Abstract art instead of simple icon
        AbstractArtwork(
            modifier = Modifier.size(120.sdp())
        )

        Spacer(modifier = Modifier.height(24.sdp()))

        Text(
            text = message,
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Spacer(modifier = Modifier.height(24.sdp()))

        Button(
            onClick = onAction
        ) {
            Text(actionLabel)
        }
    }
}

@Composable
private fun AbstractArtwork(
    modifier: Modifier = Modifier
) {
    val primaryColor = MaterialTheme.colorScheme.primary
    val secondaryColor = MaterialTheme.colorScheme.secondary
    val tertiaryColor = MaterialTheme.colorScheme.tertiary

    Canvas(modifier = modifier) {
        val width = size.width
        val height = size.height
        val centerX = width / 2f
        val centerY = height / 2f

        // Create abstract geometric shapes with gradients

        // Background circle with gradient
        drawCircle(
            brush = Brush.radialGradient(
                colors = listOf(
                    primaryColor.copy(alpha = 0.1f),
                    primaryColor.copy(alpha = 0.05f)
                ),
                center = Offset(centerX, centerY),
                radius = width / 2f
            ),
            radius = width / 2f,
            center = Offset(centerX, centerY)
        )

        // Abstract flowing shapes
        val path1 = Path().apply {
            moveTo(centerX - width * 0.3f, centerY - height * 0.2f)
            quadraticBezierTo(
                centerX + width * 0.1f, centerY - height * 0.4f,
                centerX + width * 0.3f, centerY - height * 0.1f
            )
            quadraticBezierTo(
                centerX + width * 0.2f, centerY + height * 0.1f,
                centerX - width * 0.1f, centerY + height * 0.2f
            )
            quadraticBezierTo(
                centerX - width * 0.4f, centerY + height * 0.1f,
                centerX - width * 0.3f, centerY - height * 0.2f
            )
            close()
        }

        drawPath(
            path = path1,
            brush = Brush.linearGradient(
                colors = listOf(
                    primaryColor.copy(alpha = 0.6f),
                    secondaryColor.copy(alpha = 0.4f)
                ),
                start = Offset(centerX - width * 0.3f, centerY - height * 0.2f),
                end = Offset(centerX + width * 0.3f, centerY + height * 0.2f)
            )
        )

        // Second abstract shape
        val path2 = Path().apply {
            moveTo(centerX + width * 0.2f, centerY - height * 0.3f)
            quadraticBezierTo(
                centerX + width * 0.4f, centerY - height * 0.1f,
                centerX + width * 0.2f, centerY + height * 0.1f
            )
            quadraticBezierTo(
                centerX, centerY + height * 0.3f,
                centerX - width * 0.2f, centerY + height * 0.1f
            )
            quadraticBezierTo(
                centerX - width * 0.1f, centerY - height * 0.1f,
                centerX + width * 0.2f, centerY - height * 0.3f
            )
            close()
        }

        drawPath(
            path = path2,
            brush = Brush.linearGradient(
                colors = listOf(
                    tertiaryColor.copy(alpha = 0.5f),
                    primaryColor.copy(alpha = 0.3f)
                ),
                start = Offset(centerX + width * 0.2f, centerY - height * 0.3f),
                end = Offset(centerX - width * 0.2f, centerY + height * 0.3f)
            )
        )

        // Decorative circles
        val numCircles = 8
        for (i in 0 until numCircles) {
            val angle = (i * 2 * Math.PI / numCircles).toFloat()
            val radius = width * 0.25f
            val x = centerX + cos(angle) * radius
            val y = centerY + sin(angle) * radius
            val circleRadius = width * 0.02f + (i % 3) * width * 0.01f

            drawCircle(
                color = when (i % 3) {
                    0 -> primaryColor.copy(alpha = 0.4f)
                    1 -> secondaryColor.copy(alpha = 0.4f)
                    else -> tertiaryColor.copy(alpha = 0.4f)
                },
                radius = circleRadius,
                center = Offset(x, y)
            )
        }

        // Central highlight
        drawCircle(
            brush = Brush.radialGradient(
                colors = listOf(
                    primaryColor.copy(alpha = 0.8f),
                    primaryColor.copy(alpha = 0.2f)
                ),
                center = Offset(centerX, centerY),
                radius = width * 0.08f
            ),
            radius = width * 0.08f,
            center = Offset(centerX, centerY)
        )
    }
}