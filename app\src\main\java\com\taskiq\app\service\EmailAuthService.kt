package com.taskiq.app.service

import android.Manifest
import android.accounts.Account
import android.accounts.AccountManager
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.util.Log
import androidx.browser.customtabs.CustomTabsIntent
import androidx.core.content.ContextCompat
import androidx.core.content.ContextCompat.startActivity
import com.taskiq.app.R
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.net.URLEncoder
import java.util.UUID

/**
 * Service to handle email authentication and verification.
 * Uses AccountManager for Google accounts and simpler verification for others.
 */
class EmailAuthService(private val context: Context) {
    
    // Map to store verified emails
    private val verifiedEmails = mutableMapOf<String, EmailAuthInfo>()
    
    // Account manager for accessing device accounts
    private val accountManager = AccountManager.get(context)
    
    companion object {
        private const val TAG = "EmailAuthService"
    }
    
    /**
     * Initiates email verification process
     * Now uses AccountManager for Google accounts
     */
    fun initiateEmailVerification(email: String, activity: Activity): Boolean {
        try {
            Log.d(TAG, "Starting email verification for: $email")

            // Check if we have permission to access accounts
            if (ContextCompat.checkSelfPermission(context, Manifest.permission.GET_ACCOUNTS)
                != PackageManager.PERMISSION_GRANTED) {
                Log.e(TAG, "No GET_ACCOUNTS permission")
                return false
            }

            Log.d(TAG, "GET_ACCOUNTS permission granted")

            // For Gmail accounts, verify directly using AccountManager
            if (email.endsWith("@gmail.com")) {
                Log.d(TAG, "Processing Gmail account: $email")

                // Check if the account exists on the device
                val googleAccounts = accountManager.getAccountsByType("com.google")
                Log.d(TAG, "Found ${googleAccounts.size} Google accounts on device")

                val accountExists = googleAccounts.any { it.name.equals(email, ignoreCase = true) }
                Log.d(TAG, "Account $email exists on device: $accountExists")

                if (accountExists) {
                    // Auto-verify Gmail accounts that exist on the device
                    addVerifiedEmail(email)
                    Log.d(TAG, "Verified Gmail account directly: $email")
                    return true
                } else {
                    Log.d(TAG, "Gmail account not found on device: $email")
                    return false
                }
            } else {
                // For non-Gmail accounts, we'll just simulate verification
                // In a real app, you'd implement proper verification
                addVerifiedEmail(email)
                Log.d(TAG, "Simulated verification for non-Gmail account: $email")
                return true
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error initiating email verification: ${e.message}", e)
            return false
        }
    }
    
    /**
     * Handle redirects (legacy method - now only used for non-Gmail accounts)
     * Maintained for backwards compatibility
     */
    fun handleOAuthRedirect(intent: Intent): String? {
        try {
            val uri = intent.data ?: return null
            
            // Log the full URI for debugging
            Log.d(TAG, "Received redirect: ${uri.toString()}")
            
            // Extract email parameter (if present)
            val email = uri.getQueryParameter("email")
            if (!email.isNullOrEmpty()) {
                addVerifiedEmail(email)
                return email
            }
            
            return null
        } catch (e: Exception) {
            Log.e(TAG, "Error handling redirect: ${e.message}")
            return null
        }
    }
    
    /**
     * Adds an email to the verified list
     */
    private fun addVerifiedEmail(email: String) {
        verifiedEmails[email] = EmailAuthInfo(
            email = email,
            accessToken = "direct_access_${UUID.randomUUID()}",
            refreshToken = "direct_refresh_${UUID.randomUUID()}",
            provider = getEmailProvider(email),
            verifiedAt = System.currentTimeMillis()
        )
    }
    
    /**
     * Checks if an email has been verified
     */
    fun isEmailVerified(email: String): Boolean {
        return verifiedEmails.containsKey(email)
    }
    
    /**
     * Gets the list of verified emails
     */
    fun getVerifiedEmails(): List<String> {
        return verifiedEmails.keys.toList()
    }
    
    /**
     * Removes a verified email
     */
    fun removeVerifiedEmail(email: String): Boolean {
        return verifiedEmails.remove(email) != null
    }
    
    /**
     * Determines the email provider based on the email address
     */
    private fun getEmailProvider(email: String): String {
        return when {
            email.endsWith("@gmail.com") -> "gmail"
            email.endsWith("@outlook.com") || email.endsWith("@hotmail.com") -> "outlook"
            email.endsWith("@yahoo.com") -> "yahoo"
            else -> "other"
        }
    }
    
    /**
     * Simulates scanning emails for bills
     */
    fun scanEmailForBills(email: String): Boolean {
        if (!isEmailVerified(email)) {
            Log.e(TAG, "Cannot scan unverified email: $email")
            return false
        }
        
        // In a real app, this would use the Gmail API or other email APIs
        Log.d(TAG, "Scanning verified email: $email")
        return true
    }
    
    /**
     * Gets all Gmail accounts available on the device
     */
    fun getDeviceGmailAccounts(): List<String> {
        try {
            Log.d(TAG, "=== GETTING DEVICE GMAIL ACCOUNTS ===")

            if (ContextCompat.checkSelfPermission(context, Manifest.permission.GET_ACCOUNTS)
                == PackageManager.PERMISSION_GRANTED) {

                Log.d(TAG, "GET_ACCOUNTS permission granted")

                // Try multiple account types to find Google/Gmail accounts
                val accountTypes = listOf("com.google", "com.google.android.gm", "com.google.android.gsf")
                var accounts = emptyArray<Account>()

                for (accountType in accountTypes) {
                    Log.d(TAG, "Trying account type: $accountType")
                    val typeAccounts = accountManager.getAccountsByType(accountType)
                    Log.d(TAG, "Found ${typeAccounts.size} accounts with type '$accountType'")

                    typeAccounts.forEach { account ->
                        Log.d(TAG, "Account: ${account.name} (type: ${account.type})")
                    }

                    if (typeAccounts.isNotEmpty()) {
                        accounts = typeAccounts
                        Log.d(TAG, "Using accounts from type: $accountType")
                        break
                    }
                }

                // If still no accounts found, try searching all accounts for Gmail addresses
                if (accounts.isEmpty()) {
                    Log.d(TAG, "No Google accounts found with specific types, searching all accounts")
                    try {
                        val allAccounts = accountManager.accounts
                        Log.d(TAG, "=== ALL DEVICE ACCOUNTS ===")
                        val gmailAccounts = mutableListOf<Account>()

                        allAccounts.forEach { account ->
                            Log.d(TAG, "Account: ${account.name} (type: ${account.type})")
                            // Look for Gmail addresses in any account type
                            if (account.name.contains("@gmail.com", ignoreCase = true)) {
                                gmailAccounts.add(account)
                                Log.d(TAG, "Found Gmail account: ${account.name}")
                            }
                        }
                        Log.d(TAG, "=== END ALL ACCOUNTS ===")

                        if (gmailAccounts.isNotEmpty()) {
                            accounts = gmailAccounts.toTypedArray()
                            Log.d(TAG, "Using ${gmailAccounts.size} Gmail accounts found in all accounts")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error getting all accounts: ${e.message}")
                    }
                }

                val emailAccounts = accounts.map { it.name }.filter { it.contains("@") }
                Log.d(TAG, "Filtered email accounts: $emailAccounts")
                Log.d(TAG, "=== RETURNING ${emailAccounts.size} GMAIL ACCOUNTS ===")

                return emailAccounts
            } else {
                Log.e(TAG, "GET_ACCOUNTS permission not granted")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting device Gmail accounts: ${e.message}", e)
        }

        Log.d(TAG, "Returning empty list")
        return emptyList()
    }
}

/**
 * Data class to store email authentication information
 */
data class EmailAuthInfo(
    val email: String,
    val accessToken: String,
    val refreshToken: String,
    val provider: String,
    val verifiedAt: Long
)