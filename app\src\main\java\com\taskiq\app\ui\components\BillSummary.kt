package com.taskiq.app.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.Alignment
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.taskiq.app.model.Bill
import com.taskiq.app.model.BillStatus
import java.time.LocalDate

@Composable
fun BillSummary(bills: List<Bill>, modifier: Modifier = Modifier) {
    val today = LocalDate.now()
    
    // Calculate summary statistics
    val totalBills = bills.size
    val paidBills = bills.count { it.status == BillStatus.PAID }
    val pendingBills = bills.count { it.status == BillStatus.PENDING }
    val overdueBills = bills.count { 
        it.status == BillStatus.OVERDUE || 
        (it.status == BillStatus.PENDING && it.dueDate.isBefore(today))
    }
    
    Surface(
        modifier = modifier.fillMaxWidth(),
        color = MaterialTheme.colorScheme.surface,
        tonalElevation = 1.dp
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            SummaryItem(
                label = "Total",
                value = totalBills.toString(),
                color = MaterialTheme.colorScheme.primary
            )
            
            SummaryItem(
                label = "Paid",
                value = paidBills.toString(),
                color = Color.Green
            )
            
            SummaryItem(
                label = "Pending",
                value = pendingBills.toString(),
                color = Color(0xFF2196F3) // Blue
            )
            
            SummaryItem(
                label = "Overdue",
                value = overdueBills.toString(),
                color = Color.Red
            )
        }
    }
}


@Composable
private fun SummaryItem(
    label: String,
    value: String,
    color: Color,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(8.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        androidx.compose.material3.Text(
            text = value,
            style = MaterialTheme.typography.headlineMedium,
            color = color
        )
        androidx.compose.material3.Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
        )
    }
}