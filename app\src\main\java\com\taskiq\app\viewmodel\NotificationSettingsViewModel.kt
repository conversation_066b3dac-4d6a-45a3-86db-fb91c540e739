package com.taskiq.app.viewmodel

import android.app.Application
import android.content.Context
import androidx.lifecycle.AndroidViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

data class NotificationPreferences(
    val taskReminders: Boolean = true,
    val billDueDates: Boolean = true,
    val importantDates: Boolean = true,
    val systemNotifications: Boolean = true,
    val dailySummaryEnabled: Boolean = true,
    val dailySummaryHour: Int = 8, // Default to 8 AM
    val dailySummaryMinute: Int = 0 // Default to 0 minutes
)

class NotificationSettingsViewModel(application: Application) : AndroidViewModel(application) {
    private val sharedPreferences = application.getSharedPreferences("notification_preferences", Context.MODE_PRIVATE)
    
    private val _preferences = MutableStateFlow(
        NotificationPreferences(
            taskReminders = sharedPreferences.getBoolean("taskReminders", true),
            billDueDates = sharedPreferences.getBoolean("billDueDates", true),
            importantDates = sharedPreferences.getBoolean("importantDates", true),
            systemNotifications = sharedPreferences.getBoolean("systemNotifications", true),
            dailySummaryEnabled = sharedPreferences.getBoolean("dailySummaryEnabled", true),
            dailySummaryHour = sharedPreferences.getInt("dailySummaryHour", 8),
            dailySummaryMinute = sharedPreferences.getInt("dailySummaryMinute", 0)
        )
    )
    val preferences: StateFlow<NotificationPreferences> = _preferences.asStateFlow()

    fun updateTaskReminders(enabled: Boolean) {
        _preferences.value = _preferences.value.copy(taskReminders = enabled)
        savePreference("taskReminders", enabled)
    }

    fun updateBillDueDates(enabled: Boolean) {
        _preferences.value = _preferences.value.copy(billDueDates = enabled)
        savePreference("billDueDates", enabled)
    }

    fun updateImportantDates(enabled: Boolean) {
        _preferences.value = _preferences.value.copy(importantDates = enabled)
        savePreference("importantDates", enabled)
    }

    fun updateSystemNotifications(enabled: Boolean) {
        _preferences.value = _preferences.value.copy(systemNotifications = enabled)
        savePreference("systemNotifications", enabled)
    }

    fun updateDailySummaryEnabled(enabled: Boolean) {
        _preferences.value = _preferences.value.copy(dailySummaryEnabled = enabled)
        savePreference("dailySummaryEnabled", enabled)
    }

    fun updateDailySummaryTime(hour: Int, minute: Int) {
        _preferences.value = _preferences.value.copy(
            dailySummaryHour = hour,
            dailySummaryMinute = minute
        )
        savePreference("dailySummaryHour", hour)
        savePreference("dailySummaryMinute", minute)
    }
    
    private fun savePreference(key: String, value: Boolean) {
        sharedPreferences.edit().putBoolean(key, value).apply()
    }

    private fun savePreference(key: String, value: Int) {
        sharedPreferences.edit().putInt(key, value).apply()
    }
}