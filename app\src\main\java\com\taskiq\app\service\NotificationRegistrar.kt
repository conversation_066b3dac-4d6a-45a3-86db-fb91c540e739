package com.taskiq.app.service

import android.content.Context
import android.content.Intent
import android.util.Log
import com.taskiq.app.model.Task
import com.taskiq.app.model.TaskPriority
import com.taskiq.app.service.TaskService
import com.taskiq.app.service.BillService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.Date
import java.util.concurrent.TimeUnit

/**
 * Service responsible for registering all pending notifications at app startup
 */
class NotificationRegistrar(private val context: Context) {
    
    private val TAG = "NotificationRegistrar"
    private val notificationService = NotificationService(context)
    private val taskService = TaskService(context)
    private val billService = BillService(context)
    private val coroutineScope = CoroutineScope(Dispatchers.IO)
    
    // Store a flag to prevent duplicate test notifications
    private val PREF_NAME = "notification_registrar_prefs"
    private val KEY_FIRST_RUN = "first_run_completed"
    
    /**
     * Register all pending notifications at app startup
     */
    fun registerAllPendingNotifications() {
        Log.d(TAG, "Registering all pending notifications")
        
        coroutineScope.launch {
            try {
                // First cancel any existing notifications to prevent duplicates
                cancelAllExistingNotifications()
                
                // Register high priority task notifications
                registerHighPriorityTaskNotifications()
                
                // Register pending bill notifications
                registerPendingBillNotifications()
                
                // Register important date notifications
                registerImportantDateNotifications()
                
                Log.d(TAG, "Completed registering all pending notifications")
            } catch (e: Exception) {
                Log.e(TAG, "Error registering notifications: ${e.message}", e)
            }
        }
    }
    
    private fun cancelAllExistingNotifications() {
        Log.d(TAG, "Cancelling all existing notifications")
        // This just clears the notification tray, not scheduled notifications
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager
        notificationManager.cancelAll()
    }
    
    private suspend fun registerHighPriorityTaskNotifications() {
        Log.d(TAG, "Registering high priority task notifications")
        
        val tasks = taskService.getTasks()
        val highPriorityTasks = tasks.filter { 
            // Only include high priority tasks that are not completed
            (it.priority == TaskPriority.HIGH && !it.isCompleted)
        }
        
        Log.d(TAG, "Found ${highPriorityTasks.size} high priority tasks")
        
        // Schedule notifications for each high priority task at their scheduled due time
        highPriorityTasks.forEach { task ->
            if (task.dueDate != null) {
                // Check if the due date is in the past
                val isPastDue = task.dueDate.before(Date())
                
                if (isPastDue) {
                    Log.d(TAG, "Task due date is in the past, skipping notification: ${task.title} - Due date: ${formatDate(task.dueDate)}")
                    return@forEach
                }
                
                // Regular high priority task handling
                if (task.reminderTime != null) {
                    Log.d(TAG, "Task has reminder time set: ${task.title} - Due date: ${formatDate(task.dueDate)} - Reminder time available")
                    notificationService.scheduleTaskReminder(task)
                } else {
                    // Create a default reminder time (8 AM on due date)
                    val calendar = java.util.Calendar.getInstance()
                    calendar.time = task.dueDate
                    calendar.set(java.util.Calendar.HOUR_OF_DAY, 8)
                    calendar.set(java.util.Calendar.MINUTE, 0)
                    
                    Log.d(TAG, "Creating default reminder time (8 AM) for task: ${task.title}")
                    
                    // Create a copy of the task with the reminder time set
                    val taskWithReminder = task.copy(reminderTime = calendar.time)
                    notificationService.scheduleTaskReminder(taskWithReminder)
                }
            } else {
                Log.d(TAG, "Task has no due date, skipping notification: ${task.title}")
            }
        }
    }
    
    private suspend fun registerPendingBillNotifications() {
        Log.d(TAG, "Registering pending bill notifications")
        
        val bills = billService.getBills()
        val pendingBills = bills.filter { !it.isPaid }
        
        Log.d(TAG, "Found ${pendingBills.size} pending bills")
        
        // Cancel any existing bill notifications
        bills.forEach { bill ->
            notificationService.cancelBillReminder(bill.id)
        }
        // Cancel any existing daily bill summary
        notificationService.cancelDailyBillSummary()
        
        // Schedule a daily summary notification at 8 AM instead of individual bills
        if (pendingBills.isNotEmpty()) {
            Log.d(TAG, "Scheduling daily bill summary for ${pendingBills.size} pending bills")
            // Force using 8 AM for all bill notifications
            notificationService.scheduleDailyBillSummary(pendingBills)
        }
    }
    
    private suspend fun registerImportantDateNotifications() {
        Log.d(TAG, "Registering important date notifications")
        
        val importantDates = taskService.getImportantDates()
        
        Log.d(TAG, "Found ${importantDates.size} important dates")
        
        // Schedule notifications for each important date
        importantDates.forEach { date ->
            if (date.dueDate != null) {
                // Check if the due date is in the past
                val isPastDue = date.dueDate.before(Date())
                
                if (isPastDue) {
                    Log.d(TAG, "Important date is in the past, skipping notification: ${date.title} - Due date: ${formatDate(date.dueDate)}")
                    return@forEach
                }
                
                Log.d(TAG, "Processing important date: ${date.title} - Due date: ${formatDate(date.dueDate)}")
                
                // Always create a reminder time for dates at 8 AM (as per requirements)
                val calendar = java.util.Calendar.getInstance()
                calendar.time = date.dueDate
                calendar.set(java.util.Calendar.HOUR_OF_DAY, 8) // Use 8 AM instead of 9 AM
                calendar.set(java.util.Calendar.MINUTE, 0)
                
                Log.d(TAG, "Creating default reminder time (8 AM) for important date: ${date.title}")
                
                // Create a copy of the date with the reminder time set
                val dateWithReminder = date.copy(reminderTime = calendar.time)
                notificationService.scheduleImportantDateReminder(dateWithReminder)
            } else {
                Log.d(TAG, "Important date has no due date, skipping notification: ${date.title}")
            }
        }
    }
    
    private fun showImmediateTaskNotification(task: Task) {
        Log.d(TAG, "Showing immediate notification for high priority task: ${task.title}")
        
        val intent = android.content.Intent(context, com.taskiq.app.MainActivity::class.java)
        val pendingIntent = android.app.PendingIntent.getActivity(
            context, 
            task.id.hashCode(),
            intent, 
            android.app.PendingIntent.FLAG_UPDATE_CURRENT or android.app.PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = androidx.core.app.NotificationCompat.Builder(context, NotificationService.CHANNEL_ID_TASKS)
            .setSmallIcon(com.taskiq.app.R.drawable.ic_notification)
            .setContentTitle("HIGH PRIORITY: ${task.title}")
            .setContentText("Due: ${formatDate(task.dueDate)}")
            .setPriority(androidx.core.app.NotificationCompat.PRIORITY_HIGH)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .build()
        
        val notificationManager = androidx.core.app.NotificationManagerCompat.from(context)
        try {
            notificationManager.notify(task.id.hashCode(), notification)
        } catch (e: SecurityException) {
            Log.e(TAG, "Permission denied for showing notification", e)
        }
    }
    
    private fun showImmediateBillNotification(bill: com.taskiq.app.model.Bill) {
        Log.d(TAG, "Showing immediate notification for pending bill: ${bill.title}")
        
        val intent = android.content.Intent(context, com.taskiq.app.MainActivity::class.java)
        val pendingIntent = android.app.PendingIntent.getActivity(
            context, 
            bill.id.hashCode(),
            intent, 
            android.app.PendingIntent.FLAG_UPDATE_CURRENT or android.app.PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = androidx.core.app.NotificationCompat.Builder(context, NotificationService.CHANNEL_ID_TASKS)
            .setSmallIcon(com.taskiq.app.R.drawable.ic_notification)
            .setContentTitle("Bill Due: ${bill.title}")
            .setContentText("Amount: $${String.format("%.2f", bill.amount)} - Due: ${bill.dueDate}")
            .setPriority(androidx.core.app.NotificationCompat.PRIORITY_HIGH)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .build()
        
        val notificationManager = androidx.core.app.NotificationManagerCompat.from(context)
        try {
            notificationManager.notify(bill.id.hashCode(), notification)
        } catch (e: SecurityException) {
            Log.e(TAG, "Permission denied for showing notification", e)
        }
    }
    
    private fun showImmediateDateNotification(date: Task) {
        Log.d(TAG, "Showing immediate notification for important date: ${date.title}")
        
        val intent = android.content.Intent(context, com.taskiq.app.MainActivity::class.java)
        val pendingIntent = android.app.PendingIntent.getActivity(
            context, 
            date.id.hashCode(),
            intent, 
            android.app.PendingIntent.FLAG_UPDATE_CURRENT or android.app.PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = androidx.core.app.NotificationCompat.Builder(context, NotificationService.CHANNEL_ID_IMPORTANT_DATES)
            .setSmallIcon(com.taskiq.app.R.drawable.ic_notification)
            .setContentTitle("Important Date: ${date.title}")
            .setContentText("Date: ${formatDate(date.dueDate)}")
            .setPriority(androidx.core.app.NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .build()
        
        val notificationManager = androidx.core.app.NotificationManagerCompat.from(context)
        try {
            notificationManager.notify(date.id.hashCode(), notification)
        } catch (e: SecurityException) {
            Log.e(TAG, "Permission denied for showing notification", e)
        }
    }
    
    private fun formatDate(date: Date?): String {
        return date?.let {
            java.text.SimpleDateFormat("MMM dd, yyyy", java.util.Locale.getDefault()).format(it)
        } ?: "No due date"
    }
    
    // Send a test notification to verify the notification system works
    // This method exists but is no longer called from registerAllPendingNotifications()
    private fun sendTestNotification() {
        Log.d(TAG, "Sending test notification to verify the system works")
        
        try {
            val intent = Intent(context, NotificationReceiver::class.java).apply {
                putExtra(NotificationReceiver.EXTRA_NOTIFICATION_ID, 9999)
                putExtra(NotificationReceiver.EXTRA_CHANNEL_ID, NotificationService.CHANNEL_ID_TASKS)
                putExtra(NotificationReceiver.EXTRA_REQUEST_CODE, 9999)
                putExtra(NotificationReceiver.EXTRA_TITLE, "TaskIQ Test")
                putExtra(NotificationReceiver.EXTRA_CONTENT_TEXT, "This test notification confirms that notifications are working")
                putExtra(NotificationReceiver.EXTRA_BIG_TEXT, "If you see this in your notification history, the notification system is working correctly.")
            }
            
            // Use BroadcastReceiver to deliver immediately
            context.sendBroadcast(intent)
            
            Log.d(TAG, "Test notification sent")
        } catch (e: Exception) {
            Log.e(TAG, "Error sending test notification: ${e.message}", e)
        }
    }
    
    /**
     * Register notification for a single newly added task
     * This method should be called whenever a new task is created
     */
    fun registerTaskNotification(task: Task) {
        Log.d(TAG, "Registering notification for newly added task: ${task.title}")
        
        coroutineScope.launch {
            try {
                // Only register if they are high priority and not completed
                val isHighPriorityTask = task.priority == TaskPriority.HIGH && !task.isCompleted
                
                if (isHighPriorityTask) {
                    if (task.dueDate != null) {
                        // Check if the due date is in the past
                        val isPastDue = task.dueDate.before(Date())
                        
                        if (isPastDue) {
                            Log.d(TAG, "Task due date is in the past, skipping notification: ${task.title} - Due date: ${formatDate(task.dueDate)}")
                            return@launch
                        }
                        
                        // Regular high priority task handling
                        if (task.reminderTime != null) {
                            Log.d(TAG, "Task has reminder time set: ${task.title} - Due date: ${formatDate(task.dueDate)} - Reminder time available")
                            notificationService.scheduleTaskReminder(task)
                        } else {
                            // Create a default reminder time (8 AM on due date)
                            val calendar = java.util.Calendar.getInstance()
                            calendar.time = task.dueDate
                            calendar.set(java.util.Calendar.HOUR_OF_DAY, 8)
                            calendar.set(java.util.Calendar.MINUTE, 0)
                            
                            Log.d(TAG, "Creating default reminder time (8 AM) for task: ${task.title}")
                            
                            // Create a copy of the task with the reminder time set
                            val taskWithReminder = task.copy(reminderTime = calendar.time)
                            notificationService.scheduleTaskReminder(taskWithReminder)
                        }
                    } else {
                        Log.d(TAG, "Task has no due date, skipping notification: ${task.title}")
                    }
                } else {
                    Log.d(TAG, "Task is not high priority, skipping: ${task.title}")
                }
                
                Log.d(TAG, "Completed registering notification for newly added task")
            } catch (e: Exception) {
                Log.e(TAG, "Error registering notification for task: ${e.message}", e)
            }
        }
    }
} 