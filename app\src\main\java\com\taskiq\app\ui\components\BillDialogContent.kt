package com.taskiq.app.ui.components

import android.content.Intent
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Share
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

import com.taskiq.app.model.Bill
import com.taskiq.app.viewmodel.TaskViewModel

import com.taskiq.app.ui.theme.ProfessionalBlue
import com.taskiq.app.ui.theme.ProfessionalWhite
import com.taskiq.app.ui.theme.standardHorizontalPadding
import com.taskiq.app.ui.theme.topAppBarColor
import com.taskiq.app.ui.theme.uniformVerticalPadding
import com.taskiq.app.ui.theme.sdp
import com.taskiq.app.ui.theme.ssp

/**
 * A standalone composable for displaying the bill dialog content.
 * This is used for both adding and editing bills.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BillDialogContent(
    editingBill: Bill? = null,
    onDismiss: () -> Unit,
    onBillAdded: (Bill) -> Unit,
    viewModel: TaskViewModel
) {
    val context = LocalContext.current
    var showDuplicateWarning = remember { mutableStateOf(false) }

    // Handle system back button
    BackHandler {
        onDismiss()
    }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Custom top app bar
        TopAppBar(
            title = {
                Text(
                    text = if (editingBill != null) "Edit Bill" else "Add Bill",
                    fontWeight = FontWeight.Bold,
                    fontSize = 18.ssp(),
                    color = ProfessionalWhite
                )
            },
            navigationIcon = {
                IconButton(
                    onClick = onDismiss,
                    modifier = Modifier.padding(end = uniformVerticalPadding())
                ) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                        contentDescription = "Back",
                        tint = ProfessionalWhite
                    )
                }
            },
            actions = {
                if (editingBill != null) {
                    // Share icon - only show for editing mode
                    IconButton(onClick = {
                        val shareIntent = Intent(Intent.ACTION_SEND).apply {
                            type = "text/plain"
                            val shareText = """
                                Bill Details:
                                Title: ${editingBill.title}
                                Amount: $${editingBill.amount}
                                Due Date: ${editingBill.dueDate}
                                Type: ${editingBill.type.name.replace("_", " ")}
                                Status: ${editingBill.status}
                            """.trimIndent()
                            putExtra(Intent.EXTRA_TEXT, shareText)
                        }
                        context.startActivity(Intent.createChooser(shareIntent, "Share Bill Details"))
                    }) {
                        Icon(
                            imageVector = Icons.Default.Share,
                            contentDescription = "Share Bill",
                            tint = ProfessionalWhite
                        )
                    }
                    
                    // Delete icon - already present but moved after share
                    IconButton(onClick = { 
                        editingBill.let { bill ->
                            viewModel.deleteBill(bill.id)
                            onDismiss()
                        }
                    }) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "Delete Bill",
                            tint = ProfessionalWhite
                        )
                    }
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = topAppBarColor(),
                titleContentColor = ProfessionalWhite,
                navigationIconContentColor = ProfessionalWhite,
                actionIconContentColor = ProfessionalWhite
            ),
            windowInsets = WindowInsets.statusBars
        )
        
        // Bill dialog content
        AddBillDialog(
            onDismiss = onDismiss,
            onBillAdded = { bill ->
                // Check for duplicate bill with more comprehensive comparison
                val isDuplicate = viewModel.bills.value.any { existingBill ->
                    existingBill.id != bill.id && 
                    existingBill.title.equals(bill.title, ignoreCase = true) &&
                    existingBill.amount == bill.amount &&
                    existingBill.dueDate.equals(bill.dueDate) &&
                    existingBill.type == bill.type
                }
                
                if (isDuplicate && editingBill == null) {
                    // Show duplicate warning
                    showDuplicateWarning.value = true
                } else {
                    // Add/update bill normally
                    onBillAdded(bill)
                }
            },
            existingBill = editingBill,
            isFullScreen = true,
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = standardHorizontalPadding(), vertical = 16.sdp())
        )
        
        // Show duplicate warning message if needed
        if (showDuplicateWarning.value) {
            Text(
                text = "A bill with the same title, amount, due date, and type already exists.",
                color = MaterialTheme.colorScheme.error,
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier
                    .fillMaxWidth()
                    .background(MaterialTheme.colorScheme.errorContainer)
                    .padding(16.sdp())
            )
        }
    }
}