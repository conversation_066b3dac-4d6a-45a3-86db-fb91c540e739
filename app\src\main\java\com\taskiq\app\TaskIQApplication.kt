package com.taskiq.app

import android.app.Application
import android.content.Context
import android.util.Log
import com.taskiq.app.service.NotificationRegistrar
import com.taskiq.app.service.NotificationService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class TaskIQApplication : Application() {

    companion object {
        private const val TAG = "TaskIQApp"
        lateinit var instance: TaskIQApplication
            private set
        
        // Convenience function to access notification service from anywhere
        fun getNotificationService(): NotificationService {
            return instance.notificationService
        }
    }
    
    // Initialize notification service as a singleton
    lateinit var notificationService: NotificationService
        private set
    
    // Initialize notification registrar
    private lateinit var notificationRegistrar: NotificationRegistrar
    
    // Coroutine scope for background tasks
    private val coroutineScope = CoroutineScope(Dispatchers.IO)
    
    override fun onCreate() {
        super.onCreate()
        instance = this
        
        // Initialize notification service
        notificationService = NotificationService(this)
        
        // Create notification channels
        notificationService.createNotificationChannels()
        
        // Ensure notification preferences are set to true by default
        resetNotificationPreferencesIfNeeded()
        
        // Initialize notification registrar
        notificationRegistrar = NotificationRegistrar(this)
        
        // Register all pending notifications in a coroutine
        coroutineScope.launch {
            try {
                // Register all pending notifications
                registerPendingNotifications()

                // Schedule daily summary notifications
                notificationService.scheduleDailySummary()

                Log.d(TAG, "Successfully registered pending notifications and daily summary")
            } catch (e: Exception) {
                Log.e(TAG, "Error registering pending notifications: ${e.message}", e)
            }
        }
        
        Log.d(TAG, "Application initialized with notification service")
    }
    
    private fun registerPendingNotifications() {
        Log.d(TAG, "Registering pending notifications")
        notificationRegistrar.registerAllPendingNotifications()
    }
    
    private fun resetNotificationPreferencesIfNeeded() {
        val sharedPrefs = getSharedPreferences("notification_preferences", Context.MODE_PRIVATE)
        val editor = sharedPrefs.edit()
        
        if (!sharedPrefs.contains("taskReminders")) {
            editor.putBoolean("taskReminders", true)
        }
        
        if (!sharedPrefs.contains("billDueDates")) {
            editor.putBoolean("billDueDates", true)
        }
        
        if (!sharedPrefs.contains("importantDates")) {
            editor.putBoolean("importantDates", true)
        }
        
        if (!sharedPrefs.contains("systemNotifications")) {
            editor.putBoolean("systemNotifications", true)
        }

        if (!sharedPrefs.contains("dailySummaryEnabled")) {
            editor.putBoolean("dailySummaryEnabled", true)
        }

        if (!sharedPrefs.contains("dailySummaryHour")) {
            editor.putInt("dailySummaryHour", 8)
        }

        if (!sharedPrefs.contains("dailySummaryMinute")) {
            editor.putInt("dailySummaryMinute", 0)
        }

        editor.apply()
    }
} 
