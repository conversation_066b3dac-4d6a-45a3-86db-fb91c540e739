package com.taskiq.app.model

import java.util.Date
import java.util.UUID
import java.util.Calendar

data class Task(
    val id: String = UUID.randomUUID().toString(),
    val title: String,
    val description: String = "",
    val dueDate: Date? = null,
    val frequency: TaskFrequency = TaskFrequency.ONE_TIME,
    val priority: TaskPriority = TaskPriority.MEDIUM,
    val isCompleted: Boolean = false,
    val userId: String,
    val createdAt: Date = Date(),
    val completedAt: Date? = null,
    val reminderTime: Date? = null,
    val subtasks: List<Subtask> = emptyList(),
    val isTemplate: Boolean = false,
    val templateId: String? = null,
    val customFrequencyDays: Int? = null,
    val customFrequencyHours: Int? = null,
    val completionColor: String? = null // Store the color at time of completion
) {
    // Helper function to determine if a task is overdue
    fun isOverdue(): Bo<PERSON>an {
        if (isCompleted) return false  // Completed tasks are not overdue
        
        val now = System.currentTimeMillis()
        return dueDate != null && dueDate.time < now
    }
    
    // Helper function to check if a task is a recurring one
    fun isRecurring(): Boolean {
        return frequency != TaskFrequency.ONE_TIME
    }

    // Helper function to check if this is an overdue recurring task
    fun isOverdueRecurring(): Boolean {
        return isOverdue() && isRecurring()
    }
    
    // Calculate the next occurrence date based on the frequency
    fun getNextOccurrenceDate(): Date? {
        if (dueDate == null || !isRecurring()) {
            return null // Non-recurring tasks don't have next occurrences
        }
        
        val calendar = Calendar.getInstance()
        
        // If the task is overdue, calculate from current date instead of past due date
        if (isOverdue()) {
            calendar.time = Date() // Start from now for overdue tasks
        } else {
            calendar.time = dueDate // Start from the due date for future tasks
        }
        
        // Add time based on frequency
        when (frequency) {
            TaskFrequency.HOURLY -> calendar.add(Calendar.HOUR, 1)
            TaskFrequency.DAILY -> calendar.add(Calendar.DAY_OF_YEAR, 1)
            TaskFrequency.WEEKLY -> calendar.add(Calendar.WEEK_OF_YEAR, 1)
            TaskFrequency.MONTHLY -> calendar.add(Calendar.MONTH, 1)
            TaskFrequency.YEARLY -> calendar.add(Calendar.YEAR, 1)
            TaskFrequency.CUSTOM -> {
                // Add custom days and hours if defined
                if (customFrequencyDays != null && customFrequencyDays > 0) {
                    calendar.add(Calendar.DAY_OF_YEAR, customFrequencyDays)
                }
                if (customFrequencyHours != null && customFrequencyHours > 0) {
                    calendar.add(Calendar.HOUR, customFrequencyHours)
                }
            }
            else -> return null // Other frequencies (like ONE_TIME) don't have next occurrences
        }
        
        return calendar.time
    }
    
    // Generate a new task instance for the next occurrence
    fun generateNextOccurrence(): Task {
        val nextDate = getNextOccurrenceDate() ?: return this
        
        // Create a new task for the next occurrence with a new ID
        return this.copy(
            id = UUID.randomUUID().toString(),
            dueDate = nextDate,
            isCompleted = false,
            completedAt = null,
            createdAt = Date() // Set creation time to now
        )
    }
}

enum class TaskPriority {
    LOW,
    MEDIUM,
    HIGH;
}