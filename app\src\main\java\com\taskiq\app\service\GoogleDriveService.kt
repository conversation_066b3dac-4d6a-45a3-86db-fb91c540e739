package com.taskiq.app.service

import android.content.Context
import android.util.Log
import com.google.api.client.googleapis.extensions.android.gms.auth.GoogleAccountCredential
import com.google.api.client.http.FileContent
import com.google.api.client.http.javanet.NetHttpTransport
import com.google.api.client.json.gson.GsonFactory
import com.google.api.services.drive.Drive
import com.google.api.services.drive.DriveScopes
import com.google.api.services.drive.model.File
import com.google.api.services.drive.model.FileList
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream
import java.io.FileOutputStream
import java.util.Collections

/**
 * Service for handling Google Drive operations
 */
class GoogleDriveService(
    private val context: Context,
    private val credential: GoogleAccountCredential
) {
    private val TAG = "GoogleDriveService"
    
    private val driveService: Drive by lazy {
        Drive.Builder(
            NetHttpTransport(),
            GsonFactory.getDefaultInstance(),
            credential
        )
        .setApplicationName("TaskIQ")
        .build()
    }
    
    /**
     * Upload a backup file to Google Drive
     */
    suspend fun uploadBackup(backupFile: java.io.File): DriveResult<String> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "=== STARTING GOOGLE DRIVE BACKUP UPLOAD ===")
            Log.d(TAG, "Backup file: ${backupFile.name}, size: ${backupFile.length()} bytes")
            Log.d(TAG, "Selected account: ${credential.selectedAccountName}")

            // Check if credential has an account selected
            if (credential.selectedAccountName == null) {
                Log.e(TAG, "No account selected in credential")
                return@withContext DriveResult.Error("No Google account authenticated. Please authenticate through Gmail Auth screen first.")
            }

            // Create file metadata
            val fileMetadata = File().apply {
                name = "TaskIQ_Backup_${System.currentTimeMillis()}.zip"
                parents = Collections.singletonList(getOrCreateBackupFolder())
            }

            // Create file content
            val mediaContent = FileContent("application/zip", backupFile)

            Log.d(TAG, "Uploading backup to Google Drive...")

            // Upload the file
            val uploadedFile = driveService.files()
                .create(fileMetadata, mediaContent)
                .setFields("id,name,size,createdTime")
                .execute()

            Log.d(TAG, "=== GOOGLE DRIVE BACKUP UPLOAD COMPLETED ===")
            Log.d(TAG, "Uploaded file ID: ${uploadedFile.id}")
            Log.d(TAG, "Uploaded file name: ${uploadedFile.name}")
            Log.d(TAG, "Uploaded file size: ${uploadedFile.getSize()} bytes")

            DriveResult.Success(uploadedFile.id)

        } catch (e: Exception) {
            Log.e(TAG, "Error uploading backup to Google Drive: ${e.message}", e)

            // Provide more specific error messages
            val errorMessage = when {
                e.message?.contains("401") == true || e.message?.contains("Unauthorized") == true ->
                    "Authentication failed. Please authenticate through Gmail Auth screen first."
                e.message?.contains("403") == true || e.message?.contains("Forbidden") == true ->
                    "Access denied. Please ensure Google Drive permissions are granted."
                e.message?.contains("network") == true || e.message?.contains("connection") == true ->
                    "Network error. Please check your internet connection."
                else -> "Failed to upload backup to Google Drive: ${e.message ?: "Unknown error"}"
            }

            DriveResult.Error(errorMessage)
        }
    }
    
    /**
     * List available backups from Google Drive
     */
    suspend fun listBackups(): DriveResult<List<DriveBackupInfo>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "=== LISTING GOOGLE DRIVE BACKUPS ===")
            
            val folderId = getOrCreateBackupFolder()
            
            // Query for backup files in the TaskIQ backup folder
            val query = "'$folderId' in parents and name contains 'TaskIQ_Backup_' and trashed=false"
            
            val result: FileList = driveService.files()
                .list()
                .setQ(query)
                .setFields("files(id,name,size,createdTime)")
                .setOrderBy("createdTime desc")
                .execute()
            
            val backups = result.files.map { file ->
                DriveBackupInfo(
                    id = file.id,
                    name = file.name,
                    size = file.getSize() ?: 0L,
                    createdTime = file.createdTime?.value ?: 0L
                )
            }
            
            Log.d(TAG, "Found ${backups.size} backups in Google Drive")
            backups.forEach { backup ->
                Log.d(TAG, "Backup: ${backup.name}, size: ${backup.size} bytes")
            }
            
            DriveResult.Success(backups)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error listing backups from Google Drive: ${e.message}", e)
            DriveResult.Error("Failed to list backups from Google Drive: ${e.message}")
        }
    }
    
    /**
     * Download a backup file from Google Drive
     */
    suspend fun downloadBackup(fileId: String, destinationFile: java.io.File): DriveResult<java.io.File> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "=== DOWNLOADING BACKUP FROM GOOGLE DRIVE ===")
            Log.d(TAG, "File ID: $fileId")
            Log.d(TAG, "Destination: ${destinationFile.absolutePath}")
            
            val outputStream = ByteArrayOutputStream()
            driveService.files().get(fileId).executeMediaAndDownloadTo(outputStream)
            
            // Write to destination file
            FileOutputStream(destinationFile).use { fileOut ->
                fileOut.write(outputStream.toByteArray())
            }
            
            Log.d(TAG, "=== BACKUP DOWNLOAD COMPLETED ===")
            Log.d(TAG, "Downloaded file size: ${destinationFile.length()} bytes")
            
            DriveResult.Success(destinationFile)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error downloading backup from Google Drive: ${e.message}", e)
            DriveResult.Error("Failed to download backup from Google Drive: ${e.message}")
        }
    }
    
    /**
     * Delete a backup file from Google Drive
     */
    suspend fun deleteBackup(fileId: String): DriveResult<Unit> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "=== DELETING BACKUP FROM GOOGLE DRIVE ===")
            Log.d(TAG, "File ID: $fileId")
            
            driveService.files().delete(fileId).execute()
            
            Log.d(TAG, "=== BACKUP DELETION COMPLETED ===")
            
            DriveResult.Success(Unit)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting backup from Google Drive: ${e.message}", e)
            DriveResult.Error("Failed to delete backup from Google Drive: ${e.message}")
        }
    }
    
    /**
     * Get or create the TaskIQ backup folder in Google Drive
     */
    private fun getOrCreateBackupFolder(): String {
        try {
            // First, check if the folder already exists
            val query = "name='TaskIQ Backups' and mimeType='application/vnd.google-apps.folder' and trashed=false"
            val result = driveService.files()
                .list()
                .setQ(query)
                .setFields("files(id,name)")
                .execute()
            
            if (result.files.isNotEmpty()) {
                val folderId = result.files[0].id
                Log.d(TAG, "Found existing TaskIQ Backups folder: $folderId")
                return folderId
            }
            
            // Create the folder if it doesn't exist
            Log.d(TAG, "Creating TaskIQ Backups folder in Google Drive")
            val folderMetadata = File().apply {
                name = "TaskIQ Backups"
                mimeType = "application/vnd.google-apps.folder"
            }
            
            val folder = driveService.files()
                .create(folderMetadata)
                .setFields("id")
                .execute()
            
            Log.d(TAG, "Created TaskIQ Backups folder: ${folder.id}")
            return folder.id
            
        } catch (e: Exception) {
            Log.e(TAG, "Error getting/creating backup folder: ${e.message}", e)
            throw e
        }
    }
    
    companion object {
        /**
         * Create GoogleDriveService with simplified authentication using device accounts
         */
        fun create(context: Context, accountEmail: String): GoogleDriveService {
            Log.d("GoogleDriveService", "=== CREATING GOOGLE DRIVE SERVICE ===")
            Log.d("GoogleDriveService", "Account: $accountEmail")

            // Use GoogleAccountCredential with device account authentication
            val credential = GoogleAccountCredential.usingOAuth2(
                context,
                Collections.singleton(DriveScopes.DRIVE_FILE)
            )

            // Set the selected account
            credential.selectedAccountName = accountEmail

            Log.d("GoogleDriveService", "Credential created with account: ${credential.selectedAccountName}")

            return GoogleDriveService(context, credential)
        }

        /**
         * Create GoogleDriveService with automatic account selection from device
         */
        fun createWithDeviceAccount(context: Context): GoogleDriveService? {
            try {
                Log.d("GoogleDriveService", "=== CREATING DRIVE SERVICE WITH DEVICE ACCOUNT ===")

                // Get device Google accounts
                val accountManager = android.accounts.AccountManager.get(context)
                val accounts = accountManager.getAccountsByType("com.google")

                if (accounts.isNotEmpty()) {
                    val firstAccount = accounts[0].name
                    Log.d("GoogleDriveService", "Using first device account: $firstAccount")
                    return create(context, firstAccount)
                } else {
                    Log.e("GoogleDriveService", "No Google accounts found on device")
                    return null
                }
            } catch (e: Exception) {
                Log.e("GoogleDriveService", "Error creating service with device account: ${e.message}")
                return null
            }
        }
    }
}

/**
 * Result wrapper for Google Drive operations
 */
sealed class DriveResult<out T> {
    data class Success<T>(val data: T) : DriveResult<T>()
    data class Error(val message: String) : DriveResult<Nothing>()
}

/**
 * Data class for Google Drive backup information
 */
data class DriveBackupInfo(
    val id: String,
    val name: String,
    val size: Long,
    val createdTime: Long
)
