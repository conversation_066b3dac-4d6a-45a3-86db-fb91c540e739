package com.taskiq.app.ui.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Repeat
import androidx.compose.material.icons.filled.AccessTime
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.taskiq.app.model.TaskFrequency
import com.taskiq.app.model.TaskPriority
import com.taskiq.app.model.getRepeatText
import java.text.SimpleDateFormat
import java.util.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.ui.graphics.Color
import com.taskiq.app.ui.theme.cardBackgroundColor
import com.taskiq.app.ui.theme.textFieldColors
import com.taskiq.app.ui.theme.sdp

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TaskAdditionContent(
    title: String,
    onTitleChange: (String) -> Unit,
    description: String,
    onDescriptionChange: (String) -> Unit,
    dueDate: Date,
    onDueDateChange: (Date) -> Unit,
    priority: TaskPriority,
    onPriorityChange: (TaskPriority) -> Unit,
    frequency: TaskFrequency,
    onFrequencyChange: (TaskFrequency) -> Unit,
    customDays: String,
    onCustomDaysChange: (String) -> Unit,
    customHours: String,
    onCustomHoursChange: (String) -> Unit,
    hasNoDueDate: Boolean,
    onHasNoDueDateChange: (Boolean) -> Unit,
    isTemplate: Boolean,
    onIsTemplateChange: (Boolean) -> Unit,
    showDatePicker: Boolean,
    onShowDatePickerChange: (Boolean) -> Unit,
    showTimePicker: Boolean,
    onShowTimePickerChange: (Boolean) -> Unit,
    showPriorityMenu: Boolean,
    onShowPriorityMenuChange: (Boolean) -> Unit,
    showFrequencyMenu: Boolean,
    onShowFrequencyMenuChange: (Boolean) -> Unit,
    reminderTime: Date? = null,
    onReminderTimeChange: (Date?) -> Unit = {},
    onSave: () -> Unit,
    onDismiss: () -> Unit
) {
    val dateFormatter = remember { SimpleDateFormat("E, MMM d, yyyy", Locale.getDefault()) }
    val timeFormatter = remember { SimpleDateFormat("h:mm a", Locale.getDefault()) }
    
    val datePickerState = rememberDatePickerState(
        initialSelectedDateMillis = dueDate.time
    )
    
    val calendar = remember { Calendar.getInstance() }
    val hour = calendar.get(Calendar.HOUR_OF_DAY)
    val minute = calendar.get(Calendar.MINUTE)
    val timePickerState = rememberTimePickerState(
        initialHour = hour,
        initialMinute = minute
    )
    
    // For custom repeat options
    var customRepeatDays by remember { mutableStateOf(customDays) }
    var customRepeatHours by remember { mutableStateOf(customHours) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 16.sdp),
        verticalArrangement = Arrangement.spacedBy(16.sdp)
    ) {
        // Title field with improved styling
        OutlinedTextField(
            value = title,
            onValueChange = onTitleChange,
            label = { Text("Title") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true,
            colors = textFieldColors()
        )
        
        // Description field with more height
        OutlinedTextField(
            value = description,
            onValueChange = onDescriptionChange,
            label = { Text("Description (Optional)") },
            modifier = Modifier
                .fillMaxWidth()
                .height(120.sdp),
            singleLine = false,
            maxLines = 5,
            colors = textFieldColors()
        )
        
        // No Due Date Checkbox with improved styling
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { onHasNoDueDateChange(!hasNoDueDate) }
                .padding(vertical = 8.sdp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = hasNoDueDate,
                onCheckedChange = onHasNoDueDateChange
            )
            Text(
                text = "No due date",
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier.padding(start = 8.sdp)
            )
        }
        
        // Due Date and Time (only show if hasNoDueDate is false)
        if (!hasNoDueDate) {
            // Date and Time side by side with equal heights
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.sdp)
            ) {
                // Date selection card
                Card(
                    modifier = Modifier
                        .weight(1f)
                        .height(80.sdp)
                        .clickable { onShowDatePickerChange(true) },
                    colors = CardDefaults.cardColors(
                        containerColor = Color.Transparent
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 0.sdp),
                    border = BorderStroke(1.sdp, MaterialTheme.colorScheme.outline.copy(alpha = 0.3f))
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(horizontal = 8.sdp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.DateRange,
                            contentDescription = "Date",
                            tint = MaterialTheme.colorScheme.primary
                        )
                        Spacer(modifier = Modifier.width(12.sdp))
                        Column {
                            Text(
                                text = "Due Date",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            Text(
                                text = dateFormatter.format(dueDate),
                                style = MaterialTheme.typography.bodyLarge
                            )
                        }
                    }
                }
                
                // Time selection card
                Card(
                    modifier = Modifier
                        .weight(1f)
                        .height(80.sdp)
                        .clickable { onShowTimePickerChange(true) },
                    colors = CardDefaults.cardColors(
                        containerColor = Color.Transparent
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 0.sdp),
                    border = BorderStroke(1.sdp, MaterialTheme.colorScheme.outline.copy(alpha = 0.3f))
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(horizontal = 8.sdp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.AccessTime,
                            contentDescription = "Time",
                            tint = MaterialTheme.colorScheme.primary
                        )
                        Spacer(modifier = Modifier.width(12.sdp))
                        Column {
                            Text(
                                text = "Time",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            Text(
                                text = timeFormatter.format(dueDate),
                                style = MaterialTheme.typography.bodyLarge
                            )
                        }
                    }
                }
            }
            
            // Frequency Selection with Card style
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { onShowFrequencyMenuChange(true) },
                colors = CardDefaults.cardColors(
                    containerColor = Color.Transparent
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 0.sdp),
                border = BorderStroke(1.sdp, MaterialTheme.colorScheme.outline.copy(alpha = 0.3f))
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.sdp, vertical = 16.sdp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Repeat,
                        contentDescription = "Repeat",
                        tint = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.width(16.sdp))
                    Column {
                        Text(
                            text = "Repeat",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Text(
                            text = getRepeatText(frequency, customDays.toIntOrNull(), customRepeatHours.toIntOrNull()),
                            style = MaterialTheme.typography.bodyLarge
                        )
                    }
                }
                
                DropdownMenu(
                    expanded = showFrequencyMenu,
                    onDismissRequest = { onShowFrequencyMenuChange(false) }
                ) {
                    // Simplified menu options as requested
                    listOf(
                        TaskFrequency.ONE_TIME,
                        TaskFrequency.HOURLY,
                        TaskFrequency.DAILY,
                        TaskFrequency.MONTHLY,
                        TaskFrequency.CUSTOM
                    ).forEach { frequencyOption ->
                        DropdownMenuItem(
                            text = { 
                                Text(
                                    when(frequencyOption) {
                                        TaskFrequency.ONE_TIME -> "One time"
                                        TaskFrequency.HOURLY -> "Hourly"
                                        TaskFrequency.DAILY -> "Daily"
                                        TaskFrequency.MONTHLY -> "Monthly"
                                        TaskFrequency.CUSTOM -> "Custom"
                                        else -> frequencyOption.name
                                    }
                                ) 
                            },
                            onClick = {
                                onFrequencyChange(frequencyOption)
                                onShowFrequencyMenuChange(false)
                            }
                        )
                    }
                }
            }
            
            // Custom Frequency options (only show if frequency is CUSTOM)
            AnimatedVisibility(visible = frequency == TaskFrequency.CUSTOM) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(16.sdp)
                ) {
                    // Days input
                    OutlinedTextField(
                        value = customDays,
                        onValueChange = {
                            onCustomDaysChange(it)
                            customRepeatDays = it
                        },
                        label = { Text("Days") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        modifier = Modifier.weight(1f),
                        colors = textFieldColors()
                    )

                    // Hours input
                    OutlinedTextField(
                        value = customHours,
                        onValueChange = {
                            onCustomHoursChange(it)
                            customRepeatHours = it
                        },
                        label = { Text("Hours") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        modifier = Modifier.weight(1f),
                        colors = textFieldColors()
                    )
                }
            }
        }
        
        // Priority Selection with Card style
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { onShowPriorityMenuChange(true) },
            colors = CardDefaults.cardColors(
                containerColor = Color.Transparent
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 0.sdp),
            border = BorderStroke(1.sdp, MaterialTheme.colorScheme.outline.copy(alpha = 0.3f))
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.sdp, vertical = 16.sdp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.ArrowDropDown,
                    contentDescription = "Priority",
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.width(16.sdp))
                Column {
                    Text(
                        text = "Priority",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = priority.toString(),
                        style = MaterialTheme.typography.bodyLarge
                    )
                }
            }
            
            DropdownMenu(
                expanded = showPriorityMenu,
                onDismissRequest = { onShowPriorityMenuChange(false) }
            ) {
                TaskPriority.values().forEach { priorityOption ->
                    DropdownMenuItem(
                        text = { Text(priorityOption.toString()) },
                        onClick = {
                            onPriorityChange(priorityOption)
                            onShowPriorityMenuChange(false)
                        }
                    )
                }
            }
        }

        // Action buttons with modern styling
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 16.sdp),
            horizontalArrangement = Arrangement.spacedBy(16.sdp)
        ) {
            OutlinedButton(
                onClick = onDismiss,
                modifier = Modifier.weight(1f),
                shape = MaterialTheme.shapes.small
            ) {
                Text("Cancel")
            }

            Button(
                onClick = onSave,
                enabled = title.isNotBlank(),
                modifier = Modifier.weight(1f),
                shape = MaterialTheme.shapes.small
            ) {
                Text("Save")
            }
        }
    }
    
    // DatePicker Dialog
    if (showDatePicker) {
        DatePickerDialog(
            onDismissRequest = { onShowDatePickerChange(false) },
            confirmButton = {
                TextButton(
                    onClick = {
                        datePickerState.selectedDateMillis?.let { selectedDateMillis ->
                            val newDate = Calendar.getInstance().apply {
                                timeInMillis = selectedDateMillis
                                set(Calendar.HOUR_OF_DAY, calendar.get(Calendar.HOUR_OF_DAY))
                                set(Calendar.MINUTE, calendar.get(Calendar.MINUTE))
                                set(Calendar.SECOND, 0)
                                set(Calendar.MILLISECOND, 0)
                            }
                            onDueDateChange(newDate.time)
                        }
                        onShowDatePickerChange(false)
                    }
                ) {
                    Text("OK")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { onShowDatePickerChange(false) }
                ) {
                    Text("Cancel")
                }
            }
        ) {
            DatePicker(state = datePickerState)
        }
    }
    
    // TimePicker Dialog
    if (showTimePicker) {
        Dialog(
            onDismissRequest = { onShowTimePickerChange(false) }
        ) {
            Surface(
                shape = MaterialTheme.shapes.medium,
                color = MaterialTheme.colorScheme.surface
            ) {
                Column(
                    modifier = Modifier.padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "Select Time",
                        style = MaterialTheme.typography.titleLarge
                    )
                    
                    Spacer(modifier = Modifier.height(12.sdp))

                    TimePicker(state = timePickerState)

                    Spacer(modifier = Modifier.height(12.sdp))
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.End
                    ) {
                        TextButton(
                            onClick = { onShowTimePickerChange(false) }
                        ) {
                            Text("Cancel")
                        }
                        
                        Spacer(modifier = Modifier.width(8.sdp))
                        
                        TextButton(
                            onClick = {
                                val newDate = Calendar.getInstance().apply {
                                    time = dueDate
                                    set(Calendar.HOUR_OF_DAY, timePickerState.hour)
                                    set(Calendar.MINUTE, timePickerState.minute)
                                    set(Calendar.SECOND, 0)
                                    set(Calendar.MILLISECOND, 0)
                                }
                                onDueDateChange(newDate.time)
                                onShowTimePickerChange(false)
                            }
                        ) {
                            Text("OK")
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun TimePickerDialog(
    onDismiss: () -> Unit,
    onConfirm: () -> Unit,
    content: @Composable () -> Unit
) {
    Dialog(
        onDismissRequest = onDismiss
    ) {
        Surface(
            shape = MaterialTheme.shapes.medium,
            color = MaterialTheme.colorScheme.surface
        ) {
            Column(
                modifier = Modifier.padding(16.sdp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                content()
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 24.sdp),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(
                        onClick = onDismiss
                    ) {
                        Text("Cancel")
                    }
                    TextButton(
                        onClick = onConfirm
                    ) {
                        Text("OK")
                    }
                }
            }
        }
    }
}