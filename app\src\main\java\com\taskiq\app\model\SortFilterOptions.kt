package com.taskiq.app.model

enum class TaskSortOption {
    DUE_DATE_ASC,
    DUE_DATE_DESC,
    PRIORITY_HIGH_TO_LOW,
    PRIORITY_LOW_TO_HIGH
}

enum class TaskFilterOption {
    ALL,
    INCOMPLETE,
    COMPLETED,
    <PERSON><PERSON><PERSON>_PRIORITY,
    <PERSON><PERSON>UM_PRIORITY,
    <PERSON><PERSON>_PRIORITY
}

enum class DateSortOption {
    DATE_ASC,
    DATE_DESC,
    TITLE_ASC,
    TITLE_DESC
}

enum class DateFilterOption {
    ALL,
    UPCOMING,
    PAST
} 