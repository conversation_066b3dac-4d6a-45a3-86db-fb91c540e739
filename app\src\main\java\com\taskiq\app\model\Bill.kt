package com.taskiq.app.model

import java.time.LocalDate
import java.util.Date

enum class BillType {
    CREDIT_CARD,
    UTILITY,
    LOAN_EMI,
    SUBSCRIPTION,
    INSURANCE,
    RENT,
    OTHER
}

enum class BillStatus {
    PENDING,
    PAID,
    OVERDUE
}

data class Bill(
    val id: String,
    val title: String,
    val amount: Double,
    val dueDate: LocalDate,
    val type: BillType,
    val description: String = "",
    val status: BillStatus = BillStatus.PENDING,
    val isPaid: Boolean = false,
    val paymentDate: LocalDate? = null,
    val recurringPeriod: Int = 0, // 0 = one-time, 1 = monthly, 3 = quarterly, etc.
    val userId: String,
    val createdAt: LocalDate,
    val autoDetected: Boolean = false,
    val completedAt: Date? = null
) 