package com.taskiq.app

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.Modifier
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewmodel.compose.viewModel
import com.taskiq.app.service.EmailAuthService
import com.taskiq.app.service.NotificationRegistrar
import com.taskiq.app.service.PermissionManager
import com.taskiq.app.ui.navigation.AppNavigation
import com.taskiq.app.ui.theme.TaskReminderTheme
import com.taskiq.app.ui.theme.rootContainerModifier
import com.taskiq.app.ui.theme.LocalDarkMode
import com.taskiq.app.viewmodel.AuthViewModel
import com.taskiq.app.viewmodel.TaskViewModel

class MainActivity : ComponentActivity() {
    private lateinit var emailAuthService: EmailAuthService
    private lateinit var taskViewModel: TaskViewModel
    private lateinit var notificationRegistrar: NotificationRegistrar
    private lateinit var permissionManager: PermissionManager
    
    companion object {
        private const val TAG = "MainActivity"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Initialize the email auth service
        emailAuthService = EmailAuthService(this)

        // Initialize notification registrar
        notificationRegistrar = NotificationRegistrar(this)

        // Initialize permission manager
        permissionManager = PermissionManager(this)

        // Request all required permissions on first run
        if (permissionManager.isFirstRun()) {
            Log.d(TAG, "First run detected, requesting all required permissions")
            permissionManager.requestAllRequiredPermissions(this)
            permissionManager.markPermissionsRequested()
        } else {
            // Check for exact alarm scheduling permission on subsequent runs
            checkExactAlarmPermission()
        }
        
        Log.d(TAG, "MainActivity onCreate - starting to set content with application: $application")
        
        setContent {
            val authViewModel: AuthViewModel = viewModel(factory = AuthViewModel.Factory(this))
            taskViewModel = viewModel(factory = ViewModelProvider.AndroidViewModelFactory(application))
            val isDarkMode by authViewModel.isDarkMode.collectAsState()
            val useDarkTheme = isDarkMode || isSystemInDarkTheme()
            
            Log.d(TAG, "MainActivity setContent - initializing app navigation")
            
            CompositionLocalProvider(LocalDarkMode provides isDarkMode) {
            TaskReminderTheme(darkTheme = useDarkTheme) {
                Surface(
                    modifier = rootContainerModifier,
                    color = MaterialTheme.colorScheme.background
                ) {
                    AppNavigation(authViewModel = authViewModel)
                    }
                }
            }
        }
        
        // Handle intent if activity was started from OAuth redirect
        handleIntent(intent)
    }
    
    override fun onResume() {
        super.onResume()
        // Refresh notifications when app is resumed
        refreshNotifications()

        // Ensure important dates are loaded and trigger sync check
        if (::taskViewModel.isInitialized) {
            taskViewModel.loadImportantDates()
            taskViewModel.forceSaveAllData()

            // Check for data sync from cloud on app resume
            taskViewModel.checkForCloudDataSync()
        }
    }
    
    private fun refreshNotifications() {
        Log.d(TAG, "Refreshing notifications")
        notificationRegistrar.registerAllPendingNotifications()
    }
    
    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        handleIntent(intent)
    }
    
    private fun handleIntent(intent: Intent) {
        // Check if this is a redirect from our app
        if (intent.action == Intent.ACTION_VIEW && intent.data != null) {
            val uri = intent.data
            if (uri?.scheme == "taskiq") {
                Log.d(TAG, "Received app redirect: $uri")
                
                // Extract email from parameters if available
                val email = uri.getQueryParameter("email")
                
                if (!email.isNullOrEmpty()) {
                    // Try to verify and add the email
                    if (emailAuthService.initiateEmailVerification(email, this)) {
                        taskViewModel.addVerifiedEmail(email)
                        Log.d(TAG, "Successfully verified and added email: $email")
                    } else {
                        Log.d(TAG, "Failed to verify email: $email")
                    }
                }
            }
        }
    }
    
    /**
     * Checks if the app has permission to schedule exact alarms on Android 12+
     * and shows a dialog to direct the user to system settings if needed
     */
    private fun checkExactAlarmPermission() {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
            val alarmManager = getSystemService(Context.ALARM_SERVICE) as android.app.AlarmManager
            if (!alarmManager.canScheduleExactAlarms()) {
                Log.d(TAG, "App doesn't have permission to schedule exact alarms, requesting...")
                
                // Show a dialog to direct the user to system settings
                android.app.AlertDialog.Builder(this)
                    .setTitle("Permission Required")
                    .setMessage("This app needs permission to schedule exact alarms for task notifications. Please grant this permission in settings.")
                    .setPositiveButton("Open Settings") { _, _ ->
                        // Open the system settings screen where the user can grant the permission
                        val intent = android.content.Intent(android.provider.Settings.ACTION_REQUEST_SCHEDULE_EXACT_ALARM)
                        intent.data = android.net.Uri.fromParts("package", packageName, null)
                        startActivity(intent)
                    }
                    .setNegativeButton("Cancel", null)
                    .show()
            } else {
                Log.d(TAG, "App has permission to schedule exact alarms")
            }
        }
    }
}