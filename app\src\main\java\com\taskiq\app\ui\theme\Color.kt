package com.taskiq.app.ui.theme

import androidx.compose.ui.graphics.Color
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.runtime.Composable
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.compositionLocalOf

// Light Theme
val Blue80 = Color(0xFF1E88E5)
val BlueGrey80 = Color(0xFF607D8B)
val Teal80 = Color(0xFF00796B)
val Blue50 = Color(0xFFE3F2FD)
val Grey50 = Color(0xFFF5F5F5)
val BlueGrey50 = Color(0xFFECEFF1)

// New professional colors for app bars
val ProfessionalBlue = Color(0xFF1565C0)            // Darker blue for app bar background
val ProfessionalLightBlue = Color(0xFF1976D2)       // Lighter blue variant for drawer header
val ProfessionalWhite = Color(0xFFFFFFFF)           // White for text and icons on blue background

// Dark Theme
val Blue40 = Color(0xFF2196F3)
val BlueGrey40 = Color(0xFF78909C)
val Teal40 = Color(0xFF009688)
val Grey900 = Color(0xFF212121)
val Grey800 = Color(0xFF424242)
val BlueGrey900 = Color(0xFF263238)

// Composition local for dark mode state
val LocalDarkMode = compositionLocalOf { false }

// Remove the fixed white card background color
// val CardBackgroundColor = Color.White

// Instead, add these imports and create a composable function
@Composable
fun cardBackgroundColor(): Color {
    val isDarkMode = LocalDarkMode.current
    
    return if (isDarkMode) {
        Color(0xFFFFFFFF)  // Pure white color in dark mode
    } else {
        Blue50  // Light blue background in light mode
    }
}

@Composable
fun taskItemBackgroundColor(): Color {
    val isDarkMode = LocalDarkMode.current
    
    return if (isDarkMode) {
        Color.White  // White background for task items in dark mode
    } else {
        Blue50  // Light blue background in light mode
    }
}

@Composable
fun cardTextColor(): Color {
    val isDarkMode = LocalDarkMode.current
    
    return if (isDarkMode) {
        Color(0xFF000000)  // Pure black for maximum contrast on white background
    } else {
        MaterialTheme.colorScheme.onSurface
    }
}

@Composable
fun cardSecondaryTextColor(): Color {
    val isDarkMode = LocalDarkMode.current
    
    return if (isDarkMode) {
        Color(0xFF1A1A1A)  // Dark gray for secondary text
    } else {
        MaterialTheme.colorScheme.onSurfaceVariant
    }
}

@Composable
fun cardDescriptionTextColor(): Color {
    val isDarkMode = LocalDarkMode.current
    
    return if (isDarkMode) {
        Color(0xFF333333)  // Darker gray for descriptions
    } else {
        MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
    }
}

@Composable
fun topAppBarColor(): Color {
    val isDarkMode = LocalDarkMode.current
    
    return if (isDarkMode) {
        Color.Black  // Black color in dark mode
    } else {
        ProfessionalBlue  // Blue color in light mode
    }
}