package com.taskiq.app.viewmodel

import android.app.Application
import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.taskiq.app.service.BackupService
import com.taskiq.app.service.BackupResult
import com.taskiq.app.service.EmailAuthService
import com.taskiq.app.service.GoogleDriveAuthService
import com.taskiq.app.service.AuthResult
import com.taskiq.app.service.DriveBackupInfo
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import java.util.Date

/**
 * ViewModel for managing data backup and restore operations
 */
class BackupViewModel(application: Application) : AndroidViewModel(application) {
    
    private val backupService = BackupService(application)
    private val emailAuthService = EmailAuthService(application)
    private val driveAuthService = GoogleDriveAuthService(application)
    private val sharedPreferences: SharedPreferences = application.getSharedPreferences("backup_preferences", Context.MODE_PRIVATE)
    
    // Maximum number of backups to keep
    private val MAX_BACKUPS = 3
    
    // Backup state
    private val _backupState = MutableStateFlow<BackupState>(BackupState.Idle)
    val backupState: StateFlow<BackupState> = _backupState.asStateFlow()
    
    // Last backup information
    private val _lastBackupDate = MutableStateFlow<Date?>(null)
    val lastBackupDate: StateFlow<Date?> = _lastBackupDate.asStateFlow()
    
    private val _backupSize = MutableStateFlow<Long>(0)
    val backupSize: StateFlow<Long> = _backupSize.asStateFlow()
    
    // Available backups
    private val _availableBackups = MutableStateFlow<List<BackupInfo>>(emptyList())
    val availableBackups: StateFlow<List<BackupInfo>> = _availableBackups.asStateFlow()
    
    // Device Gmail accounts
    private val _deviceGmailAccounts = MutableStateFlow<List<String>>(emptyList())
    val deviceGmailAccounts: StateFlow<List<String>> = _deviceGmailAccounts.asStateFlow()
    
    // Connected Gmail account
    private val _connectedGmailAccount = MutableStateFlow<String?>(null)
    val connectedGmailAccount: StateFlow<String?> = _connectedGmailAccount.asStateFlow()
    
    // Backup frequency
    private val _backupFrequency = MutableStateFlow("Daily")
    val backupFrequency: StateFlow<String> = _backupFrequency.asStateFlow()
    
    // Auto backup setting
    val autoBackup = mutableStateOf(true)
    
    // List of valid backup frequency options
    private val backupFrequencyOptions = listOf("Daily", "Weekly", "Monthly", "Off")
    
    // Load device Gmail accounts
    private fun loadDeviceGmailAccounts() {
        viewModelScope.launch {
            try {
                val accounts = emailAuthService.getDeviceGmailAccounts()
                _deviceGmailAccounts.value = accounts
                Log.d("BackupViewModel", "Loaded ${accounts.size} device Gmail accounts")
            } catch (e: Exception) {
                Log.e("BackupViewModel", "Error loading device Gmail accounts: ${e.message}")
                _deviceGmailAccounts.value = emptyList()
            }
        }
    }
    
    init {
        // Load backup information from BackupService
        loadBackupInformation()
        
        // Load backup settings from SharedPreferences
        loadBackupSettings()
        
        // Load device Gmail accounts
        loadDeviceGmailAccounts()
        
        // Initialize connected account from preferences
        loadConnectedAccountFromPreferences()
        _availableBackups.value = emptyList()
        
        // Check for automatic restore on new device
        checkForAutomaticRestore()
        
        // Start a background job to periodically create backups
        startAutomaticBackupSchedule()
    }
    
    // Load backup information from BackupService
    private fun loadBackupInformation() {
        try {
            Log.d("BackupViewModel", "Loading backup information from BackupService")
            
            // Load last backup date and size from BackupService
            val lastBackupDate = backupService.getLastBackupDate()
            val lastBackupSize = backupService.getLastBackupSize()
            
            _lastBackupDate.value = lastBackupDate
            _backupSize.value = lastBackupSize
            
            Log.d("BackupViewModel", "Loaded backup info - Date: $lastBackupDate, Size: $lastBackupSize")
        } catch (e: Exception) {
            Log.e("BackupViewModel", "Error loading backup information: ${e.message}")
            _lastBackupDate.value = null
            _backupSize.value = 0
        }
    }
    
    // Load backup settings from SharedPreferences
    private fun loadBackupSettings() {
        try {
            Log.d("BackupViewModel", "Loading backup settings from SharedPreferences")
            
            // Load auto backup setting
            val autoBackupEnabled = backupService.isAutoBackupEnabled()
            autoBackup.value = autoBackupEnabled
            
            // Load backup frequency from SharedPreferences
            val savedFrequency = sharedPreferences.getString("backup_frequency", "Daily") ?: "Daily"
            if (backupFrequencyOptions.contains(savedFrequency)) {
                _backupFrequency.value = savedFrequency
            }
            
            Log.d("BackupViewModel", "Loaded settings - Auto backup: $autoBackupEnabled, Frequency: $savedFrequency")
        } catch (e: Exception) {
            Log.e("BackupViewModel", "Error loading backup settings: ${e.message}")
            // Set default values
            autoBackup.value = true
            _backupFrequency.value = "Daily"
        }
    }
    
    // Check for automatic restore on new device
    private fun checkForAutomaticRestore() {
        viewModelScope.launch {
            try {
                Log.d("BackupViewModel", "=== CHECKING FOR AUTOMATIC RESTORE ===")

                // Check if this is a fresh install (no previous backup data)
                val lastBackupDate = backupService.getLastBackupDate()
                val hasLocalData = hasExistingLocalData()

                Log.d("BackupViewModel", "Last backup date: $lastBackupDate")
                Log.d("BackupViewModel", "Has existing local data: $hasLocalData")

                // Always check for newer backups on Google Drive, not just fresh installs
                Log.d("BackupViewModel", "Checking for Google Drive backups to sync...")

                // Try to find and restore from Google Drive automatically
                val deviceAccounts = emailAuthService.getDeviceGmailAccounts()

                for (account in deviceAccounts) {
                    Log.d("BackupViewModel", "Checking Google Drive backups for account: $account")

                    when (val result = backupService.listGoogleDriveBackups(account)) {
                        is BackupResult.Success -> {
                            if (result.data.isNotEmpty()) {
                                Log.d("BackupViewModel", "Found ${result.data.size} backups for $account")

                                val latestBackup = result.data.maxByOrNull { it.createdTime }
                                if (latestBackup != null) {
                                    // Check if this backup is newer than our local data
                                    val localLastBackup = lastBackupDate?.time ?: 0
                                    if (latestBackup.createdTime > localLastBackup) {
                                        Log.d("BackupViewModel", "Remote backup is newer, performing sync restore...")
                                        
                                        // Auto-connect the account and restore the latest backup
                                        _connectedGmailAccount.value = account
                                        saveConnectedAccountToPreferences(account)
                                        
                                        Log.d("BackupViewModel", "=== STARTING AUTOMATIC RESTORE ===")
                                        Log.d("BackupViewModel", "Restoring from: ${latestBackup.name}")

                                        performAutomaticRestore(latestBackup, account)
                                        return@launch // Exit after first successful restore
                                    } else {
                                        Log.d("BackupViewModel", "Local data is up to date for $account")
                                        // Still connect the account for future syncing if not already connected
                                        if (_connectedGmailAccount.value == null) {
                                            _connectedGmailAccount.value = account
                                            saveConnectedAccountToPreferences(account)
                                        }
                                    }
                                }
                            }
                        }
                        is BackupResult.Error -> {
                            Log.d("BackupViewModel", "No backups found for $account: ${result.message}")
                        }
                    }
                }

                Log.d("BackupViewModel", "No automatic restore needed or available")
            } catch (e: Exception) {
                Log.e("BackupViewModel", "Error during automatic restore check: ${e.message}")
            }
        }
    }

    // Load connected Gmail account from SharedPreferences
    private fun loadConnectedAccountFromPreferences() {
        try {
            val savedAccount = sharedPreferences.getString("connected_gmail_account", null)
            _connectedGmailAccount.value = savedAccount
            Log.d("BackupViewModel", "Loaded connected account from preferences: $savedAccount")

            // If we have a connected account, refresh backups
            if (savedAccount != null) {
                refreshGoogleDriveBackups()
            }
        } catch (e: Exception) {
            Log.e("BackupViewModel", "Error loading connected account from preferences: ${e.message}")
            _connectedGmailAccount.value = null
        }
    }

    // Save connected Gmail account to SharedPreferences
    private fun saveConnectedAccountToPreferences(account: String?) {
        try {
            val success = sharedPreferences.edit()
                .putString("connected_gmail_account", account)
                .commit() // Use commit for immediate persistence

            if (success) {
                Log.d("BackupViewModel", "Successfully saved connected account to preferences: $account")
            } else {
                Log.e("BackupViewModel", "Failed to save connected account to preferences")
            }
        } catch (e: Exception) {
            Log.e("BackupViewModel", "Error saving connected account to preferences: ${e.message}")
        }
    }

    // Check if there's existing local data
    private fun hasExistingLocalData(): Boolean {
        return try {
            val context = getApplication<Application>()
            val sharedPrefs = context.getSharedPreferences("task_preferences", Context.MODE_PRIVATE)

            // Check for any existing tasks, dates, or bills
            val hasTaskData = sharedPrefs.getString("tasks", null) != null
            val hasDateData = sharedPrefs.getString("important_dates", null) != null
            val hasBillData = sharedPrefs.getString("bills", null) != null

            hasTaskData || hasDateData || hasBillData
        } catch (e: Exception) {
            Log.e("BackupViewModel", "Error checking for existing local data: ${e.message}")
            false
        }
    }

    // Perform automatic restore
    private suspend fun performAutomaticRestore(backup: DriveBackupInfo, accountEmail: String) {
        try {
            Log.d("BackupViewModel", "=== PERFORMING AUTOMATIC RESTORE ===")
            Log.d("BackupViewModel", "Backup: ${backup.name}, Account: $accountEmail")

            _backupState.value = BackupState.InProgress

            // Download and restore the backup
            when (val downloadResult = backupService.downloadGoogleDriveBackup(backup.id, accountEmail)) {
                is BackupResult.Success -> {
                    val backupFile = downloadResult.data
                    Log.d("BackupViewModel", "Downloaded backup file: ${backupFile.name}")

                    // Perform actual restore from the downloaded backup file
                    when (val restoreResult = backupService.restoreFromBackup(backupFile)) {
                        is BackupResult.Success -> {
                            _lastBackupDate.value = Date(backup.createdTime)
                            _backupSize.value = backup.size
                            _backupState.value = BackupState.Success

                            // Refresh available backups
                            refreshGoogleDriveBackups()

                            Log.d("BackupViewModel", "=== AUTOMATIC RESTORE COMPLETED SUCCESSFULLY ===")
                        }
                        is BackupResult.Error -> {
                            Log.e("BackupViewModel", "Automatic restore failed: ${restoreResult.message}")
                            _backupState.value = BackupState.Error("Automatic restore failed: ${restoreResult.message}")
                        }
                    }
                }
                is BackupResult.Error -> {
                    Log.e("BackupViewModel", "Automatic restore failed: ${downloadResult.message}")
                    _backupState.value = BackupState.Error("Automatic restore failed: ${downloadResult.message}")
                }
            }
        } catch (e: Exception) {
            Log.e("BackupViewModel", "Error during automatic restore: ${e.message}")
            _backupState.value = BackupState.Error("Automatic restore failed: ${e.message}")
        }
    }

    // Update backup frequency
    fun updateBackupFrequency(frequency: String) {
        if (backupFrequencyOptions.contains(frequency)) {
            _backupFrequency.value = frequency

            // Save to SharedPreferences
            try {
                sharedPreferences.edit()
                    .putString("backup_frequency", frequency)
                    .apply()
                Log.d("BackupViewModel", "Backup frequency updated and saved to: $frequency")
            } catch (e: Exception) {
                Log.e("BackupViewModel", "Error saving backup frequency: ${e.message}")
            }
        }
    }

    // Update auto backup setting
    fun updateAutoBackup(enabled: Boolean) {
        try {
            autoBackup.value = enabled
            backupService.setAutoBackup(enabled)
            Log.d("BackupViewModel", "Auto backup updated to: $enabled")
        } catch (e: Exception) {
            Log.e("BackupViewModel", "Error updating auto backup setting: ${e.message}")
        }
    }

    // Automatic backup scheduler
    private fun startAutomaticBackupSchedule() {
        viewModelScope.launch {
            while (true) {
                // Determine delay based on frequency setting
                val delayMillis = when (_backupFrequency.value) {
                    "Daily" -> 24 * 60 * 60 * 1000L // 24 hours
                    "Weekly" -> 7 * 24 * 60 * 60 * 1000L // 7 days
                    "Monthly" -> 30 * 24 * 60 * 60 * 1000L // 30 days
                    "Off" -> 365 * 24 * 60 * 60 * 1000L // Effectively disabled (1 year)
                    else -> 24 * 60 * 60 * 1000L // Default to daily
                }

                // Wait for the specified period
                delay(delayMillis)

                // Skip if set to Off
                if (_backupFrequency.value != "Off") {
                    createAutomaticBackup()
                }
            }
        }
    }

    // Automatic backup creation - happens without user interaction
    private fun createAutomaticBackup() {
        viewModelScope.launch {
            try {
                Log.d("BackupViewModel", "Creating automatic backup")

                // Don't change UI state to InProgress for automatic backups
                // to avoid interrupting user

                // Only create automatic backup if we have a connected account and auto backup is enabled
                if (_connectedGmailAccount.value != null && autoBackup.value) {
                    Log.d("BackupViewModel", "Auto backup enabled, creating real backup to Google Drive")

                    // Create a real backup and upload to Google Drive
                    when (val localBackupResult = backupService.createBackup()) {
                        is BackupResult.Success -> {
                            val backupFile = localBackupResult.data
                            Log.d("BackupViewModel", "Local backup created: ${backupFile.name}, size: ${backupFile.length()} bytes")

                            // Upload to Google Drive silently
                            when (val driveResult = backupService.backupToGoogleDrive(backupFile, _connectedGmailAccount.value!!)) {
                                is BackupResult.Success -> {
                                    // Update local state
                                    _lastBackupDate.value = Date()
                                    _backupSize.value = backupFile.length()

                                    // Refresh available backups and cleanup old ones
                                    refreshGoogleDriveBackups()
                                    cleanupOldBackups()

                                    Log.d("BackupViewModel", "Automatic backup to Google Drive completed successfully")
                                }
                                is BackupResult.Error -> {
                                    Log.e("BackupViewModel", "Automatic Google Drive backup failed: ${driveResult.message}")
                                }
                            }
                        }
                        is BackupResult.Error -> {
                            Log.e("BackupViewModel", "Automatic local backup creation failed: ${localBackupResult.message}")
                        }
                    }
                } else {
                    Log.d("BackupViewModel", "Auto backup disabled or no connected account - skipping automatic backup")
                }

            } catch (e: Exception) {
                Log.e("BackupViewModel", "Failed to create automatic backup: ${e.message}")
                // Don't update UI state to Error for automatic operations
            }
        }
    }

    fun backupToGoogleDrive() {
        viewModelScope.launch {
            try {
                if (_connectedGmailAccount.value == null) {
                    _backupState.value = BackupState.Error("No Gmail account connected")
                    return@launch
                }

                _backupState.value = BackupState.InProgress

                Log.d("BackupViewModel", "=== STARTING GOOGLE DRIVE BACKUP ===")
                Log.d("BackupViewModel", "Connected account: ${_connectedGmailAccount.value}")

                // First create a local backup file
                when (val localBackupResult = backupService.createBackup()) {
                    is BackupResult.Success -> {
                        val backupFile = localBackupResult.data
                        Log.d("BackupViewModel", "Local backup created: ${backupFile.name}, size: ${backupFile.length()} bytes")

                        // Now upload to Google Drive
                        when (val driveResult = backupService.backupToGoogleDrive(backupFile, _connectedGmailAccount.value!!)) {
                            is BackupResult.Success -> {
                                Log.d("BackupViewModel", "=== GOOGLE DRIVE BACKUP COMPLETED SUCCESSFULLY ===")
                                Log.d("BackupViewModel", "Uploaded file ID: ${driveResult.data}")

                                // Update local state
                                _lastBackupDate.value = Date()
                                _backupSize.value = backupFile.length()
                                _backupState.value = BackupState.Success

                                // Refresh available backups from Google Drive and cleanup old ones
                                refreshGoogleDriveBackups()
                                cleanupOldBackups()

                                Log.d("BackupViewModel", "Google Drive backup completed successfully")
                            }
                            is BackupResult.Error -> {
                                Log.e("BackupViewModel", "Google Drive backup failed: ${driveResult.message}")
                                _backupState.value = BackupState.Error(driveResult.message)
                            }
                        }
                    }
                    is BackupResult.Error -> {
                        Log.e("BackupViewModel", "Local backup creation failed: ${localBackupResult.message}")
                        _backupState.value = BackupState.Error("Backup creation failed: ${localBackupResult.message}")
                    }
                }

            } catch (e: Exception) {
                Log.e("BackupViewModel", "Error during Google Drive backup: ${e.message}", e)
                _backupState.value = BackupState.Error("Failed to backup to Google Drive: ${e.message}")
            }
        }
    }

    // Refresh available backups from Google Drive
    private fun refreshGoogleDriveBackups() {
        viewModelScope.launch {
            try {
                if (_connectedGmailAccount.value != null) {
                    Log.d("BackupViewModel", "Refreshing Google Drive backups...")

                    when (val result = backupService.listGoogleDriveBackups(_connectedGmailAccount.value!!)) {
                        is BackupResult.Success -> {
                            // Convert DriveBackupInfo to BackupInfo
                            val backups = result.data.map { driveBackup ->
                                BackupInfo(
                                    name = driveBackup.name,
                                    date = Date(driveBackup.createdTime),
                                    size = driveBackup.size
                                )
                            }.take(MAX_BACKUPS) // Limit to MAX_BACKUPS

                            _availableBackups.value = backups
                            Log.d("BackupViewModel", "Refreshed ${backups.size} backups from Google Drive")
                        }
                        is BackupResult.Error -> {
                            Log.e("BackupViewModel", "Failed to refresh Google Drive backups: ${result.message}")
                            // Don't update UI state for background refresh failures
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e("BackupViewModel", "Error refreshing Google Drive backups: ${e.message}")
            }
        }
    }

    private fun cleanupOldBackups() {
        viewModelScope.launch {
            try {
                if (_connectedGmailAccount.value == null) {
                    Log.d("BackupViewModel", "No connected Gmail account for backup cleanup")
                    return@launch
                }

                Log.d("BackupViewModel", "Cleaning up old backups, keeping only last $MAX_BACKUPS")

                when (val result = backupService.listGoogleDriveBackups(_connectedGmailAccount.value!!)) {
                    is BackupResult.Success -> {
                        val allBackups = result.data

                        if (allBackups.size > MAX_BACKUPS) {
                            // Sort by creation time (newest first) and get backups to delete
                            val sortedBackups = allBackups.sortedByDescending { it.createdTime }
                            val backupsToDelete = sortedBackups.drop(MAX_BACKUPS)

                            Log.d("BackupViewModel", "Found ${allBackups.size} backups, deleting ${backupsToDelete.size} old ones")

                            // Delete old backups
                            backupsToDelete.forEach { backup ->
                                when (val deleteResult = backupService.deleteGoogleDriveBackup(backup.id, _connectedGmailAccount.value!!)) {
                                    is BackupResult.Success -> {
                                        Log.d("BackupViewModel", "Deleted old backup: ${backup.name}")
                                    }
                                    is BackupResult.Error -> {
                                        Log.e("BackupViewModel", "Failed to delete backup ${backup.name}: ${deleteResult.message}")
                                    }
                                }
                            }

                            // Refresh the backup list after cleanup
                            refreshGoogleDriveBackups()
                        } else {
                            Log.d("BackupViewModel", "No cleanup needed, only ${allBackups.size} backups found")
                        }
                    }
                    is BackupResult.Error -> {
                        Log.e("BackupViewModel", "Failed to list backups for cleanup: ${result.message}")
                    }
                }
            } catch (e: Exception) {
                Log.e("BackupViewModel", "Error during backup cleanup: ${e.message}")
            }
        }
    }

    // Refresh device Gmail accounts (call when dialog opens)
    fun refreshDeviceGmailAccounts() {
        loadDeviceGmailAccounts()
    }

    fun connectGmailAccount(email: String) {
        viewModelScope.launch {
            try {
                Log.d("BackupViewModel", "=== CONNECTING GMAIL ACCOUNT WITH SIMPLIFIED AUTH ===")
                Log.d("BackupViewModel", "Account: $email")

                _backupState.value = BackupState.InProgress

                // Use simplified device account authentication
                when (val authResult = driveAuthService.authenticateDeviceAccount(email)) {
                    is AuthResult.Success -> {
                        Log.d("BackupViewModel", "=== ACCOUNT AUTHENTICATION SUCCESSFUL ===")
                        Log.d("BackupViewModel", "Authenticated account: ${authResult.accountEmail}")

                        _connectedGmailAccount.value = email
                        saveConnectedAccountToPreferences(email)
                        _backupState.value = BackupState.Success

                        // Refresh available backups from Google Drive
                        refreshGoogleDriveBackups()

                        Log.d("BackupViewModel", "Gmail account connected and authenticated successfully")
                    }
                    is AuthResult.Error -> {
                        Log.e("BackupViewModel", "Account authentication failed: ${authResult.message}")

                        // Check if this is a manual account that needs OAuth
                        if (authResult.message.contains("Manual account requires OAuth")) {
                            _backupState.value = BackupState.Error("Manual account detected. Please use the Gmail Auth screen to authenticate this account first, then return to Data Backup.")
                        } else {
                            _backupState.value = BackupState.Error("Authentication failed: ${authResult.message}")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e("BackupViewModel", "Failed to connect Gmail account: ${e.message}")
                _backupState.value = BackupState.Error("Failed to connect Gmail account: ${e.message}")
            }
        }
    }

    fun disconnectGmailAccount() {
        viewModelScope.launch {
            _connectedGmailAccount.value = null
            saveConnectedAccountToPreferences(null)
            _availableBackups.value = emptyList()
            // In a real app, would also clear tokens/credentials
        }
    }

    fun restoreBackup(backupInfo: BackupInfo) {
        viewModelScope.launch {
            try {
                _backupState.value = BackupState.InProgress

                Log.d("BackupViewModel", "=== STARTING BACKUP RESTORE ===")
                Log.d("BackupViewModel", "Backup: ${backupInfo.name}")

                // For Google Drive backups, download and restore
                if (_connectedGmailAccount.value != null) {
                    // Find the corresponding DriveBackupInfo to get the file ID
                    when (val listResult = backupService.listGoogleDriveBackups(_connectedGmailAccount.value!!)) {
                        is BackupResult.Success -> {
                            val driveBackup = listResult.data.find { it.name == backupInfo.name }
                            if (driveBackup != null) {
                                Log.d("BackupViewModel", "Found backup file ID: ${driveBackup.id}")

                                // Download the backup file
                                when (val downloadResult = backupService.downloadGoogleDriveBackup(driveBackup.id, _connectedGmailAccount.value!!)) {
                                    is BackupResult.Success -> {
                                        val backupFile = downloadResult.data
                                        Log.d("BackupViewModel", "Downloaded backup file: ${backupFile.name}")

                                        // Restore from the downloaded backup file
                                        when (val restoreResult = backupService.restoreFromBackup(backupFile)) {
                                            is BackupResult.Success -> {
                                                Log.d("BackupViewModel", "=== BACKUP RESTORE COMPLETED SUCCESSFULLY ===")
                                                _backupState.value = BackupState.Success

                                                // Update backup info to reflect the restored backup
                                                _lastBackupDate.value = backupInfo.date
                                                _backupSize.value = backupInfo.size
                                            }
                                            is BackupResult.Error -> {
                                                Log.e("BackupViewModel", "Restore failed: ${restoreResult.message}")
                                                _backupState.value = BackupState.Error("Restore failed: ${restoreResult.message}")
                                            }
                                        }
                                    }
                                    is BackupResult.Error -> {
                                        Log.e("BackupViewModel", "Download failed: ${downloadResult.message}")
                                        _backupState.value = BackupState.Error("Download failed: ${downloadResult.message}")
                                    }
                                }
                            } else {
                                Log.e("BackupViewModel", "Backup file not found in Google Drive")
                                _backupState.value = BackupState.Error("Backup file not found in Google Drive")
                            }
                        }
                        is BackupResult.Error -> {
                            Log.e("BackupViewModel", "Failed to list backups: ${listResult.message}")
                            _backupState.value = BackupState.Error("Failed to list backups: ${listResult.message}")
                        }
                    }
                } else {
                    Log.e("BackupViewModel", "No Gmail account connected for restore")
                    _backupState.value = BackupState.Error("No Gmail account connected")
                }

            } catch (e: Exception) {
                Log.e("BackupViewModel", "Failed to restore backup: ${e.message}")
                _backupState.value = BackupState.Error("Failed to restore backup: ${e.message}")
            }
        }
    }
}

/**
 * Represents backup file information
 */
data class BackupInfo(
    val name: String,
    val date: Date,
    val size: Long
)
