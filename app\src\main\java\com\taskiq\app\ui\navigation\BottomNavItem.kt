package com.taskiq.app.ui.navigation

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.List
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material.icons.filled.ShoppingCart
import androidx.compose.ui.graphics.vector.ImageVector

sealed class BottomNavItem(
    val route: String,
    val title: String,
    val icon: ImageVector
) {
    object Dashboard : BottomNavItem(
        route = "dashboard",
        title = "Dashboard",
        icon = Icons.Default.Home
    )
    
    object Tasks : BottomNavItem(
        route = "tasks",
        title = "Tasks",
        icon = Icons.AutoMirrored.Filled.List
    )
    
    object Bills : BottomNavItem(
        route = "bills",
        title = "Bills",
        icon = Icons.Default.ShoppingCart
    )
    
    object Dates : BottomNavItem(
        route = "dates",
        title = "Dates",
        icon = Icons.Default.DateRange
    )
} 