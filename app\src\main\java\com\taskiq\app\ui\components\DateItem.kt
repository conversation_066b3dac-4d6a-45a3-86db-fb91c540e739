@file:OptIn(ExperimentalFoundationApi::class)

package com.taskiq.app.ui.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.AnimationSpec
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectHorizontalDragGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.ui.graphics.Color
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.taskiq.app.model.Task
import com.taskiq.app.model.Subtask
import com.taskiq.app.ui.theme.cardBackgroundColor
import com.taskiq.app.ui.theme.cardTextColor
import com.taskiq.app.ui.theme.cardSecondaryTextColor
import com.taskiq.app.ui.theme.cardDescriptionTextColor
import com.taskiq.app.ui.theme.sdp
import java.text.SimpleDateFormat
import java.util.*
import androidx.compose.ui.text.font.FontWeight

// No longer need DragAnchors enum

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DateItem(
    date: Task,
    onDateDelete: () -> Unit,
    onDateEdit: (Task) -> Unit,
    onAddPlan: (String, Subtask) -> Unit,
    onPlanToggle: (String, String) -> Unit,
    modifier: Modifier = Modifier,
    isPastDate: Boolean = false,
    isInUpcomingSection: Boolean = false
) {
    var expanded by remember { mutableStateOf(false) }
    // Don't allow expansion if in upcoming section
    val shouldAllowExpand = !isInUpcomingSection
    
    var isAddingPlan by remember { mutableStateOf(false) }
    var newPlanTitle by remember { mutableStateOf("") }
    
    // Use a simple offset for swipe-to-edit functionality
    var offsetX by remember { mutableStateOf(0f) }
    val density = LocalDensity.current
    val editThresholdPx = with(density) { -80.dp.toPx() }
    val offsetDp = animateFloatAsState(targetValue = offsetX, label = "offset")
    
    // Track if we've triggered the edit action to prevent multiple triggers
    var editTriggered by remember { mutableStateOf(false) }
    
    // Calculate how close the date is to determine color
    val today = Calendar.getInstance().time
    val todayCal = Calendar.getInstance().apply { time = today }
    val dateCal = Calendar.getInstance().apply { 
        time = date.dueDate ?: today
        // Set to midnight for accurate day comparison
        set(Calendar.HOUR_OF_DAY, 0)
        set(Calendar.MINUTE, 0)
        set(Calendar.SECOND, 0)
        set(Calendar.MILLISECOND, 0)
    }
    todayCal.set(Calendar.HOUR_OF_DAY, 0)
    todayCal.set(Calendar.MINUTE, 0) 
    todayCal.set(Calendar.SECOND, 0)
    todayCal.set(Calendar.MILLISECOND, 0)
    
    val daysDifference = ((dateCal.timeInMillis - todayCal.timeInMillis) / (24 * 60 * 60 * 1000))
    
    // Determine title color based on how close the date is
    val titleColor = when {
        daysDifference < 0 -> Color.Red  // Past date
        daysDifference == 0L -> Color(0xFFFF6D00)  // Today (Orange)
        daysDifference <= 3 -> Color(0xFFFFB300)  // Within 3 days (Amber)
        daysDifference <= 7 -> Color(0xFF2196F3)  // Within a week (Blue)
        else -> MaterialTheme.colorScheme.primary  // Beyond a week
    }
    
    // Reset the edit triggered flag when offset returns to 0
    LaunchedEffect(offsetX) {
        if (offsetX == 0f) {
            editTriggered = false
        } else if (offsetX <= editThresholdPx && !editTriggered) {
            // Trigger edit action when threshold is reached
            onDateEdit(date)
            editTriggered = true
            // Animate back to start position
            offsetX = 0f
        }
    }
    
    Card(
        modifier = Modifier
            .fillMaxWidth()

            .pointerInput(Unit) {
                detectHorizontalDragGestures(
                    onDragEnd = {
                        if (offsetX <= editThresholdPx && !editTriggered) {
                            onDateEdit(date)
                            editTriggered = true
                        }
                        offsetX = 0f
                    },
                    onHorizontalDrag = { _, dragAmount ->
                        if (!editTriggered) {
                            offsetX = (offsetX + dragAmount).coerceAtMost(0f)
                        }
                    }
                )
            },
        colors = CardDefaults.cardColors(
            containerColor = cardBackgroundColor()
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.sdp())
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .animateContentSize()
                .padding(12.sdp())
        ) {
            // Date header
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(IntrinsicSize.Min)
            ) {
                // Edit icon that appears when swiping (removed)
                
                // Main content row
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { expanded = !expanded }, // Always allow clicking to expand
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            if (daysDifference < 0) {
                                Icon(
                                    imageVector = Icons.Default.Warning,
                                    contentDescription = "Overdue",
                                    tint = Color.Red,
                                    modifier = Modifier.size(16.sdp())
                                )
                                Spacer(modifier = Modifier.width(4.sdp()))
                            }
                            
                            Text(
                                text = date.title,
                                style = MaterialTheme.typography.titleMedium,
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis,
                                modifier = Modifier.weight(1f),
                                color = titleColor,
                                fontWeight = if (daysDifference <= 3) FontWeight.Bold else FontWeight.Normal
                            )
                        }
                        
                        // Show description if it's not blank
                        if (date.description.isNotBlank()) {
                            Text(
                                text = date.description,
                                style = MaterialTheme.typography.bodyMedium,
                                color = cardDescriptionTextColor(),
                                maxLines = 2,
                                overflow = TextOverflow.Ellipsis,
                                modifier = Modifier.padding(top = 2.sdp())
                            )
                        }
                        
                        // Date and plans info
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.sdp()),
                            modifier = Modifier.padding(top = 2.sdp())
                        ) {
                            date.dueDate?.let { dueDate ->
                                Text(
                                    text = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault()).format(dueDate),
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = if (daysDifference < 0) Color.Red else cardSecondaryTextColor(),
                                    fontWeight = if (daysDifference <= 3) FontWeight.Medium else FontWeight.Normal
                                )
                            }
                            
                            // Add plan counter if date has plans
                            if (date.subtasks.isNotEmpty()) {
                                val completedPlans = date.subtasks.count { it.isCompleted }
                                Text(
                                    text = "$completedPlans/${date.subtasks.size} plans executed",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = Color(0xFF1565C0)  // Dark blue color for better visibility
                                )
                            }
                        }
                    }
                    
                    // Removed dropdown icon as requested
                }
            }
            
            // Plans section - only show if expanded (always allow expansion but control what shows)
            AnimatedVisibility(visible = expanded) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 4.sdp())
                ) {
                    if (date.subtasks.isNotEmpty()) {
                        Text(
                            text = "Plans",
                            style = MaterialTheme.typography.titleSmall,
                            modifier = Modifier.padding(bottom = 4.sdp()),
                            color = Color(0xFF1A1A1A)  // Darker color for better visibility
                        )
                        
                        date.subtasks.forEach { plan ->
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 2.sdp()),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Checkbox(
                                    checked = plan.isCompleted,
                                    onCheckedChange = { onPlanToggle(date.id, plan.id) },
                                    modifier = Modifier.padding(end = 8.sdp()),
                                    colors = CheckboxDefaults.colors(
                                        uncheckedColor = Color(0xFF424242),  // Darker gray for better visibility
                                        checkedColor = MaterialTheme.colorScheme.primary,
                                        checkmarkColor = MaterialTheme.colorScheme.onPrimary
                                    )
                                )
                                
                                Text(
                                    text = plan.title,
                                    modifier = Modifier
                                        .weight(1f)
                                        .padding(start = 8.sdp()),
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = cardTextColor()
                                )
                            }
                        }
                    }
                    
                    // Add Plan button - always show when expanded and not a past date
                    if (!isAddingPlan && !isPastDate) {
                        TextButton(
                            onClick = { isAddingPlan = true },
                            modifier = Modifier
                                .padding(top = 4.sdp(), start = 16.sdp())
                        ) {
                            Icon(
                                imageVector = Icons.Default.Add,
                                contentDescription = "Make plan",
                                modifier = Modifier.size(20.sdp()),
                                tint = Color(0xFF1565C0)  // Dark blue color for better visibility
                            )
                            Spacer(modifier = Modifier.width(8.sdp()))
                            Text(
                                text = "Make plan",
                                color = Color(0xFF1565C0)  // Dark blue color for better visibility
                            )
                        }
                    }
                    
                    // Inline plan addition field with borderless style
                    if (isAddingPlan) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(start = 16.sdp(), end = 16.sdp(), top = 4.sdp())
                        ) {
                            Checkbox(
                                checked = false,
                                onCheckedChange = null, // No action for the checkbox yet
                                modifier = Modifier.padding(start = 16.sdp())
                            )
                            
                            // Removed the OutlinedTextField and replaced with TextField for borderless look
                            TextField(
                                value = newPlanTitle,
                                onValueChange = { newPlanTitle = it },
                                placeholder = { Text("Enter plan title", color = cardSecondaryTextColor()) },
                                singleLine = true,
                                modifier = Modifier
                                    .weight(1f)
                                    .padding(horizontal = 8.sdp()),
                                keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
                                keyboardActions = KeyboardActions(onDone = {
                                    if (newPlanTitle.isNotBlank()) {
                                        onAddPlan(date.id, Subtask(
                                            id = UUID.randomUUID().toString(),
                                            title = newPlanTitle,
                                            isCompleted = false
                                        ))
                                        newPlanTitle = ""
                                        isAddingPlan = false
                                    }
                                }),
                                // Remove border by using transparent colors
                                colors = TextFieldDefaults.colors(
                                    focusedContainerColor = Color.Transparent,
                                    unfocusedContainerColor = Color.Transparent,
                                    disabledContainerColor = Color.Transparent,
                                    focusedIndicatorColor = Color.Transparent,
                                    unfocusedIndicatorColor = Color.Transparent
                                )
                            )
                            
                            IconButton(
                                onClick = {
                                    if (newPlanTitle.isNotBlank()) {
                                        onAddPlan(date.id, Subtask(
                                            id = UUID.randomUUID().toString(),
                                            title = newPlanTitle,
                                            isCompleted = false
                                        ))
                                        newPlanTitle = ""
                                        isAddingPlan = false
                                    }
                                }
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Add,
                                    contentDescription = "Make plan",
                                    tint = MaterialTheme.colorScheme.primary
                                )
                            }
                        }
                    }
                }
            }
        }
    }

}