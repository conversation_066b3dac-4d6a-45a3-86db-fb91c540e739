package com.taskiq.app.service

import android.Manifest
import android.accounts.Account
import android.accounts.AccountManager
import android.content.Context
import android.content.pm.PackageManager
import android.util.Log
import androidx.core.content.ContextCompat
import com.google.android.gms.auth.GoogleAuthUtil
import com.google.api.client.googleapis.extensions.android.gms.auth.GoogleAccountCredential
import com.google.api.services.drive.DriveScopes
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.Collections

/**
 * Simplified Google Drive authentication service using device accounts
 * This provides an alternative to full OAuth flow for Google Drive access
 */
class GoogleDriveAuthService(private val context: Context) {
    
    private val TAG = "GoogleDriveAuthService"
    private val accountManager = AccountManager.get(context)
    
    /**
     * Authenticate a Google account for Google Drive access
     * Uses device account authentication for device accounts, OAuth for manual accounts
     */
    suspend fun authenticateDeviceAccount(accountEmail: String): AuthResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "=== AUTHENTICATING ACCOUNT FOR GOOGLE DRIVE ===")
            Log.d(TAG, "Account: $accountEmail")

            // Check permissions
            if (ContextCompat.checkSelfPermission(context, Manifest.permission.GET_ACCOUNTS)
                != PackageManager.PERMISSION_GRANTED) {
                Log.e(TAG, "GET_ACCOUNTS permission not granted")
                return@withContext AuthResult.Error("Account access permission required")
            }

            // Check if account exists on device
            val deviceAccounts = accountManager.getAccountsByType("com.google")
            val targetAccount = deviceAccounts.find { it.name == accountEmail }

            if (targetAccount != null) {
                // Device account - use simplified authentication
                Log.d(TAG, "Account found on device: ${targetAccount.name}")

                try {
                    Log.d(TAG, "Requesting auth token for Google Drive access...")

                    val token = GoogleAuthUtil.getToken(
                        context,
                        targetAccount,
                        "oauth2:${DriveScopes.DRIVE_FILE}"
                    )

                    if (token != null && token.isNotEmpty()) {
                        Log.d(TAG, "=== DEVICE AUTHENTICATION SUCCESSFUL ===")
                        Log.d(TAG, "Token obtained for account: $accountEmail")

                        // Clear the token from cache to ensure fresh authentication
                        GoogleAuthUtil.clearToken(context, token)

                        return@withContext AuthResult.Success(accountEmail)
                    } else {
                        Log.e(TAG, "Failed to obtain auth token")
                        return@withContext AuthResult.Error("Failed to authenticate device account")
                    }

                } catch (e: Exception) {
                    Log.e(TAG, "Error getting auth token: ${e.message}", e)

                    // Handle specific authentication errors
                    val errorMessage = when {
                        e.message?.contains("UserRecoverableAuthException") == true ->
                            "Account requires user permission. Please grant access in device settings."
                        e.message?.contains("GoogleAuthException") == true ->
                            "Google authentication failed. Please check account status."
                        e.message?.contains("network") == true ->
                            "Network error. Please check internet connection."
                        else -> "Authentication failed: ${e.message}"
                    }

                    return@withContext AuthResult.Error(errorMessage)
                }
            } else {
                // Manual account - requires OAuth authentication
                Log.d(TAG, "Account $accountEmail not found on device - manual account detected")
                return@withContext AuthResult.Error("Manual account requires OAuth authentication. Please use the Gmail Auth screen to authenticate this account first.")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error during account authentication: ${e.message}", e)
            return@withContext AuthResult.Error("Authentication error: ${e.message}")
        }
    }
    
    /**
     * Get available Google accounts on device for Drive authentication
     */
    fun getAvailableGoogleAccounts(): List<String> {
        return try {
            Log.d(TAG, "Getting available Google accounts for Drive authentication")
            
            if (ContextCompat.checkSelfPermission(context, Manifest.permission.GET_ACCOUNTS) 
                != PackageManager.PERMISSION_GRANTED) {
                Log.e(TAG, "GET_ACCOUNTS permission not granted")
                return emptyList()
            }
            
            val accounts = accountManager.getAccountsByType("com.google")
            val accountEmails = accounts.map { it.name }
            
            Log.d(TAG, "Found ${accountEmails.size} Google accounts: $accountEmails")
            return accountEmails
            
        } catch (e: Exception) {
            Log.e(TAG, "Error getting Google accounts: ${e.message}")
            emptyList()
        }
    }
    
    /**
     * Create authenticated GoogleAccountCredential for an account
     */
    fun createAuthenticatedCredential(accountEmail: String): GoogleAccountCredential? {
        return try {
            Log.d(TAG, "Creating authenticated credential for: $accountEmail")
            
            val credential = GoogleAccountCredential.usingOAuth2(
                context,
                Collections.singleton(DriveScopes.DRIVE_FILE)
            )
            
            credential.selectedAccountName = accountEmail
            
            Log.d(TAG, "Credential created successfully")
            return credential
            
        } catch (e: Exception) {
            Log.e(TAG, "Error creating credential: ${e.message}")
            null
        }
    }
    
    /**
     * Verify if an account is properly authenticated for Google Drive
     */
    suspend fun verifyAccountAuthentication(accountEmail: String): Boolean = withContext(Dispatchers.IO) {
        return@withContext when (authenticateDeviceAccount(accountEmail)) {
            is AuthResult.Success -> {
                Log.d(TAG, "Account $accountEmail is properly authenticated")
                true
            }
            is AuthResult.Error -> {
                Log.d(TAG, "Account $accountEmail authentication failed")
                false
            }
        }
    }
    
    /**
     * Get the first available and authenticated Google account
     */
    suspend fun getFirstAuthenticatedAccount(): String? {
        val accounts = getAvailableGoogleAccounts()
        
        for (account in accounts) {
            if (verifyAccountAuthentication(account)) {
                Log.d(TAG, "Found authenticated account: $account")
                return account
            }
        }
        
        Log.d(TAG, "No authenticated accounts found")
        return null
    }
}

/**
 * Result wrapper for authentication operations
 */
sealed class AuthResult {
    data class Success(val accountEmail: String) : AuthResult()
    data class Error(val message: String) : AuthResult()
}
