package com.taskiq.app.ui.screens

import android.Manifest
import android.app.Activity
import android.content.pm.PackageManager
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.AccountCircle
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.Info
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.ContextCompat
import androidx.navigation.NavController
import com.taskiq.app.R
import com.taskiq.app.ui.theme.ProfessionalBlue
import com.taskiq.app.ui.theme.ProfessionalWhite
import com.taskiq.app.ui.theme.rootContainerModifier
import com.taskiq.app.ui.theme.contentBelowHeaderModifier
import com.taskiq.app.ui.theme.uniformContentPadding
import com.taskiq.app.ui.theme.uniformHorizontalPadding
import com.taskiq.app.ui.theme.fullWidthContentModifier
import com.taskiq.app.ui.theme.uniformSectionSpacing
import com.taskiq.app.ui.theme.uniformSmallSpacing
import com.taskiq.app.ui.theme.uniformLargeSpacing
import com.taskiq.app.ui.theme.uniformEmptyStatePadding
import com.taskiq.app.viewmodel.AuthState
import com.taskiq.app.viewmodel.GmailAuthViewModel
import kotlinx.coroutines.launch
import com.taskiq.app.ui.theme.topAppBarColor
import com.taskiq.app.ui.theme.responsiveIconSize
import com.taskiq.app.ui.theme.responsiveLargeSpacing
import com.taskiq.app.ui.theme.responsiveSpacing
import com.taskiq.app.ui.theme.responsiveVerticalPadding
import com.taskiq.app.ui.theme.responsiveCardPadding
import com.taskiq.app.ui.theme.responsiveSmallSpacing
import com.taskiq.app.ui.theme.responsiveLargeTextSize
import com.taskiq.app.ui.theme.responsiveTextSize

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GmailAuthScreen(
    navController: NavController,
    viewModel: GmailAuthViewModel,
    taskViewModel: com.taskiq.app.viewmodel.TaskViewModel?,
    onEmailLinked: ((String) -> Unit)? = null
) {
    val authState by viewModel.authState.collectAsState()
    val accounts by viewModel.availableAccounts.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }
    val scope = rememberCoroutineScope()
    val context = LocalContext.current

    // Permission request launcher for accessing accounts
    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            viewModel.refreshAvailableAccounts()
        } else {
            scope.launch {
                snackbarHostState.showSnackbar("Account access permission denied")
            }
        }
    }

    // Google Sign-In launcher for OAuth flow
    val googleSignInLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        android.util.Log.d("GmailAuthScreen", "Google Sign-In result received with code: ${result.resultCode}")
        if (result.resultCode == Activity.RESULT_OK) {
            android.util.Log.d("GmailAuthScreen", "Google Sign-In successful, handling result")
            viewModel.handleSignInResult(result.data)
        } else {
            android.util.Log.d("GmailAuthScreen", "Google Sign-In cancelled or failed with code: ${result.resultCode}")
            // Reset auth state if sign-in was cancelled
            viewModel.resetAuthState()
            scope.launch {
                snackbarHostState.showSnackbar("Google Sign-In was cancelled")
            }
        }
    }

    // Handle auth state changes
    LaunchedEffect(authState) {
        when (authState) {
            is AuthState.Error -> {
                snackbarHostState.showSnackbar((authState as AuthState.Error).message)
            }
            is AuthState.Success -> {
                val messageCount = (authState as AuthState.Success).messageCount
                snackbarHostState.showSnackbar("Successfully fetched $messageCount emails")
            }
            else -> {}
        }
    }

    // Check for permissions on first launch
    LaunchedEffect(Unit) {
        if (ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.GET_ACCOUNTS
            ) != PackageManager.PERMISSION_GRANTED) {
            permissionLauncher.launch(Manifest.permission.GET_ACCOUNTS)
        }
    }

    Scaffold(
        modifier = rootContainerModifier,
        snackbarHost = { SnackbarHost(hostState = snackbarHostState) },
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Gmail Connection",
                        color = ProfessionalWhite,
                        fontWeight = FontWeight.Bold,
                        fontSize = responsiveLargeTextSize()
                    )
                },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back",
                            tint = ProfessionalWhite
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = topAppBarColor(),
                    titleContentColor = ProfessionalWhite,
                    navigationIconContentColor = ProfessionalWhite
                )
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = uniformHorizontalPadding())
        ) {
            // Content based on auth state
            when (authState) {
                is AuthState.Loading -> {
                    CircularProgressIndicator(
                        modifier = Modifier
                            .size(responsiveIconSize() * 2.4f)
                            .align(Alignment.Center)
                    )
                }

                is AuthState.SignedIn -> {
                    SignedInContent(
                        email = (authState as AuthState.SignedIn).email,
                        onFetchBills = {
                            scope.launch {
                                val email = (authState as AuthState.SignedIn).email
                                android.util.Log.d("GmailAuthScreen", "Email linked: $email")

                                // If we have a callback (dialog mode), use it
                                if (onEmailLinked != null) {
                                    onEmailLinked(email)
                                } else {
                                    // Original behavior for full screen mode
                                    taskViewModel?.let { vm ->
                                        android.util.Log.d("GmailAuthScreen", "Adding verified email to TaskViewModel: $email")
                                        vm.addVerifiedEmail(email)

                                        // Set Gmail service in TaskViewModel for real scanning
                                        val gmailService = viewModel.getGmailService()
                                        if (gmailService != null) {
                                            android.util.Log.d("GmailAuthScreen", "Setting Gmail service in TaskViewModel")
                                            vm.setGmailService(gmailService)
                                        }

                                        // Scan for bills using TaskViewModel (will use real Gmail API if available)
                                        android.util.Log.d("GmailAuthScreen", "Starting real Gmail bill scan for email: $email")
                                        vm.scanEmailsForBills()

                                        // Also call the GmailAuthViewModel method for UI feedback
                                        viewModel.fetchBillEmails()

                                        android.util.Log.d("GmailAuthScreen", "Bill scan completed, navigating back to bills screen")
                                        // Navigate back to bills screen after scanning
                                        navController.popBackStack()
                                    }
                                }
                            }
                        },
                        onSignOut = {
                            viewModel.signOut()
                            // Clear Gmail service from TaskViewModel if available
                            taskViewModel?.setGmailService(null)
                            navController.popBackStack()
                        }
                    )
                }

                else -> {
                    if (accounts.isEmpty() && !viewModel.hasRequiredPermissions()) {
                        // Show permission request UI
                        PermissionRequestContent {
                            permissionLauncher.launch(Manifest.permission.GET_ACCOUNTS)
                        }
                    } else if (accounts.isEmpty()) {
                        // Show Google OAuth sign-in option
                        GoogleSignInContent {
                            android.util.Log.d("GmailAuthScreen", "User clicked Google Sign-In button")
                            val signInIntent = viewModel.startGoogleSignIn()
                            if (signInIntent != null) {
                                android.util.Log.d("GmailAuthScreen", "Launching Google Sign-In intent")
                                googleSignInLauncher.launch(signInIntent)
                            } else {
                                android.util.Log.e("GmailAuthScreen", "Failed to create Google Sign-In intent")
                                scope.launch {
                                    snackbarHostState.showSnackbar("Failed to start Google Sign-In")
                                }
                            }
                        }
                    } else {
                        // Show both device accounts and OAuth option
                        AccountSelectionWithOAuthContent(
                            accounts = accounts,
                            onAccountSelected = { email ->
                                android.util.Log.d("GmailAuthScreen", "Device account selected: $email")
                                viewModel.selectAccount(email)
                                taskViewModel?.addVerifiedEmail(email)
                                onEmailLinked?.invoke(email)
                            },
                            onGoogleSignIn = {
                                android.util.Log.d("GmailAuthScreen", "User clicked Google Sign-In from account selection")
                                val signInIntent = viewModel.startGoogleSignIn()
                                if (signInIntent != null) {
                                    android.util.Log.d("GmailAuthScreen", "Launching Google Sign-In intent from account selection")
                                    googleSignInLauncher.launch(signInIntent)
                                } else {
                                    android.util.Log.e("GmailAuthScreen", "Failed to create Google Sign-In intent from account selection")
                                    scope.launch {
                                        snackbarHostState.showSnackbar("Failed to start Google Sign-In")
                                    }
                                }
                            }
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun PermissionRequestContent(onRequestPermission: () -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(uniformEmptyStatePadding()),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.Email,
            contentDescription = null,
            modifier = Modifier.size(responsiveIconSize() * 4),
            tint = MaterialTheme.colorScheme.primary
        )

        Spacer(modifier = Modifier.height(uniformLargeSpacing()))

        Text(
            text = "Account Access Required",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(uniformSectionSpacing()))

        Text(
            text = "We need permission to access your Google accounts to help you manage your bills.",
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(uniformLargeSpacing()))

        Button(
            onClick = onRequestPermission,
            modifier = fullWidthContentModifier()
        ) {
            Text("Grant Permission", fontSize = responsiveTextSize())
        }
    }
}

@Composable
fun NoAccountsContent(onRefresh: () -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(uniformEmptyStatePadding()),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.Email,
            contentDescription = null,
            modifier = Modifier.size(responsiveIconSize() * 4),
            tint = MaterialTheme.colorScheme.primary
        )

        Spacer(modifier = Modifier.height(uniformLargeSpacing()))

        Text(
            text = "No Google Accounts Found",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(uniformSectionSpacing()))

        Text(
            text = "Please add a Google account to your device to use this feature.",
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(uniformLargeSpacing()))

        Button(
            onClick = onRefresh,
            modifier = fullWidthContentModifier()
        ) {
            Text("Refresh", fontSize = responsiveTextSize())
        }
    }
}

@Composable
fun AccountSelectionContent(
    accounts: List<String>,
    onAccountSelected: (String) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(uniformEmptyStatePadding()),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Top
    ) {
        Text(
            text = "Select Google Account",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(vertical = uniformLargeSpacing())
        )

        LazyColumn(
            modifier = Modifier.fillMaxWidth(),
            contentPadding = uniformContentPadding()
        ) {
            items(accounts) { email ->
                Button(
                    onClick = { onAccountSelected(email) },
                    modifier = fullWidthContentModifier()
                        .padding(vertical = uniformSectionSpacing())
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = uniformSectionSpacing())
                    ) {
                        Icon(
                            imageVector = Icons.Default.AccountCircle,
                            contentDescription = "Account",
                            modifier = Modifier.size(responsiveIconSize() * 1.2f)
                        )

                        Spacer(modifier = Modifier.width(uniformSectionSpacing()))

                        Text(text = email)
                    }
                }
            }
        }
    }
}

@Composable
fun GoogleSignInContent(onGoogleSignIn: () -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(uniformEmptyStatePadding()),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.Email,
            contentDescription = null,
            modifier = Modifier.size(responsiveIconSize() * 4),
            tint = MaterialTheme.colorScheme.primary
        )

        Spacer(modifier = Modifier.height(uniformLargeSpacing()))

        Text(
            text = "Connect Gmail Account",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(uniformSectionSpacing()))

        Text(
            text = "Sign in with Google to scan your emails for bills and automatically add them to your bill tracker.",
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(uniformLargeSpacing()))

        Button(
            onClick = onGoogleSignIn,
            modifier = fullWidthContentModifier()
        ) {
            Text("Sign in with Google", fontSize = responsiveTextSize())
        }
    }
}

@Composable
fun AccountSelectionWithOAuthContent(
    accounts: List<String>,
    onAccountSelected: (String) -> Unit,
    onGoogleSignIn: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(uniformEmptyStatePadding()),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Top
    ) {
        Text(
            text = "Choose Account",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(vertical = uniformLargeSpacing())
        )

        // Google OAuth Sign-In Option
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = uniformSectionSpacing()),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            )
        ) {
            Column(
                modifier = Modifier.padding(uniformSectionSpacing()),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Recommended",
                    style = MaterialTheme.typography.labelMedium,
                    color = MaterialTheme.colorScheme.primary,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(uniformSmallSpacing()))

                Button(
                    onClick = onGoogleSignIn,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text("Sign in with Google OAuth")
                }

                Spacer(modifier = Modifier.height(uniformSmallSpacing()))

                Text(
                    text = "Secure authentication with full Gmail access",
                    style = MaterialTheme.typography.bodySmall,
                    textAlign = TextAlign.Center
                )
            }
        }

        // Device Accounts
        Text(
            text = "Or select from device accounts:",
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.padding(vertical = uniformSmallSpacing())
        )

        LazyColumn(
            modifier = Modifier.fillMaxWidth(),
            contentPadding = uniformContentPadding()
        ) {
            items(accounts) { email ->
                Button(
                    onClick = { onAccountSelected(email) },
                    modifier = fullWidthContentModifier()
                        .padding(vertical = uniformSmallSpacing()),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = uniformSectionSpacing())
                    ) {
                        Icon(
                            imageVector = Icons.Default.AccountCircle,
                            contentDescription = "Account",
                            modifier = Modifier.size(responsiveIconSize() * 1.2f)
                        )

                        Spacer(modifier = Modifier.width(uniformSectionSpacing()))

                        Text(text = email)
                    }
                }
            }
        }
    }
}

@Composable
fun SignedInContent(
    email: String,
    onFetchBills: () -> Unit,
    onSignOut: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(uniformEmptyStatePadding()),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.Email,
            contentDescription = null,
            modifier = Modifier.size(responsiveIconSize() * 3.2f),
            tint = MaterialTheme.colorScheme.primary
        )

        Spacer(modifier = Modifier.height(uniformLargeSpacing()))

        Text(
            text = "Gmail Connected",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(uniformSmallSpacing()))

        Text(
            text = email,
            style = MaterialTheme.typography.titleMedium,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(uniformLargeSpacing()))

        Button(
            onClick = onFetchBills,
            modifier = fullWidthContentModifier()
        ) {
            Text("Scan for Bill Emails")
        }

        Spacer(modifier = Modifier.height(uniformSectionSpacing()))

        Button(
            onClick = onSignOut,
            modifier = fullWidthContentModifier()
        ) {
            Text("Sign Out")
        }
    }
}