package com.taskiq.app.ui.screens

import android.Manifest
import android.content.pm.PackageManager
import android.util.Log
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.Box
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.CloudUpload
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.Restore
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.AccountCircle
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.RadioButton
import androidx.compose.material3.RadioButtonDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.taskiq.app.ui.theme.*
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.platform.LocalContext
import androidx.core.content.ContextCompat
import androidx.navigation.NavController
import kotlinx.coroutines.launch
import com.taskiq.app.ui.theme.ProfessionalBlue
import com.taskiq.app.ui.theme.ProfessionalWhite
import com.taskiq.app.ui.theme.topAppBarColor
import com.taskiq.app.ui.theme.cardBackgroundColor
import com.taskiq.app.viewmodel.BackupInfo
import com.taskiq.app.viewmodel.BackupState
import com.taskiq.app.viewmodel.BackupViewModel
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.Row
import com.taskiq.app.ui.navigation.Routes
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

// Helper function to format file size
fun formatSize(sizeInBytes: Long): String {
    return when {
        sizeInBytes < 1024 -> "$sizeInBytes B"
        sizeInBytes < 1024 * 1024 -> "${sizeInBytes / 1024} KB"
        sizeInBytes < 1024 * 1024 * 1024 -> "${sizeInBytes / (1024 * 1024)} MB"
        else -> "${sizeInBytes / (1024 * 1024 * 1024)} GB"
    }
}

/**
 * Data Backup screen for managing backup settings and operations
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DataBackupScreen(
    viewModel: BackupViewModel,
    onNavigateBack: () -> Unit,
    navController: NavController
) {
    // Add logging to diagnose issues
    Log.d("DATABACKUP", "DataBackupScreen composable started")

    // Get context and coroutine scope for permission handling
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    // Permission request launcher for accessing accounts
    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            Log.d("DATABACKUP", "GET_ACCOUNTS permission granted, refreshing accounts")
            viewModel.refreshDeviceGmailAccounts()
        } else {
            Log.e("DATABACKUP", "GET_ACCOUNTS permission denied")
        }
    }

    // Format date for display
    val dateFormat = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
    Log.d("DATABACKUP", "DateFormat created")
    
    val backupInfoText = when {
        viewModel.lastBackupDate.value != null -> {
            "Last backup: ${dateFormat.format(viewModel.lastBackupDate.value!!)}\nSize: ${formatSize(viewModel.backupSize.value)}"
        }
        else -> "Last backup: Never\nSize: 0 MB"
    }
    Log.d("DATABACKUP", "Backup info text created: $backupInfoText")
    
    // Track operation state
    var showErrorMessage by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf("") }
    
    // Gmail account dialog state
    var showAddAccountDialog by remember { mutableStateOf(false) }
    var newGmailAccount by remember { mutableStateOf("") }
    
    // Backup frequency menu state
    var showBackupFrequencyMenu by remember { mutableStateOf(false) }
    
    // Backup frequency options
    val backupFrequencyOptions = listOf("Daily", "Weekly", "Monthly", "Off")
    
    // Restore confirmation dialog
    var showRestoreConfirmation by remember { mutableStateOf(false) }
    var selectedBackup by remember { mutableStateOf<BackupInfo?>(null) }
    
    // Update UI based on backup state
    when (val state = viewModel.backupState.value) {
        is BackupState.Error -> {
            errorMessage = state.message
            showErrorMessage = true
        }
        else -> { /* Handle other states if needed */ }
    }
    
    // Gmail Account Dialog
    if (showAddAccountDialog) {
        // Check permission and refresh device accounts when dialog opens
        LaunchedEffect(showAddAccountDialog) {
            if (showAddAccountDialog) {
                Log.d("DATABACKUP", "Dialog opened, checking GET_ACCOUNTS permission")
                if (ContextCompat.checkSelfPermission(
                        context,
                        Manifest.permission.GET_ACCOUNTS
                    ) == PackageManager.PERMISSION_GRANTED) {
                    Log.d("DATABACKUP", "Permission granted, refreshing accounts")
                    viewModel.refreshDeviceGmailAccounts()
                } else {
                    Log.d("DATABACKUP", "Permission not granted, requesting permission")
                    permissionLauncher.launch(Manifest.permission.GET_ACCOUNTS)
                }
            }
        }

        // WhatsApp-style floating dialog
        Dialog(
            onDismissRequest = { showAddAccountDialog = false }
        ) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = responsiveLargeSpacing()),
                shape = RoundedCornerShape(responsiveLargeSpacing()),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = responsiveCardElevation())
            ) {
                Column(
                    modifier = Modifier.padding(responsiveLargeSpacing())
                ) {
                    // Title
                    Text(
                        text = "Choose an account",
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.onSurface,
                        modifier = Modifier.padding(bottom = responsiveLargeSpacing())
                    )

                    // Device accounts list
                    val deviceAccounts = viewModel.deviceGmailAccounts.value

                    deviceAccounts.forEach { email ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    newGmailAccount = email
                                    viewModel.connectGmailAccount(email)
                                    showAddAccountDialog = false
                                }
                                .padding(vertical = responsiveSpacing()),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // Radio button
                            RadioButton(
                                selected = false,
                                onClick = {
                                    newGmailAccount = email
                                    viewModel.connectGmailAccount(email)
                                    showAddAccountDialog = false
                                },
                                colors = RadioButtonDefaults.colors(
                                    selectedColor = Color(0xFF25D366), // WhatsApp green
                                    unselectedColor = MaterialTheme.colorScheme.outline
                                )
                            )

                            Spacer(modifier = Modifier.width(responsiveLargeSpacing()))

                            // Email text
                            Text(
                                text = email,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                modifier = Modifier.weight(1f)
                            )
                        }
                    }

                    // Add account option
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable {
                                // Redirect to Gmail Auth screen
                                showAddAccountDialog = false
                                navController.navigate(Routes.GMAIL_AUTH)
                            }
                            .padding(vertical = responsiveSpacing()),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // Radio button
                        RadioButton(
                            selected = false,
                            onClick = {
                                showAddAccountDialog = false
                                navController.navigate(Routes.GMAIL_AUTH)
                            },
                            colors = RadioButtonDefaults.colors(
                                selectedColor = Color(0xFF25D366), // WhatsApp green
                                unselectedColor = MaterialTheme.colorScheme.outline
                            )
                        )

                        Spacer(modifier = Modifier.width(responsiveLargeSpacing()))

                        // Add account text
                        Text(
                            text = "Add account",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurface,
                            modifier = Modifier.weight(1f)
                        )
                    }

                    Spacer(modifier = Modifier.height(responsiveLargeSpacing()))

                    // Cancel button
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.End
                    ) {
                        TextButton(
                            onClick = { showAddAccountDialog = false },
                            colors = ButtonDefaults.textButtonColors(
                                contentColor = Color(0xFF25D366) // WhatsApp green
                            )
                        ) {
                            Text(
                                text = "Cancel",
                                style = MaterialTheme.typography.labelLarge,
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }
                }
            }
        }
    }
    
    // Restore Confirmation Dialog
    if (showRestoreConfirmation && selectedBackup != null) {
        AlertDialog(
            onDismissRequest = { 
                showRestoreConfirmation = false
                selectedBackup = null
            },
            title = { Text("Restore Data") },
            text = {
                Column {
                    Text("Are you sure you want to restore from backup '${selectedBackup?.name}'?")
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        "This will replace all your current tasks, dates, and bills with the data from this backup.",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.error.copy(alpha = 0.8f)
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        "Backup date: ${dateFormat.format(selectedBackup?.date ?: Date())}",
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            },
            confirmButton = {
                Button(
                    onClick = { 
                        selectedBackup?.let { 
                            viewModel.restoreBackup(it)
                            showRestoreConfirmation = false
                            selectedBackup = null
                        }
                    }
                ) {
                    Text("Restore")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { 
                        showRestoreConfirmation = false
                        selectedBackup = null
                    }
                ) {
                    Text("Cancel")
                }
            }
        )
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { 
                    Text(
                        text = "Data Backup",
                        fontWeight = FontWeight.Bold,
                        fontSize = 24.sp
                    ) 
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = topAppBarColor(),
                    titleContentColor = ProfessionalWhite,
                    navigationIconContentColor = ProfessionalWhite
                ),
                windowInsets = WindowInsets.statusBars
            )
        },
        contentWindowInsets = WindowInsets(0, 0, 0, 0)
    ) { paddingValues ->
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.TopCenter
        ) {
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .widthIn(max = responsiveMaxContentWidth())
                    .padding(paddingValues)
                    .padding(responsiveContentPadding())
            ) {
            // Last backup info card
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = cardBackgroundColor()
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(responsiveLargeSpacing())
                    ) {
                        Text(
                            text = "Backup Information",
                            style = MaterialTheme.typography.titleMedium
                        )
                        
                        Spacer(modifier = Modifier.height(responsiveSpacing()))
                        
                        Text(
                            text = backupInfoText,
                            style = MaterialTheme.typography.bodyMedium
                        )
                        
                        if (viewModel.backupState.value == BackupState.InProgress) {
                            Spacer(modifier = Modifier.height(responsiveSpacing()))
                            Text(
                                text = "Operation in progress...",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.primary
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(responsiveLargeSpacing()))
            }
            
            // Google Account Card
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = cardBackgroundColor()
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(responsiveLargeSpacing())
                    ) {
                        Text(
                            text = "Google Drive Account",
                            style = MaterialTheme.typography.titleMedium
                        )
                        
                        Spacer(modifier = Modifier.height(12.dp))
                        
                        if (viewModel.connectedGmailAccount.value == null) {
                            OutlinedButton(
                                onClick = { showAddAccountDialog = true },
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Add,
                                    contentDescription = "Add Account"
                                )
                                Spacer(modifier = Modifier.padding(4.dp))
                                Text("Connect Gmail Account")
                            }
                        } else {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .border(
                                        width = 1.dp,
                                        color = MaterialTheme.colorScheme.outline.copy(alpha = 0.5f),
                                        shape = RoundedCornerShape(8.dp)
                                    )
                                    .clip(RoundedCornerShape(8.dp))
                                    .padding(12.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Email,
                                    contentDescription = "Gmail Account",
                                    tint = MaterialTheme.colorScheme.primary
                                )
                                
                                Spacer(modifier = Modifier.padding(8.dp))
                                
                                Text(
                                    text = viewModel.connectedGmailAccount.value ?: "",
                                    style = MaterialTheme.typography.bodyMedium,
                                    modifier = Modifier.weight(1f)
                                )
                                
                                IconButton(
                                    onClick = { viewModel.disconnectGmailAccount() }
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Close,
                                        contentDescription = "Disconnect Account",
                                        tint = MaterialTheme.colorScheme.error
                                    )
                                }
                            }
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
            }
            
            // Backup options card
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = cardBackgroundColor()
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Backup Options",
                            style = MaterialTheme.typography.titleMedium
                        )
                        
                        Spacer(modifier = Modifier.height(12.dp))
                        
                        // Auto backup toggle
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Column(modifier = Modifier.weight(1f)) {
                                Text(
                                    text = "Auto Backup",
                                    style = MaterialTheme.typography.bodyMedium
                                )
                                Text(
                                    text = if (viewModel.autoBackup.value) "Enabled" else "Disabled",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }

                            Switch(
                                checked = viewModel.autoBackup.value,
                                onCheckedChange = { viewModel.updateAutoBackup(it) }
                            )
                        }

                        Spacer(modifier = Modifier.height(12.dp))

                        // Auto backup frequency dropdown
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Column(modifier = Modifier.weight(1f)) {
                                Text(
                                    text = "Auto Backup Frequency",
                                    style = MaterialTheme.typography.bodyMedium
                                )
                                Text(
                                    text = viewModel.backupFrequency.value,
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }

                            Box {
                                IconButton(onClick = { showBackupFrequencyMenu = true }) {
                                    Icon(
                                        imageVector = Icons.Default.Settings,
                                        contentDescription = "Set Backup Frequency"
                                    )
                                }

                                DropdownMenu(
                                    expanded = showBackupFrequencyMenu,
                                    onDismissRequest = { showBackupFrequencyMenu = false }
                                ) {
                                    backupFrequencyOptions.forEach { option ->
                                        DropdownMenuItem(
                                            text = { Text(option) },
                                            onClick = {
                                                viewModel.updateBackupFrequency(option)
                                                showBackupFrequencyMenu = false
                                            },
                                            trailingIcon = if (viewModel.backupFrequency.value == option) {
                                                {
                                                    Icon(
                                                        imageVector = Icons.Default.Check,
                                                        contentDescription = null,
                                                        tint = MaterialTheme.colorScheme.primary
                                                    )
                                                }
                                            } else null
                                        )
                                    }
                                }
                            }
                        }


                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
            }
            
            // Backup actions card
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = cardBackgroundColor()
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Backup Actions",
                            style = MaterialTheme.typography.titleMedium
                        )
                        
                        Spacer(modifier = Modifier.height(16.dp))
                        
                        Button(
                            onClick = { viewModel.backupToGoogleDrive() },
                            modifier = Modifier.fillMaxWidth(),
                            enabled = viewModel.connectedGmailAccount.value != null && 
                                viewModel.backupState.value != BackupState.InProgress
                        ) {
                            Icon(
                                imageVector = Icons.Default.CloudUpload,
                                contentDescription = null
                            )
                            Spacer(modifier = Modifier.padding(4.dp))
                            Text("Backup to Google Drive")
                        }
                        
                        Spacer(modifier = Modifier.height(4.dp))
                        
                        // Description for Google Drive backup
                        Text(
                            text = if (viewModel.connectedGmailAccount.value != null)
                                "Saves your data to your Google Drive account using device authentication"
                            else
                                "Connect your Gmail account to enable this option",
                            style = MaterialTheme.typography.bodySmall,
                            color = if (viewModel.connectedGmailAccount.value != null)
                                MaterialTheme.colorScheme.onSurfaceVariant
                            else
                                MaterialTheme.colorScheme.error
                        )
                        
                        if (showErrorMessage) {
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = errorMessage,
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.error
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
            }
            
            // Available backups and restore section
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = cardBackgroundColor()
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Available Backups",
                            style = MaterialTheme.typography.titleMedium
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        Text(
                            text = "Select a backup to restore your data",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        
                        Spacer(modifier = Modifier.height(12.dp))
                        
                        if (viewModel.availableBackups.value.isEmpty()) {
                            Text(
                                text = "No backups available",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 12.dp),
                                textAlign = TextAlign.Center
                            )
                        } else {
                            Column {
                                viewModel.availableBackups.value.forEach { backup ->
                                    Card(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(vertical = 4.dp),
                                        colors = CardDefaults.cardColors(
                                            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
                                        )
                                    ) {
                                        BackupItem(
                                            backup = backup,
                                            onClick = { 
                                                selectedBackup = backup
                                                showRestoreConfirmation = true
                                            }
                                        )
                                    }
                                }
                            }
                            
                            Spacer(modifier = Modifier.height(8.dp))
                            
                            Text(
                                text = "Note: Restoring will replace all current data",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.error.copy(alpha = 0.7f),
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }
                }
            }
            
            // Bottom padding
            item {
                Spacer(modifier = Modifier.height(16.dp))
            }
            }
        }
    }
}

@Composable
fun BackupItem(
    backup: BackupInfo,
    onClick: () -> Unit
) {
    val dateFormat = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = Icons.Default.CloudUpload,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.padding(end = 12.dp)
        )
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = backup.name,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = dateFormat.format(backup.date),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                Text(
                    text = " • ",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                Text(
                    text = formatSize(backup.size),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontWeight = FontWeight.Medium
                )
            }
        }
        
        IconButton(
            onClick = onClick
        ) {
            Icon(
                imageVector = Icons.Default.Restore,
                contentDescription = "Restore from backup",
                tint = MaterialTheme.colorScheme.primary
            )
        }
    }
} 
