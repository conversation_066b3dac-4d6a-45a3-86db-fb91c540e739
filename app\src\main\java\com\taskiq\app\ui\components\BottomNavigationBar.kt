package com.taskiq.app.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.ui.unit.dp
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.List
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material.icons.filled.ShoppingCart
import androidx.compose.material3.Icon
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.NavigationBarItemDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.navigation.NavController
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.compose.currentBackStackEntryAsState
import com.taskiq.app.ui.navigation.BottomNavItem
import com.taskiq.app.ui.theme.ProfessionalBlue
import com.taskiq.app.ui.theme.ProfessionalLightBlue
import com.taskiq.app.ui.theme.ProfessionalWhite

@Composable
fun BottomNavigationBar(navController: NavController) {
    val items = listOf(
        BottomNavItem.Dashboard,
        BottomNavItem.Tasks,
        BottomNavItem.Bills,
        BottomNavItem.Dates
    )

    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentDestination = navBackStackEntry?.destination

    // Box with background that extends to bottom navigation panel
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color(0xFFF8F8F8)) // Off-white background that extends to bottom
            .windowInsetsPadding(WindowInsets.navigationBars) // Extend background to navigation bars
    ) {
        Column {
            // Top border for contrast
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(1.dp)
                    .background(Color.Gray.copy(alpha = 0.5f))
            )

            NavigationBar(
                containerColor = Color(0xFFF8F8F8), // Off-white background
                contentColor = Color.Black,
                windowInsets = WindowInsets(0, 0, 0, 0) // Remove default window insets
            ) {
            items.forEach { item ->
                NavigationBarItem(
                    icon = {
                        Icon(
                            imageVector = when (item) {
                                BottomNavItem.Dashboard -> Icons.Default.Home
                                BottomNavItem.Tasks -> Icons.AutoMirrored.Filled.List
                                BottomNavItem.Bills -> Icons.Default.ShoppingCart
                                BottomNavItem.Dates -> Icons.Default.DateRange
                            },
                            contentDescription = item.title
                        )
                    },
                    label = { Text(item.title) },
                    selected = currentDestination?.hierarchy?.any { it.route == item.route } == true,
                    colors = NavigationBarItemDefaults.colors(
                        selectedIconColor = ProfessionalBlue,
                        selectedTextColor = ProfessionalBlue,
                        unselectedIconColor = Color.Gray,
                        unselectedTextColor = Color.Gray,
                        indicatorColor = ProfessionalBlue.copy(alpha = 0.1f)
                    ),
                    onClick = {
                        navController.navigate(item.route) {
                            popUpTo(navController.graph.findStartDestination().id) {
                                saveState = true
                            }
                            launchSingleTop = true
                            restoreState = true
                        }
                    }
                )
            }
            }
        }
    }
}