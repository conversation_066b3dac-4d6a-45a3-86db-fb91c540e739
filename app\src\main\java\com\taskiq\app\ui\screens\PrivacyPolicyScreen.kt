package com.taskiq.app.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.taskiq.app.ui.theme.ProfessionalWhite
import com.taskiq.app.ui.theme.topAppBarColor
import com.taskiq.app.ui.theme.sdp
import com.taskiq.app.ui.theme.ssp
import com.taskiq.app.ui.theme.responsiveLargeSpacing
import com.taskiq.app.ui.theme.responsiveSpacing
import com.taskiq.app.ui.theme.responsiveLargeTextSize

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PrivacyPolicyScreen(
    navController: NavController
) {
    Scaffold(
        topBar = {
            TopAppBar(
                title = { 
                    Text(
                        text = "Privacy Policy",
                        fontWeight = FontWeight.Bold,
                        fontSize = 18.ssp()
                    )
                },
                navigationIcon = {
                    IconButton(
                        onClick = { navController.popBackStack() },
                        modifier = Modifier.padding(end = 1.sdp())
                    ) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = topAppBarColor(),
                    titleContentColor = ProfessionalWhite,
                    navigationIconContentColor = ProfessionalWhite
                ),
                windowInsets = WindowInsets.statusBars
            )
        },
        contentWindowInsets = WindowInsets(0, 0, 0, 0)
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(responsiveLargeSpacing() * 3),
            verticalArrangement = Arrangement.spacedBy(responsiveLargeSpacing() * 3)
        ) {
            item {
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = "Last Updated: December 2024",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        fontWeight = FontWeight.Medium
                    )

                    Spacer(modifier = Modifier.height(responsiveLargeSpacing() * 2))

                    Text(
                        text = "TaskIQ is committed to protecting your privacy. This Privacy Policy explains how we collect, use, and safeguard your information when you use our mobile application.",
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Justify,
                        lineHeight = responsiveLargeTextSize() * 1.33f
                    )
                }
            }

            item {
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = "Information We Collect",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )

                    Spacer(modifier = Modifier.height(responsiveSpacing() * 3))

                    Text(
                        text = "• Personal Information: Name, email address, and profile information you provide\n" +
                                "• Task Data: Tasks, dates, bills, and related information you create\n" +
                                "• Usage Data: How you interact with the app and its features\n" +
                                "• Device Information: Device type, operating system, and app version",
                        style = MaterialTheme.typography.bodyLarge,
                        lineHeight = responsiveLargeTextSize() * 1.33f
                    )
                }
            }

            item {
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = "How We Use Your Information",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )

                    Spacer(modifier = Modifier.height(responsiveSpacing() * 3))

                    Text(
                        text = "• Provide and maintain the app's functionality\n" +
                                "• Sync your data across devices\n" +
                                "• Send notifications and reminders\n" +
                                "• Improve our services and user experience\n" +
                                "• Provide customer support",
                        style = MaterialTheme.typography.bodyLarge,
                        lineHeight = responsiveLargeTextSize() * 1.33f
                    )
                }
            }
            
            item {
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = "Data Security",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )

                    Spacer(modifier = Modifier.height(responsiveSpacing() * 3))

                    Text(
                        text = "We implement appropriate security measures to protect your personal information. Your data is encrypted both in transit and at rest. We use secure cloud storage and follow industry best practices for data protection.",
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Justify,
                        lineHeight = responsiveLargeTextSize() * 1.33f
                    )
                }
            }

            item {
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = "Third-Party Services",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )

                    Spacer(modifier = Modifier.height(responsiveSpacing() * 3))

                    Text(
                        text = "TaskIQ integrates with Google Drive for backup services and Gmail for bill scanning. These services have their own privacy policies, and we encourage you to review them.",
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Justify,
                        lineHeight = responsiveLargeTextSize() * 1.33f
                    )
                }
            }

            item {
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = "Your Rights",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )

                    Spacer(modifier = Modifier.height(responsiveSpacing() * 3))

                    Text(
                        text = "You have the right to access, update, or delete your personal information. You can also export your data or request account deletion at any time through the app settings.",
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Justify,
                        lineHeight = responsiveLargeTextSize() * 1.33f
                    )
                }
            }

            item {
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = "Contact Us",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )

                    Spacer(modifier = Modifier.height(responsiveSpacing() * 3))

                    Text(
                        text = "If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>",
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Justify,
                        lineHeight = responsiveLargeTextSize() * 1.33f
                    )
                }
            }
            
            item {
                Spacer(modifier = Modifier.height(responsiveLargeSpacing() * 2))
            }
        }
    }
}
