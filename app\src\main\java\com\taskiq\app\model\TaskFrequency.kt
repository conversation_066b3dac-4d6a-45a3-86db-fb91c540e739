package com.taskiq.app.model

enum class TaskFrequency {
    ONE_TIME,
    HOURLY,
    DAILY,
    WEEKLY,
    MONTHLY,
    YEARLY,
    CUSTOM;

    fun getRepeatText(customDays: Int? = null, customHours: Int? = null): String {
        return when (this) {
            ONE_TIME -> "One Time"
            HOURLY -> "Hourly"
            DAILY -> "Daily"
            WEEKLY -> "Weekly"
            MONTHLY -> "Monthly"
            YEARLY -> "Yearly"
            CUSTOM -> {
                val days = customDays ?: 0
                val hours = customHours ?: 0
                
                when {
                    days > 0 && hours > 0 -> "Custom ($days days $hours hours)"
                    days > 0 -> "Custom ($days days)"
                    hours > 0 -> "Custom ($hours hours)"
                    else -> "Custom"
                }
            }
        }
    }

    companion object {
        fun fromString(value: String): TaskFrequency {
            return when (value.lowercase()) {
                "one time" -> ONE_TIME
                "hourly" -> HOURLY
                "daily" -> DAILY
                "weekly" -> WEEKLY
                "monthly" -> MONTHLY
                "yearly" -> YEARLY
                "custom" -> CUSTOM
                else -> ONE_TIME
            }
        }
    }
}

fun getRepeatText(frequency: TaskFrequency, customDays: Int? = null, customHours: Int? = null): String {
    return when (frequency) {
        TaskFrequency.ONE_TIME -> "Once"
        TaskFrequency.HOURLY -> "Hourly"
        TaskFrequency.DAILY -> "Daily"
        TaskFrequency.WEEKLY -> "Weekly"
        TaskFrequency.MONTHLY -> "Monthly"
        TaskFrequency.YEARLY -> "Yearly"
        TaskFrequency.CUSTOM -> {
            val days = customDays ?: 0
            val hours = customHours ?: 0
            
            when {
                days > 0 && hours > 0 -> "Every $days days $hours hours"
                days > 0 -> "Every $days days"
                hours > 0 -> "Every $hours hours"
                else -> "Custom"
            }
        }
    }
} 