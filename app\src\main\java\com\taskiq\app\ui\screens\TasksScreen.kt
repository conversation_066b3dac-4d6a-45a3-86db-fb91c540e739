package com.taskiq.app.ui.screens

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.derivedStateOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.LayoutDirection
import java.util.*

import com.taskiq.app.model.*
import com.taskiq.app.ui.components.*
import com.taskiq.app.viewmodel.TaskViewModel

import com.taskiq.app.ui.theme.rootContainerModifier
import com.taskiq.app.ui.theme.uniformContentBelowHeaderModifier
import com.taskiq.app.ui.theme.uniformContentPadding
import com.taskiq.app.ui.theme.uniformHorizontalPadding
import com.taskiq.app.ui.theme.uniformVerticalPadding
import com.taskiq.app.ui.theme.uniformInitialPadding
import com.taskiq.app.ui.theme.uniformSectionSpacing
import com.taskiq.app.ui.theme.uniformItemSpacing
import com.taskiq.app.ui.theme.uniformSmallSpacing
import com.taskiq.app.ui.theme.uniformBottomSpacing
import com.taskiq.app.ui.theme.responsiveButtonHeight
import com.taskiq.app.ui.theme.responsiveSmallIconSize
import com.taskiq.app.ui.theme.ProfessionalBlue
import com.taskiq.app.ui.theme.ProfessionalWhite

@Composable
fun TasksScreen(
    taskViewModel: TaskViewModel,
    onBackClick: () -> Unit = {},
    onShowAddTaskScreen: (Boolean) -> Unit = {},
    onScrollStateChanged: (Boolean) -> Unit = {} // Callback for scroll state changes
) {
    val tasks = taskViewModel.tasks
    val isLoading = taskViewModel.isLoading

    // Scroll state for hide-on-scroll behavior
    val listState = rememberLazyListState()
    var previousScrollOffset by remember { mutableStateOf(0) }

    val isScrollingDown by remember {
        derivedStateOf {
            val currentOffset = listState.firstVisibleItemScrollOffset
            val isScrollingDown = currentOffset > previousScrollOffset && currentOffset > 100
            previousScrollOffset = currentOffset
            isScrollingDown
        }
    }

    // Update scroll state - hide when scrolling down, show only when at top
    LaunchedEffect(listState.firstVisibleItemScrollOffset) {
        val isAtTop = listState.firstVisibleItemScrollOffset <= 50
        onScrollStateChanged(!isAtTop)
    }

    // Use uniform padding system for consistency across all screens
    
    var showSortOptions by remember { mutableStateOf(false) }
    var showCompletedSortOptions by remember { mutableStateOf(false) }
    var sortOption by remember { mutableStateOf("Due Date") }
    var completedTasksSortOrder by remember { mutableStateOf(CompletedTasksSortOrder.BY_DATE) }
    
    // For editing tasks
    var editingTask by remember { mutableStateOf<Task?>(null) }
    var showTaskAddition by remember { mutableStateOf(false) }
    
    // Effect to notify parent when task addition/editing screens are shown
    LaunchedEffect(showTaskAddition, editingTask) {
        onShowAddTaskScreen(showTaskAddition || editingTask != null)
    }
    
    // Handle task addition/editing in full screen
    if (showTaskAddition || editingTask != null) {
        TaskAdditionScreen(
            onDismiss = { 
                showTaskAddition = false
                editingTask = null
            },
            onAddTask = { task ->
                if (editingTask != null) {
                    taskViewModel.updateTask(task)
                } else {
                    taskViewModel.addTask(task)
                }
                showTaskAddition = false
                editingTask = null
            },
            taskViewModel = taskViewModel,
            editingTask = editingTask
        )
        return
    }
    
    // Filter tasks based on completion status and type
    val completedTasks = taskViewModel.getCompletedTasks() // Use ViewModel function that limits to 50
    val uncompletedTasks = tasks.filter { !it.isCompleted }

    val scheduledTasks = uncompletedTasks.filter { it.dueDate != null }
    val unplannedTasks = uncompletedTasks.filter { it.dueDate == null }

    // Sort completed tasks based on user preference (already limited to 50 by ViewModel)
    val sortedCompletedTasks = when (completedTasksSortOrder) {
        CompletedTasksSortOrder.BY_DATE -> completedTasks.sortedByDescending { it.completedAt ?: it.createdAt }
        CompletedTasksSortOrder.BY_TITLE -> completedTasks.sortedBy { it.title }
    }
    
    // Apply sort option to scheduled tasks
    val sortedScheduledTasks = when (sortOption) {
        "Due Date" -> scheduledTasks.sortedWith(compareBy { 
            it.dueDate?.let { date ->
                val today = Calendar.getInstance().apply {
                    set(Calendar.HOUR_OF_DAY, 0)
                    set(Calendar.MINUTE, 0)
                    set(Calendar.SECOND, 0)
                    set(Calendar.MILLISECOND, 0)
                }.time
                if (date.before(today)) -1 else date.time
            } ?: Long.MAX_VALUE
        })
        "Priority" -> scheduledTasks.sortedWith(compareByDescending { 
            when (it.priority) {
                TaskPriority.HIGH -> 3
                TaskPriority.MEDIUM -> 2
                TaskPriority.LOW -> 1
            }
        })
        "Title" -> scheduledTasks.sortedBy { it.title }
        else -> scheduledTasks.sortedWith(compareBy { it.dueDate?.time ?: Long.MAX_VALUE })
    }
    
    // Get daily tasks that need to be included with scheduled tasks
    val dailyTasks = taskViewModel.getTasksByFrequency(TaskFrequency.DAILY)
        .filter { !it.isCompleted }
    
    // Get important dates with plans (subtasks)
    val datesWithPlans = taskViewModel.importantDates.filter { importantDate ->
        importantDate.subtasks.isNotEmpty() && 
        !importantDate.isCompleted &&
        importantDate.dueDate != null
    }
    
    // Find the corresponding tasks for these dates
    val tasksForDatesWithPlans = datesWithPlans.mapNotNull { date ->
        val exactTitle = "Reminder for ${date.title}"
        taskViewModel.tasks.firstOrNull { task ->
            !task.isCompleted &&
            task.title.equals(exactTitle, ignoreCase = true) && 
            (task.dueDate?.let { dueDate ->
                date.dueDate?.let { dateDate ->
                    // Use same date comparison as in the viewmodel
                    val cal1 = Calendar.getInstance().apply { time = dueDate }
                    val cal2 = Calendar.getInstance().apply { time = dateDate }
                    
                    cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
                    cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH) &&
                    cal1.get(Calendar.DAY_OF_MONTH) == cal2.get(Calendar.DAY_OF_MONTH)
                }
            } ?: false)
        }
    }
    
    // Combine all scheduled tasks
    val allScheduledTasks = (sortedScheduledTasks + dailyTasks + tasksForDatesWithPlans)
        .distinctBy { it.id }
        .filter { !it.isCompleted }
        .sortedWith(compareBy { 
            it.dueDate?.let { date ->
                val today = Calendar.getInstance().apply {
                    set(Calendar.HOUR_OF_DAY, 0)
                    set(Calendar.MINUTE, 0)
                    set(Calendar.SECOND, 0)
                    set(Calendar.MILLISECOND, 0)
                }.time
                if (date.before(today)) -1 else date.time
            } ?: Long.MAX_VALUE
        })
    
    Scaffold(
        floatingActionButton = {
            FloatingActionButton(
                onClick = { showTaskAddition = true },
                containerColor = ProfessionalBlue,
                contentColor = ProfessionalWhite,
                modifier = Modifier.size(50.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "Add Task"
                )
            }
        }
    ) { innerPadding ->
        // Main content
        if (isLoading) {
            TaskLoadingState()
        } else if (tasks.isEmpty()) {
            TaskEmptyState(
                message = "You don't have any tasks yet. Add a task to get started!",
                actionLabel = "Add Task",
                onAction = { showTaskAddition = true }
            )
        } else {
            LazyColumn(
                state = listState,
                modifier = uniformContentBelowHeaderModifier(),
                contentPadding = uniformContentPadding(),
                verticalArrangement = Arrangement.spacedBy(uniformItemSpacing())
            ) {
                // Initial spacing - uniform across all screens
                item {
                    Spacer(modifier = Modifier.height(uniformInitialPadding()))
                }
                // ============================================
                // TASK SECTIONS ORGANIZATION
                // ============================================

                // Get all incomplete tasks
                val incompleteTasks = tasks.filter { !it.isCompleted }

                // ============================================
                // SECTION 1: SCHEDULED TASKS
                // ============================================
                if (scheduledTasks.isNotEmpty()) {
                    item {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "Scheduled Tasks (${scheduledTasks.size})",
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Bold
                            )

                            Box {
                                TextButton(
                                    onClick = { showSortOptions = true },
                                    shape = MaterialTheme.shapes.medium,
                                    modifier = Modifier.height(responsiveButtonHeight() * 0.6f),
                                    contentPadding = PaddingValues(horizontal = uniformHorizontalPadding(), vertical = uniformVerticalPadding())
                                ) {
                                    Text(
                                        "Sort by",
                                        style = MaterialTheme.typography.bodyMedium
                                    )
                                    Icon(
                                        imageVector = Icons.Default.ArrowDropDown,
                                        contentDescription = "Sort options",
                                        modifier = Modifier.padding(start = uniformSmallSpacing()).size(responsiveSmallIconSize())
                                    )
                                }

                                DropdownMenu(
                                    expanded = showSortOptions,
                                    onDismissRequest = { showSortOptions = false }
                                ) {
                                    DropdownMenuItem(
                                        text = { Text("Due Date") },
                                        onClick = {
                                            sortOption = "Due Date"
                                            showSortOptions = false
                                        }
                                    )
                                    DropdownMenuItem(
                                        text = { Text("Priority") },
                                        onClick = {
                                            sortOption = "Priority"
                                            showSortOptions = false
                                        }
                                    )
                                    DropdownMenuItem(
                                        text = { Text("Title") },
                                        onClick = {
                                            sortOption = "Title"
                                            showSortOptions = false
                                        }
                                    )
                                }
                            }
                        }
                    }

                    // Display all scheduled tasks as a flat list
                    items(allScheduledTasks) { task ->
                        // Check if this task is a date item (has "Reminder for" prefix)
                        val isDateItem = task.title.startsWith("Reminder for")
                        TaskItem(
                            task = task,
                            onTaskComplete = { taskViewModel.toggleTaskCompletion(task.id) },
                            onTaskDelete = { taskViewModel.deleteTask(task.id) },
                            onTaskEdit = { editingTask = it },
                            onSubtaskToggle = { taskId, subtaskId ->
                                taskViewModel.toggleSubtaskCompletion(taskId, subtaskId)
                            },
                            onAddSubtask = { taskId, subtask ->
                                taskViewModel.addSubtask(taskId, subtask)
                            },
                            isDate = isDateItem
                        )
                    }
                }

                // ============================================
                // SECTION 2: UNPLANNED TASKS
                // ============================================
                val plannedTasks = incompleteTasks.filter { it.dueDate == null }
                if (plannedTasks.isNotEmpty()) {
                    item {
                        Text(
                            text = "Unplanned Tasks (${plannedTasks.size})",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold
                        )
                    }

                    items(plannedTasks) { task ->
                        TaskItem(
                            task = task,
                            onTaskComplete = { taskViewModel.toggleTaskCompletion(task.id) },
                            onTaskDelete = { taskViewModel.deleteTask(task.id) },
                            onTaskEdit = { editingTask = it },
                            onSubtaskToggle = { taskId, subtaskId ->
                                taskViewModel.toggleSubtaskCompletion(taskId, subtaskId)
                            },
                            onAddSubtask = { taskId, subtask ->
                                taskViewModel.addSubtask(taskId, subtask)
                            }
                        )
                    }
                }

                // ============================================
                // SECTION 3: COMPLETED TASKS
                // ============================================
                if (sortedCompletedTasks.isNotEmpty()) {
                    item {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "Completed Tasks (${sortedCompletedTasks.size})",
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Bold,
                                color = MaterialTheme.colorScheme.onSurface
                            )

                            Box {
                                TextButton(
                                    onClick = { showCompletedSortOptions = true },
                                    shape = MaterialTheme.shapes.medium,
                                    modifier = Modifier.height(responsiveButtonHeight() * 0.6f),
                                    contentPadding = PaddingValues(horizontal = uniformHorizontalPadding(), vertical = uniformVerticalPadding())
                                ) {
                                    Text(
                                        "Sort by",
                                        style = MaterialTheme.typography.bodyMedium
                                    )
                                    Icon(
                                        imageVector = Icons.Default.ArrowDropDown,
                                        contentDescription = "Sort options",
                                        modifier = Modifier.padding(start = uniformSmallSpacing()).size(responsiveSmallIconSize())
                                    )
                                }

                                DropdownMenu(
                                    expanded = showCompletedSortOptions,
                                    onDismissRequest = { showCompletedSortOptions = false }
                                ) {
                                    DropdownMenuItem(
                                        text = {
                                            Text(
                                                "Date",
                                                style = MaterialTheme.typography.bodyMedium
                                            )
                                        },
                                        onClick = {
                                            completedTasksSortOrder = CompletedTasksSortOrder.BY_DATE
                                            showCompletedSortOptions = false
                                        },
                                        contentPadding = PaddingValues(horizontal = uniformHorizontalPadding() * 2, vertical = uniformVerticalPadding())
                                    )
                                    DropdownMenuItem(
                                        text = {
                                            Text(
                                                "Title",
                                                style = MaterialTheme.typography.bodyMedium
                                            )
                                        },
                                        onClick = {
                                            completedTasksSortOrder = CompletedTasksSortOrder.BY_TITLE
                                            showCompletedSortOptions = false
                                        },
                                        contentPadding = PaddingValues(horizontal = uniformHorizontalPadding() * 2, vertical = uniformVerticalPadding())
                                    )
                                }
                            }
                        }
                    }

                    // List completed tasks
                    items(sortedCompletedTasks) { task ->
                        TaskItem(
                            task = task,
                            onTaskComplete = { taskViewModel.toggleTaskCompletion(task.id) },
                            onTaskDelete = { taskViewModel.deleteTask(task.id) },
                            onTaskEdit = { editingTask = it },
                            onSubtaskToggle = { taskId, subtaskId ->
                                taskViewModel.toggleSubtaskCompletion(taskId, subtaskId)
                            },
                            onAddSubtask = { taskId, subtask ->
                                taskViewModel.addSubtask(taskId, subtask)
                            }
                        )
                    }
                }

                item {
                    Spacer(modifier = Modifier.height(uniformBottomSpacing())) // Space for FAB
                }
            }
        }
    }
}