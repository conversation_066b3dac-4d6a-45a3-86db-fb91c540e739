package com.taskiq.app.ui.screens

import android.os.Build
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Notifications
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.taskiq.app.service.NotificationService
import com.taskiq.app.ui.theme.ProfessionalBlue
import com.taskiq.app.ui.theme.ProfessionalWhite
import com.taskiq.app.ui.theme.cardBackgroundColor
import com.taskiq.app.ui.theme.topAppBarColor
import com.taskiq.app.utils.findActivity
import com.taskiq.app.ui.theme.responsiveLargeTextSize
import com.taskiq.app.ui.theme.responsiveSpacing
import com.taskiq.app.ui.theme.responsiveLargeSpacing
import com.taskiq.app.ui.theme.responsiveVerticalPadding
import com.taskiq.app.ui.theme.responsiveCardPadding
import com.taskiq.app.ui.theme.responsiveSmallSpacing
import com.taskiq.app.ui.theme.responsiveCardElevation
import com.taskiq.app.ui.theme.responsiveIconSize
import com.taskiq.app.ui.theme.responsiveHorizontalPadding
import com.taskiq.app.ui.theme.ssp
import com.taskiq.app.viewmodel.NotificationSettingsViewModel
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NotificationSettingsScreen(
    navController: NavController,
    viewModel: NotificationSettingsViewModel = viewModel()
) {
    val preferences by viewModel.preferences.collectAsState()
    val context = LocalContext.current
    val activity = context.findActivity()
    val notificationService = remember { NotificationService(context) }
    
    // Check and request notification permission
    LaunchedEffect(Unit) {
        activity?.let { notificationService.requestNotificationPermission(it) }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Notification Preferences",
                        fontWeight = FontWeight.Bold,
                        fontSize = 18.ssp()
                    )
                },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = topAppBarColor(),
                    titleContentColor = ProfessionalWhite,
                    navigationIconContentColor = ProfessionalWhite
                )
            )
        }
    ) { padding ->
        Surface(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding),
            color = MaterialTheme.colorScheme.background
        ) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .navigationBarsPadding() // Add padding for navigation bar
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                ) {
                    // Notification preferences list
                    LazyColumn(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(horizontal = responsiveSpacing() * 2, vertical = responsiveSpacing() * 2),
                        verticalArrangement = Arrangement.spacedBy(responsiveLargeSpacing() * 2)
                    ) {
                        item {
                            NotificationPreferenceItem(
                                title = "Task Reminders",
                                subtitle = "Get notified about upcoming tasks and deadlines",
                                checked = preferences.taskReminders,
                                onCheckedChange = viewModel::updateTaskReminders,
                                iconTint = Color(0xFF4CAF50) // Green
                            )
                        }

                        item {
                            NotificationPreferenceItem(
                                title = "Bill Due Dates",
                                subtitle = "Receive alerts for bill payment deadlines",
                                checked = preferences.billDueDates,
                                onCheckedChange = viewModel::updateBillDueDates,
                                iconTint = Color(0xFFF44336) // Red
                            )
                        }

                        item {
                            NotificationPreferenceItem(
                                title = "Important Dates",
                                subtitle = "Get reminders for important dates and events",
                                checked = preferences.importantDates,
                                onCheckedChange = viewModel::updateImportantDates,
                                iconTint = Color(0xFF2196F3) // Blue
                            )
                        }

                        item {
                            NotificationPreferenceItem(
                                title = "System Notifications",
                                subtitle = "Receive general app notifications and updates",
                                checked = preferences.systemNotifications,
                                onCheckedChange = viewModel::updateSystemNotifications,
                                iconTint = Color(0xFFFF9800) // Orange
                            )
                        }

                        item {
                            DailySummaryPreferenceItem(
                                title = "Daily Summary",
                                subtitle = "Get daily summary of tasks, bills, and important dates",
                                checked = preferences.dailySummaryEnabled,
                                onCheckedChange = viewModel::updateDailySummaryEnabled,
                                hour = preferences.dailySummaryHour,
                                minute = preferences.dailySummaryMinute,
                                onTimeChange = viewModel::updateDailySummaryTime,
                                iconTint = Color(0xFF9C27B0) // Purple
                            )
                        }
                        
                        // Additional information section
                        item {
                            Spacer(modifier = Modifier.height(responsiveLargeSpacing() * 2))

                            Card(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = responsiveVerticalPadding()),
                                colors = CardDefaults.cardColors(
                                    containerColor = cardBackgroundColor()
                                )
                            ) {
                                Column(
                                    modifier = Modifier.padding(responsiveCardPadding()),
                                    horizontalAlignment = Alignment.CenterHorizontally
                                ) {
                                    Text(
                                        text = "Notification Tips",
                                        style = MaterialTheme.typography.titleMedium,
                                        fontWeight = FontWeight.Bold,
                                        color = Color(0xFF2E7D32)
                                    )
                                    
                                    Spacer(modifier = Modifier.height(responsiveSpacing() * 2))
                                    
                                    Text(
                                        text = "Enable notifications to never miss important tasks, bills, and dates. You can always change these settings later.",
                                        style = MaterialTheme.typography.bodyMedium,
                                        textAlign = TextAlign.Center,
                                        color = Color(0xFF2E7D32)
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun NotificationPreferenceItem(
    title: String,
    subtitle: String,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    iconTint: Color
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = responsiveSmallSpacing() * 2),
        elevation = CardDefaults.cardElevation(
            defaultElevation = responsiveCardElevation() * 2
        ),
        colors = CardDefaults.cardColors(
            containerColor = cardBackgroundColor()
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(responsiveCardPadding()),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Icon with colored background
            Box(
                modifier = Modifier
                    .size(responsiveIconSize() * 2)
                    .clip(RoundedCornerShape(responsiveSpacing() * 2))
                    .background(iconTint.copy(alpha = 0.1f)),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Notifications,
                    contentDescription = null,
                    tint = iconTint,
                    modifier = Modifier.size(responsiveIconSize()).alpha(if (checked) 1f else 0.5f)
                )
            }

            // Text content
            Column(
                modifier = Modifier
                    .weight(1f)
                    .padding(horizontal = responsiveHorizontalPadding())
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = subtitle,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            // Switch
            Switch(
                checked = checked,
                onCheckedChange = onCheckedChange,
                colors = SwitchDefaults.colors(
                    checkedThumbColor = MaterialTheme.colorScheme.primary,
                    checkedTrackColor = MaterialTheme.colorScheme.primaryContainer,
                    uncheckedThumbColor = MaterialTheme.colorScheme.outline,
                    uncheckedTrackColor = MaterialTheme.colorScheme.surfaceVariant
                )
            )
        }
    }
}

@Composable
fun DailySummaryPreferenceItem(
    title: String,
    subtitle: String,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    hour: Int,
    minute: Int,
    onTimeChange: (Int, Int) -> Unit,
    iconTint: Color
) {
    var showTimePicker by remember { mutableStateOf(false) }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = responsiveSmallSpacing() * 2),
        elevation = CardDefaults.cardElevation(
            defaultElevation = responsiveCardElevation() * 2
        ),
        colors = CardDefaults.cardColors(
            containerColor = cardBackgroundColor()
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(responsiveCardPadding())
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Icon with colored background
                Box(
                    modifier = Modifier
                        .size(responsiveIconSize() * 2)
                        .clip(RoundedCornerShape(responsiveSpacing() * 2))
                        .background(iconTint.copy(alpha = 0.1f)),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Notifications,
                        contentDescription = null,
                        tint = iconTint,
                        modifier = Modifier.size(responsiveIconSize()).alpha(if (checked) 1f else 0.5f)
                    )
                }

                // Text content
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .padding(horizontal = responsiveHorizontalPadding())
                ) {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.SemiBold,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    Text(
                        text = subtitle,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                // Switch
                Switch(
                    checked = checked,
                    onCheckedChange = onCheckedChange,
                    colors = SwitchDefaults.colors(
                        checkedThumbColor = MaterialTheme.colorScheme.primary,
                        checkedTrackColor = MaterialTheme.colorScheme.primaryContainer,
                        uncheckedThumbColor = MaterialTheme.colorScheme.outline,
                        uncheckedTrackColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                )
            }

            // Time picker section (only shown when enabled)
            if (checked) {
                Spacer(modifier = Modifier.height(responsiveSpacing() * 3))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Summary Time:",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )

                    OutlinedButton(
                        onClick = { showTimePicker = true },
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = iconTint
                        )
                    ) {
                        val timeFormat = SimpleDateFormat("hh:mm a", Locale.getDefault())
                        val calendar = Calendar.getInstance().apply {
                            set(Calendar.HOUR_OF_DAY, hour)
                            set(Calendar.MINUTE, minute)
                        }
                        Text(timeFormat.format(calendar.time))
                    }
                }
            }
        }
    }

    // Time picker dialog
    if (showTimePicker) {
        TimePickerDialog(
            onTimeSelected = { selectedHour, selectedMinute ->
                onTimeChange(selectedHour, selectedMinute)
                showTimePicker = false
            },
            onDismiss = { showTimePicker = false },
            initialHour = hour,
            initialMinute = minute
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TimePickerDialog(
    onTimeSelected: (Int, Int) -> Unit,
    onDismiss: () -> Unit,
    initialHour: Int,
    initialMinute: Int
) {
    val timePickerState = rememberTimePickerState(
        initialHour = initialHour,
        initialMinute = initialMinute,
        is24Hour = false
    )

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Select Time") },
        text = {
            TimePicker(state = timePickerState)
        },
        confirmButton = {
            TextButton(
                onClick = {
                    onTimeSelected(timePickerState.hour, timePickerState.minute)
                }
            ) {
                Text("OK")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}