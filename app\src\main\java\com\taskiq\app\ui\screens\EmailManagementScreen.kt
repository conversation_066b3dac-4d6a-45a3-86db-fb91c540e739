package com.taskiq.app.ui.screens

import android.app.Activity
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.unit.sp
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.activity.compose.BackHandler
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.taskiq.app.ui.theme.cardBackgroundColor
import com.taskiq.app.ui.theme.topAppBarColor
import com.taskiq.app.ui.theme.ProfessionalBlue
import com.taskiq.app.ui.theme.ProfessionalWhite
import androidx.lifecycle.viewmodel.compose.viewModel
import com.taskiq.app.viewmodel.GmailAuthViewModel
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.rememberCoroutineScope
import kotlinx.coroutines.launch
import com.taskiq.app.viewmodel.AuthState
import androidx.compose.runtime.collectAsState

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EmailManagementScreen(
    linkedEmails: List<String>,
    isScanning: Boolean,
    onAddEmail: (String) -> Boolean,
    onRemoveEmail: (String) -> Unit,
    onScanEmails: () -> Unit,
    onBack: () -> Unit,
    onNavigateToGmailAuth: () -> Unit,
    onVerifyEmail: ((String, Activity) -> Boolean)? = null
) {
    var emailError by remember { mutableStateOf("") }
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    // Gmail Auth ViewModel for direct Google Sign-In
    val gmailAuthViewModel: GmailAuthViewModel = viewModel()
    val authState by gmailAuthViewModel.authState.collectAsState()

    // Handle system back button to ensure it goes back to Bills screen
    BackHandler {
        onBack()
    }

    // Google Sign-In launcher
    val googleSignInLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        android.util.Log.d("EmailManagementScreen", "Google Sign-In result received with code: ${result.resultCode}")
        android.util.Log.d("EmailManagementScreen", "Result data: ${result.data}")
        android.util.Log.d("EmailManagementScreen", "RESULT_OK = ${Activity.RESULT_OK}, RESULT_CANCELED = ${Activity.RESULT_CANCELED}")

        // Always try to handle the result, regardless of result code
        // Sometimes Google Sign-In returns RESULT_CANCELED even when successful
        if (result.data != null) {
            android.util.Log.d("EmailManagementScreen", "Result has data, attempting to handle sign-in result")
            gmailAuthViewModel.handleSignInResult(result.data)
        } else {
            android.util.Log.d("EmailManagementScreen", "No result data available")
            when (result.resultCode) {
                Activity.RESULT_CANCELED -> {
                    android.util.Log.d("EmailManagementScreen", "Google Sign-In was cancelled by user")
                    gmailAuthViewModel.resetAuthState()
                    emailError = "Google Sign-In was cancelled by user"
                }
                else -> {
                    android.util.Log.d("EmailManagementScreen", "Google Sign-In failed with result code: ${result.resultCode}")
                    gmailAuthViewModel.resetAuthState()
                    emailError = "Google Sign-In failed with code: ${result.resultCode}"
                }
            }
        }
    }

    // Handle auth state changes
    LaunchedEffect(authState) {
        when (authState) {
            is AuthState.SignedIn -> {
                val email = (authState as AuthState.SignedIn).email
                android.util.Log.d("EmailManagementScreen", "Email linked successfully: $email")

                if (onAddEmail(email)) {
                    emailError = "Gmail account linked successfully: $email. Email scanning will start in 5 minutes."

                    // Get Gmail service from GmailAuthViewModel and set it globally for real scanning
                    val gmailService = gmailAuthViewModel.getGmailService()
                    if (gmailService != null) {
                        android.util.Log.d("EmailManagementScreen", "Gmail service available, setting it globally for real scanning")
                        com.taskiq.app.viewmodel.GmailAuthViewModel.setGlobalGmailService(gmailService)

                        // Schedule delayed email scanning after 10 seconds to allow Gmail service initialization
                        android.util.Log.d("EmailManagementScreen", "Scheduling delayed email scanning in 10 seconds")
                        kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.Main).launch {
                            try {
                                android.util.Log.d("EmailManagementScreen", "Waiting 10 seconds before starting email scan...")
                                kotlinx.coroutines.delay(10 * 1000) // 10 seconds delay
                                android.util.Log.d("EmailManagementScreen", "Starting delayed email scan after 10 seconds")
                                onScanEmails()
                            } catch (e: Exception) {
                                android.util.Log.e("EmailManagementScreen", "Error during delayed scanning: ${e.message}")
                            }
                        }
                    } else {
                        android.util.Log.w("EmailManagementScreen", "Gmail service not available after OAuth")
                    }
                } else {
                    emailError = "Email already linked: $email"
                }
                gmailAuthViewModel.resetAuthState()
            }
            is AuthState.Error -> {
                emailError = (authState as AuthState.Error).message
                android.util.Log.e("EmailManagementScreen", "Auth error: $emailError")
            }
            else -> {}
        }
    }



    // Full-screen replacement - completely independent from parent
    Surface(
        modifier = Modifier.fillMaxSize(),
        color = MaterialTheme.colorScheme.background
    ) {
        Scaffold(
            topBar = {
                TopAppBar(
                    title = {
                        Text(
                            text = "Email Management",
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold
                        )
                    },
                    navigationIcon = {
                        IconButton(onClick = onBack) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "Back"
                            )
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = topAppBarColor(),
                        titleContentColor = ProfessionalWhite,
                        navigationIconContentColor = ProfessionalWhite
                    )
                )
            }
        ) { paddingValues ->
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
            ) {
                // Main email management content
                MainEmailContent(
                    linkedEmails = linkedEmails,
                    isScanning = isScanning,
                    onLinkNewGmail = {
                        // Directly trigger Google Sign-In
                        android.util.Log.d("EmailManagementScreen", "Banner clicked, starting Google Sign-In")
                        val signInIntent = gmailAuthViewModel.startGoogleSignIn()
                        if (signInIntent != null) {
                            android.util.Log.d("EmailManagementScreen", "Launching Google Sign-In intent")
                            googleSignInLauncher.launch(signInIntent)
                        } else {
                            android.util.Log.e("EmailManagementScreen", "Failed to create Google Sign-In intent")
                            emailError = "Failed to start Google Sign-In"
                        }
                    },
                    onRemoveEmail = onRemoveEmail,
                    onScanEmails = onScanEmails,
                    emailError = emailError,
                    modifier = Modifier.padding(8.dp)
                )


            }
        }
    }
}

@Composable
private fun MainEmailContent(
    linkedEmails: List<String>,
    isScanning: Boolean,
    onLinkNewGmail: () -> Unit,
    onRemoveEmail: (String) -> Unit,
    onScanEmails: () -> Unit,
    emailError: String,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxSize()
    ) {
        // Banner asking users to link email
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp),
            shape = RoundedCornerShape(12.dp),
            colors = CardDefaults.cardColors(
                containerColor = ProfessionalBlue.copy(alpha = 0.7f)
            ),
            onClick = onLinkNewGmail
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = "Link Your Gmail Account",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = ProfessionalWhite
                    )
                    Text(
                        text = "Get timely bill reminders by linking your Gmail account for automatic bill scanning.",
                        style = MaterialTheme.typography.bodyMedium,
                        color = ProfessionalWhite.copy(alpha = 0.9f)
                    )
                }
                
                Icon(
                    imageVector = Icons.Default.ChevronRight,
                    contentDescription = null,
                    tint = ProfessionalWhite,
                    modifier = Modifier.size(24.dp)
                )
            }
        }
        
        // Error message
        if (emailError.isNotEmpty()) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = if (emailError.contains("successfully"))
                        MaterialTheme.colorScheme.primaryContainer
                    else
                        MaterialTheme.colorScheme.errorContainer
                )
            ) {
                Text(
                    text = emailError,
                    color = if (emailError.contains("successfully"))
                        MaterialTheme.colorScheme.onPrimaryContainer
                    else
                        MaterialTheme.colorScheme.onErrorContainer,
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(16.dp)
                )
            }
        }
        
        // Linked Gmail accounts section
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(12.dp),
            colors = CardDefaults.cardColors(
                containerColor = cardBackgroundColor()
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Linked Gmail Accounts",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                if (linkedEmails.isEmpty()) {
                    Text(
                        text = "No Gmail accounts linked yet.\nTap the banner above to get started.",
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                        textAlign = TextAlign.Center,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 24.dp)
                    )
                } else {
                    LazyColumn {
                        items(linkedEmails) { email ->
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 8.dp),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Email,
                                        contentDescription = null,
                                        tint = MaterialTheme.colorScheme.primary
                                    )
                                    
                                    Spacer(modifier = Modifier.width(8.dp))
                                    
                                    Text(
                                        text = email,
                                        style = MaterialTheme.typography.bodyMedium
                                    )
                                    
                                    Spacer(modifier = Modifier.width(8.dp))
                                    
                                    Icon(
                                        imageVector = Icons.Default.CheckCircle,
                                        contentDescription = "Verified",
                                        tint = Color.Green,
                                        modifier = Modifier.size(16.dp)
                                    )
                                }
                                
                                IconButton(
                                    onClick = { onRemoveEmail(email) }
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Delete,
                                        contentDescription = "Remove Email",
                                        tint = Color.Red
                                    )
                                }
                            }
                            
                            HorizontalDivider()
                        }
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Scan button
        Button(
            onClick = onScanEmails,
            modifier = Modifier.fillMaxWidth(),
            enabled = linkedEmails.isNotEmpty() && !isScanning,
            colors = ButtonDefaults.buttonColors(
                containerColor = ProfessionalBlue,
                contentColor = ProfessionalWhite
            )
        ) {
            if (isScanning) {
                CircularProgressIndicator(
                    modifier = Modifier.size(20.dp),
                    strokeWidth = 2.dp,
                    color = MaterialTheme.colorScheme.onPrimary
                )
                Spacer(modifier = Modifier.width(8.dp))
            } else {
                Icon(
                    imageVector = Icons.Default.Refresh,
                    contentDescription = null
                )
                Spacer(modifier = Modifier.width(8.dp))
            }
            Text(text = if (isScanning) "Scanning..." else "Scan Gmail for Bills")
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Info text
        Text(
            text = "We access Gmail accounts logged into your device. We only scan for bills with your permission.",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth()
        )
    }
}




