package com.taskiq.app.ui.theme

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat

private val DarkColorScheme = darkColorScheme(
    primary = Blue40,
    onPrimary = Color.White,
    primaryContainer = BlueGrey900,
    onPrimaryContainer = Color.White,
    secondary = BlueGrey40,
    onSecondary = Color.White,
    tertiary = Teal40,
    background = Grey900,
    surface = Grey800,
    onSurface = Color.White,
    error = Color(0xFFCF6679)
)

private val LightColorScheme = lightColorScheme(
    primary = Blue80,
    onPrimary = Color.White,
    primaryContainer = Blue50,
    onPrimaryContainer = Blue80,
    secondary = BlueGrey80,
    onSecondary = Color.White,
    tertiary = Teal80,
    background = Color.White, // Changed from Grey50 to White
    surface = Color.White,
    onSurface = Color(0xFF121212),
    error = Color(0xFFB00020)
)

@Composable
fun TaskReminderTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    // Dynamic color is available on Android 12+
    dynamicColor: Boolean = true,
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = LocalContext.current
            if (darkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
        }
        darkTheme -> DarkColorScheme
        else -> LightColorScheme
    }
    
    // Update status bar color
    val view = LocalView.current
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            // Enable edge-to-edge display
            WindowCompat.setDecorFitsSystemWindows(window, false)
            // Use the non-deprecated approach for system bars
            WindowCompat.getInsetsController(window, view).apply {
                isAppearanceLightStatusBars = !darkTheme
                isAppearanceLightNavigationBars = !darkTheme
            }
            // Set system bar colors using the modern approach
            // Note: For modern apps, consider using edge-to-edge design
            // and handling insets properly instead of setting colors directly
        }
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        content = content
    )
}