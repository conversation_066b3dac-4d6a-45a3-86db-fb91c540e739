package com.taskiq.app

import com.taskiq.app.model.Task
import com.taskiq.app.model.TaskFrequency
import com.taskiq.app.model.TaskPriority
import org.junit.Assert.*
import org.junit.Test
import java.util.*
import java.util.concurrent.TimeUnit

class TaskTest {

    @Test
    fun testIsOverdueWithOverdueTask() {
        // Create a task with a due date in the past
        val pastDate = Calendar.getInstance().apply {
            add(Calendar.DAY_OF_YEAR, -1) // Yesterday
        }.time
        
        val task = Task(
            title = "Overdue Task",
            dueDate = pastDate,
            userId = "user1",
            isCompleted = false
        )
        
        // Test
        assertTrue("Task with past due date should be overdue", task.isOverdue())
    }
    
    @Test
    fun testIsOverdueWithFutureTask() {
        // Create a task with a due date in the future
        val futureDate = Calendar.getInstance().apply {
            add(Calendar.DAY_OF_YEAR, 1) // Tomorrow
        }.time
        
        val task = Task(
            title = "Future Task",
            dueDate = futureDate,
            userId = "user1",
            isCompleted = false
        )
        
        // Test
        assertFalse("Task with future due date should not be overdue", task.isOverdue())
    }
    
    @Test
    fun testIsOverdueWithCompletedTask() {
        // Create a completed task with a due date in the past
        val pastDate = Calendar.getInstance().apply {
            add(Calendar.DAY_OF_YEAR, -1) // Yesterday
        }.time
        
        val task = Task(
            title = "Completed Task",
            dueDate = pastDate,
            userId = "user1",
            isCompleted = true
        )
        
        // Test
        assertFalse("Completed task should not be overdue, regardless of due date", task.isOverdue())
    }
    
    @Test
    fun testIsRecurring() {
        // Test each frequency type
        val frequencies = listOf(
            TaskFrequency.ONE_TIME to false,
            TaskFrequency.HOURLY to true,
            TaskFrequency.DAILY to true,
            TaskFrequency.WEEKLY to true,
            TaskFrequency.MONTHLY to true,
            TaskFrequency.YEARLY to true,
            TaskFrequency.CUSTOM to true
        )
        
        for ((frequency, expectedResult) in frequencies) {
            val task = Task(
                title = "Task with $frequency frequency",
                frequency = frequency,
                userId = "user1"
            )
            
            assertEquals("Task with $frequency frequency should have isRecurring=$expectedResult", 
                expectedResult, task.isRecurring())
        }
    }
    
    @Test
    fun testIsOverdueRecurring() {
        // Create an overdue recurring task
        val pastDate = Calendar.getInstance().apply {
            add(Calendar.DAY_OF_YEAR, -1) // Yesterday
        }.time
        
        val task = Task(
            title = "Overdue Recurring Task",
            dueDate = pastDate,
            frequency = TaskFrequency.DAILY,
            userId = "user1",
            isCompleted = false
        )
        
        // Test
        assertTrue("Overdue recurring task should return true for isOverdueRecurring", 
            task.isOverdueRecurring())
        
        // Test a task that is overdue but not recurring
        val overdueNonRecurringTask = Task(
            title = "Overdue Non-recurring Task",
            dueDate = pastDate,
            frequency = TaskFrequency.ONE_TIME,
            userId = "user1",
            isCompleted = false
        )
        
        assertFalse("Overdue non-recurring task should return false for isOverdueRecurring", 
            overdueNonRecurringTask.isOverdueRecurring())
    }
    
    @Test
    fun testGetNextOccurrenceDate() {
        // Test different frequencies to ensure next occurrence date is calculated correctly
        
        // Start with a date in the past to simulate an overdue task
        val pastDate = Calendar.getInstance().apply {
            add(Calendar.DAY_OF_YEAR, -2) // 2 days ago
            set(Calendar.HOUR_OF_DAY, 10)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }.time
        
        // Daily task
        val dailyTask = Task(
            title = "Daily Task",
            dueDate = pastDate,
            frequency = TaskFrequency.DAILY,
            userId = "user1"
        )
        
        val nextDailyDate = dailyTask.getNextOccurrenceDate()
        assertNotNull("Next occurrence date should not be null for daily task", nextDailyDate)
        
        val now = Date()
        assertTrue("Next daily occurrence should be in the future", 
            nextDailyDate!!.after(now) || nextDailyDate.time == now.time)
        
        // Weekly task
        val weeklyTask = Task(
            title = "Weekly Task",
            dueDate = pastDate,
            frequency = TaskFrequency.WEEKLY,
            userId = "user1"
        )
        
        val nextWeeklyDate = weeklyTask.getNextOccurrenceDate()
        assertNotNull("Next occurrence date should not be null for weekly task", nextWeeklyDate)
        assertTrue("Next weekly occurrence should be in the future", 
            nextWeeklyDate!!.after(now) || nextWeeklyDate.time == now.time)
        
        // Custom frequency task
        val customTask = Task(
            title = "Custom Task",
            dueDate = pastDate,
            frequency = TaskFrequency.CUSTOM,
            userId = "user1",
            customFrequencyDays = 3,
            customFrequencyHours = 0
        )
        
        val nextCustomDate = customTask.getNextOccurrenceDate()
        assertNotNull("Next occurrence date should not be null for custom task", nextCustomDate)
        assertTrue("Next custom occurrence should be in the future", 
            nextCustomDate!!.after(now) || nextCustomDate.time == now.time)
    }
    
    @Test
    fun testGenerateNextOccurrence() {
        // Create a task
        val pastDate = Calendar.getInstance().apply {
            add(Calendar.DAY_OF_YEAR, -1) // Yesterday
        }.time
        
        val originalTask = Task(
            id = "task-1",
            title = "Original Daily Task",
            description = "Test description",
            dueDate = pastDate,
            frequency = TaskFrequency.DAILY,
            priority = TaskPriority.HIGH,
            isCompleted = true, // Completed
            userId = "user1",
            completedAt = Date()
        )
        
        // Generate next occurrence
        val nextTask = originalTask.generateNextOccurrence()
        
        // Verify properties
        assertNotEquals("Next task should have a different ID", originalTask.id, nextTask.id)
        assertEquals("Title should be the same", originalTask.title, nextTask.title)
        assertEquals("Description should be the same", originalTask.description, nextTask.description)
        assertEquals("Frequency should be the same", originalTask.frequency, nextTask.frequency)
        assertEquals("Priority should be the same", originalTask.priority, nextTask.priority)
        
        // Next occurrence should not be completed
        assertFalse("Next occurrence should not be marked as completed", nextTask.isCompleted)
        assertNull("Next occurrence should not have a completedAt date", nextTask.completedAt)
        
        // Due date should be in the future
        val now = Date()
        assertTrue("Next occurrence due date should be in the future", 
            nextTask.dueDate!!.after(now) || nextTask.dueDate.time == now.time)
    }
} 