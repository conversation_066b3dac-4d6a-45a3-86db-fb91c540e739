package com.taskiq.app.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.taskiq.app.ui.theme.ProfessionalWhite
import com.taskiq.app.ui.theme.topAppBarColor
import com.taskiq.app.ui.theme.sdp
import com.taskiq.app.ui.theme.ssp
import com.taskiq.app.ui.theme.responsiveLargeSpacing
import com.taskiq.app.ui.theme.responsiveSpacing
import com.taskiq.app.ui.theme.responsiveLargeTextSize
import com.taskiq.app.ui.theme.responsiveSmallSpacing

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AboutUsScreen(
    navController: NavController
) {
    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "About Us",
                        fontWeight = FontWeight.Bold,
                        fontSize = 18.ssp()
                    )
                },
                navigationIcon = {
                    IconButton(
                        onClick = { navController.popBackStack() },
                        modifier = Modifier.padding(end = 1.sdp())
                    ) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = topAppBarColor(),
                    titleContentColor = ProfessionalWhite,
                    navigationIconContentColor = ProfessionalWhite
                ),
                windowInsets = WindowInsets.statusBars
            )
        },
        contentWindowInsets = WindowInsets(0, 0, 0, 0)
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(responsiveLargeSpacing() * 3),
            verticalArrangement = Arrangement.spacedBy(responsiveLargeSpacing() * 3)
        ) {
            item {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "TaskIQ",
                        style = MaterialTheme.typography.headlineLarge,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )

                    Spacer(modifier = Modifier.height(responsiveSpacing() * 2))

                    Text(
                        text = "Version 1.0.0",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            item {
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = "Our Mission",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )

                    Spacer(modifier = Modifier.height(responsiveSpacing() * 3))

                    Text(
                        text = "TaskIQ is designed to help you stay organized and productive by managing your tasks, important dates, and bills all in one place. Our goal is to simplify your daily life and ensure you never miss important deadlines.",
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Justify,
                        lineHeight = responsiveLargeTextSize() * 1.33f
                    )
                }
            }

            item {
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = "Key Features",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )

                    Spacer(modifier = Modifier.height(responsiveSpacing() * 3))

                    val features = listOf(
                        "Task Management with priorities and subtasks",
                        "Important Dates tracking and reminders",
                        "Bill Management with automatic email scanning",
                        "Google Drive backup and restore",
                        "Cross-device synchronization",
                        "Smart notifications and reminders"
                    )

                    features.forEach { feature ->
                        Row(
                            modifier = Modifier.padding(vertical = responsiveSmallSpacing() * 2)
                        ) {
                            Text(
                                text = "• ",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.primary,
                                fontWeight = FontWeight.Bold
                            )
                            Text(
                                text = feature,
                                style = MaterialTheme.typography.bodyLarge,
                                modifier = Modifier.weight(1f)
                            )
                        }
                    }
                }
            }

            item {
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = "Contact Us",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )

                    Spacer(modifier = Modifier.height(responsiveSpacing() * 3))

                    Text(
                        text = "We'd love to hear from you! If you have any questions, suggestions, or feedback, please don't hesitate to reach out to us.",
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Justify,
                        lineHeight = responsiveLargeTextSize() * 1.33f
                    )

                    Spacer(modifier = Modifier.height(responsiveLargeSpacing() * 2))

                    Text(
                        text = "Email: <EMAIL>",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.primary,
                        fontWeight = FontWeight.Medium
                    )

                    Spacer(modifier = Modifier.height(responsiveSpacing() * 2))

                    Text(
                        text = "Website: www.taskiq.com",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.primary,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
            
            item {
                Spacer(modifier = Modifier.height(responsiveLargeSpacing() * 2))
            }
        }
    }
}
