package com.taskiq.app.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material.icons.filled.Notifications
import androidx.compose.material.icons.filled.Money
import androidx.compose.material.icons.filled.Task
import androidx.compose.material.icons.filled.DeleteSweep
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import androidx.activity.compose.BackHandler
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.compose.currentBackStackEntryAsState
import com.taskiq.app.model.Bill
import com.taskiq.app.model.Task
import com.taskiq.app.model.TaskPriority
import com.taskiq.app.ui.navigation.Routes
import com.taskiq.app.ui.navigation.BottomNavItem
import com.taskiq.app.ui.theme.ProfessionalBlue
import com.taskiq.app.ui.theme.ProfessionalWhite
import com.taskiq.app.ui.theme.contentBelowHeaderModifier
import com.taskiq.app.ui.theme.standardContentPadding
import com.taskiq.app.viewmodel.TaskViewModel
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.Locale
import java.util.Date
import java.util.concurrent.TimeUnit
import android.content.Context
import androidx.lifecycle.ViewModelProvider
import android.app.Application
import androidx.compose.material3.ColorScheme
import androidx.compose.ui.graphics.luminance
import com.taskiq.app.ui.theme.rootContainerModifier
import com.taskiq.app.ui.theme.cardBackgroundColor
import com.taskiq.app.ui.theme.topAppBarColor
import com.taskiq.app.ui.theme.sdp

data class NotificationItem(
    val id: String,
    val title: String,
    val description: String,
    val type: NotificationType,
    val date: String,
    val dateForSorting: Date?, // Added for sorting
    val priority: Boolean = false,
    val timestamp: Long = System.currentTimeMillis() // Timestamp for when notification was created
)

enum class NotificationType {
    TASK, BILL, DATE
}

// Maximum number of notifications to display
private const val MAX_NOTIFICATIONS = 30

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NotificationScreen(
    navController: NavController,
    taskViewModel: TaskViewModel = viewModel(
        factory = ViewModelProvider.AndroidViewModelFactory((LocalContext.current).applicationContext as Application)
    )
) {
    val context = LocalContext.current

    // Handle system back button
    BackHandler {
        // Simply pop back to previous screen
        navController.popBackStack()
    }

    // Get stored notifications
    val notificationItems = remember {
        getSavedNotifications(context)
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Notifications",
                        color = ProfessionalWhite,
                        fontWeight = FontWeight.Bold,
                        fontSize = 24.sp
                    )
                },
                navigationIcon = {
                    IconButton(
                        onClick = {
                            // Simply pop back to previous screen
                            navController.popBackStack()
                        },
                        modifier = Modifier.padding(end = 2.sdp())
                    ) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back",
                            tint = ProfessionalWhite
                        )
                    }
                },
                actions = {
                    // Delete button removed as requested
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = topAppBarColor(),
                    titleContentColor = ProfessionalWhite,
                    navigationIconContentColor = ProfessionalWhite,
                    actionIconContentColor = ProfessionalWhite
                )
            )
        }
    ) { padding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
        ) {
            if (notificationItems.isEmpty()) {
                // Show empty state
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Notifications,
                        contentDescription = null,
                        modifier = Modifier
                            .size(120.dp)
                            .padding(bottom = 24.dp),
                        tint = Color(0xFF6200EE)
                    )
                    
                    Text(
                        text = "No Notifications",
                        style = MaterialTheme.typography.headlineMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onBackground,
                        textAlign = TextAlign.Center
                    )
                    
                    Spacer(modifier = Modifier.height(8.sdp()))
                    
                    Text(
                        text = "You're all caught up! We'll notify you when there's something new.",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.padding(horizontal = 32.sdp())
                    )
                }
            } else {
                // Show list of notifications
                LazyColumn(
                    modifier = contentBelowHeaderModifier(),
                    contentPadding = standardContentPadding()
                ) {
                    items(notificationItems) { notification ->
                        NotificationCard(notification = notification)
                    }
                    
                    item {
                        Spacer(modifier = Modifier.height(80.sdp())) // Bottom padding
                    }
                }
            }
        }
    }
}

@Composable
fun NotificationCard(notification: NotificationItem) {
    // Card background colors with better contrast for both light and dark modes
    val isDarkMode = MaterialTheme.colorScheme.isFromStyle(dark = true) // Check if dark mode
    
    val cardColor = when (notification.type) {
        NotificationType.TASK -> if (notification.priority) {
            if (isDarkMode) Color(0xFFFFF3E0) else Color(0xFFFFF3E0)
        } else {
            if (isDarkMode) Color(0xFFE3F2FD) else Color(0xFFE3F2FD)
        }
        NotificationType.BILL -> if (isDarkMode) Color(0xFFE8F5E9) else Color(0xFFE8F5E9)
        NotificationType.DATE -> if (isDarkMode) Color(0xFFF3E5F5) else Color(0xFFF3E5F5)
    }
    
    val textColor = if (isDarkMode) Color(0xFF000000) else MaterialTheme.colorScheme.onSurface
    
    val icon = when (notification.type) {
        NotificationType.TASK -> Icons.Default.Task
        NotificationType.BILL -> Icons.Default.Money
        NotificationType.DATE -> Icons.Default.DateRange
    }
    
    val iconTint = when (notification.type) {
        NotificationType.TASK -> if (notification.priority) Color(0xFFFFB74D) else Color(0xFF64B5F6)
        NotificationType.BILL -> Color(0xFF81C784)
        NotificationType.DATE -> Color(0xFFCE93D8)
    }
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = cardColor
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = iconTint,
                modifier = Modifier.size(24.dp)
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = notification.title,
                    style = MaterialTheme.typography.titleMedium,
                    color = textColor
                )
                Text(
                    text = notification.description,
                    style = MaterialTheme.typography.bodyMedium,
                    color = textColor.copy(alpha = 0.7f)
                )
            }
        }
    }
}

private fun formatDate(date: java.util.Date): String {
    val formatter = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())
    return formatter.format(date)
}

// Get saved notifications from SharedPreferences
private fun getSavedNotifications(context: android.content.Context): List<NotificationItem> {
    val sharedPrefs = context.getSharedPreferences("notifications_history", android.content.Context.MODE_PRIVATE)
    val notificationsJson = sharedPrefs.getString("notifications", "[]")
    
    val gson = Gson()
    val type = object : TypeToken<List<NotificationItem>>() {}.type
    val notifications: List<NotificationItem> = gson.fromJson(notificationsJson, type)
    
    // Return most recent notifications first, limited to MAX_NOTIFICATIONS
    return notifications.sortedByDescending { it.timestamp }.take(MAX_NOTIFICATIONS)
}

// Helper function to save notifications to SharedPreferences
fun saveNotification(context: android.content.Context, notification: NotificationItem) {
    val sharedPrefs = context.getSharedPreferences("notifications_history", android.content.Context.MODE_PRIVATE)
    val notificationsJson = sharedPrefs.getString("notifications", "[]")
    
    val gson = Gson()
    val type = object : TypeToken<MutableList<NotificationItem>>() {}.type
    val notifications: MutableList<NotificationItem> = gson.fromJson(notificationsJson, type)
    
    // Add the new notification
    notifications.add(notification)
    
    // Keep only the most recent MAX_NOTIFICATIONS
    val recentNotifications = notifications.sortedByDescending { it.timestamp }.take(MAX_NOTIFICATIONS)
    
    // Save back to SharedPreferences
    val editor = sharedPrefs.edit()
    editor.putString("notifications", gson.toJson(recentNotifications))
    editor.apply()
}

// Extension function to check if a ColorScheme is dark or light
fun ColorScheme.isFromStyle(dark: Boolean): Boolean {
    // Use the background color's luminance to determine if we're in dark mode
    // Dark colors have low luminance values
    val luminance = this.background.luminance()
    return if (dark) {
        luminance < 0.5f  // Dark mode has low luminance
    } else {
        luminance >= 0.5f // Light mode has high luminance
    }
}