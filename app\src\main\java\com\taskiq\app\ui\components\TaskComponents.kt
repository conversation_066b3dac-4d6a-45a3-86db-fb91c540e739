package com.taskiq.app.ui.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.background

import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectHorizontalDragGestures

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height

import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Delete

import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Checkbox
import androidx.compose.material3.CheckboxDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.taskiq.app.model.Subtask
import com.taskiq.app.model.Task
import com.taskiq.app.ui.theme.*
import com.taskiq.app.model.TaskFrequency
import com.taskiq.app.model.TaskPriority
import com.taskiq.app.model.getRepeatText
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.UUID
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.key
import androidx.compose.ui.input.key.onKeyEvent
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.ui.text.input.ImeAction
import com.taskiq.app.ui.theme.fullWidthContentModifier
import com.taskiq.app.ui.theme.standardHorizontalPadding
import androidx.compose.ui.platform.LocalDensity
import com.taskiq.app.ui.theme.taskItemBackgroundColor
import com.taskiq.app.ui.theme.cardTextColor
import com.taskiq.app.ui.theme.cardSecondaryTextColor
import com.taskiq.app.ui.theme.cardDescriptionTextColor
import com.taskiq.app.ui.theme.sdp

@Composable
fun TaskLoadingState() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .padding(responsiveLargeSpacing()),
        contentAlignment = Alignment.Center
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            CircularProgressIndicator(
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(responsiveIconSize())
            )

            Spacer(modifier = Modifier.height(responsiveLargeSpacing()))
            
            Text(
                text = "Loading tasks...",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
fun TaskItem(
    task: Task,
    onTaskComplete: (String) -> Unit,
    onTaskDelete: ((String) -> Unit)? = null,
    onTaskEdit: ((Task) -> Unit)? = null,
    onSubtaskToggle: ((String, String) -> Unit)? = null,
    onAddSubtask: ((String, Subtask) -> Unit)? = null,
    isDate: Boolean = false,
    isDashboard: Boolean = false,
    disableExpand: Boolean = false
) {
    // Calculate date difference for color coding
    val todayCal = Calendar.getInstance().apply {
        set(Calendar.HOUR_OF_DAY, 0)
        set(Calendar.MINUTE, 0) 
        set(Calendar.SECOND, 0)
        set(Calendar.MILLISECOND, 0)
    }
    
    val taskCal = Calendar.getInstance().apply { 
        time = task.dueDate ?: todayCal.time
        set(Calendar.HOUR_OF_DAY, 0)
        set(Calendar.MINUTE, 0)
        set(Calendar.SECOND, 0)
        set(Calendar.MILLISECOND, 0)
    }
    
    val daysDifference = ((taskCal.timeInMillis - todayCal.timeInMillis) / (24 * 60 * 60 * 1000))
    
    // Determine title color based on how close the date is and priority
    val titleColor = if (isDate) {
        when {
            daysDifference < 0 -> Color.Red  // Past date
            daysDifference == 0L -> Color(0xFFFF6D00)  // Today (Orange)
            daysDifference <= 3 -> Color(0xFFFFB300)  // Within 3 days (Amber)
            daysDifference <= 7 -> Color(0xFF2196F3)  // Within a week (Blue)
            else -> MaterialTheme.colorScheme.primary  // Beyond a week
        }
    } else {
        when (task.priority) {
            TaskPriority.HIGH -> Color(0xFFFF4081)  // Pink
            TaskPriority.MEDIUM -> Color(0xFFAA66CC)  // Purple
            TaskPriority.LOW -> Color(0xFF4CAF50)  // Green
        }
    }

    // For swipe-to-reveal edit
    var offsetX by remember { mutableStateOf(0f) }
    val density = LocalDensity.current
    val editThresholdPx = with(density) { (-80.sdp()).toPx() }
    val offsetDp = animateFloatAsState(targetValue = offsetX, label = "offset")
    
    // Track if we've triggered the edit action to prevent multiple triggers
    var editTriggered by remember { mutableStateOf(false) }
    
    // Check if task is overdue - use the Task.isOverdue() method from our model
    val isOverdue = task.isOverdue()
    
    val dateFormat = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())
    val timeFormat = SimpleDateFormat("h:mm a", Locale.getDefault())
    var expanded by remember { mutableStateOf(false) }
    
    // For adding subtasks
    var newSubtaskTitle by remember { mutableStateOf("") }
    var isAddingSubtask by remember { mutableStateOf(false) } // State to track if adding a subtask
    
    // Define colors for overdue tasks
    val overdueBorderColor = Color(0xFFFF5252) // Bright red for border
    val overdueBackgroundColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 1f) // Restore normal background
    
    // Reset the edit triggered flag when offset returns to 0
    LaunchedEffect(offsetX) {
        if (offsetX == 0f) {
            editTriggered = false
        } else if (offsetX <= editThresholdPx && !editTriggered && onTaskEdit != null) {
            // Trigger edit action when threshold is reached
            onTaskEdit(task)
            editTriggered = true
            // Animate back to start position
            offsetX = 0f
        }
    }
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = responsiveContentPadding())
            .offset(x = offsetDp.value.dp, y = 0.dp)
            .pointerInput(Unit) {
                if (onTaskEdit != null) {
                    detectHorizontalDragGestures(
                        onDragEnd = {
                            // If not past threshold, animate back to start
                            if (offsetX > editThresholdPx) {
                                offsetX = 0f
                            }
                        },
                        onDragCancel = { offsetX = 0f },
                        onHorizontalDrag = { _, dragAmount ->
                            // Only allow dragging to the left (negative values)
                            val newOffset = (offsetX + dragAmount).coerceAtMost(0f)
                            offsetX = newOffset
                        }
                    )
                }
            },
        colors = CardDefaults.cardColors(
            containerColor = taskItemBackgroundColor()
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = responsiveCardElevation()
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .animateContentSize()
                .then(
                    if (!disableExpand) {
                        Modifier.clickable { expanded = !expanded }
                    } else {
                        Modifier
                    }
                )
        ) {
            // Main task row
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        vertical = 8.sdp(),
                        horizontal = if (isDate && isDashboard) 8.sdp() else 0.sdp()
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Checkbox - show for regular tasks and date items with plans in Today's Tasks
                if (!(isDate && isDashboard) || (isDate && isDashboard && task.title.startsWith("Reminder for"))) {
                    Checkbox(
                        checked = task.isCompleted,
                        onCheckedChange = { onTaskComplete(task.id) }
                    )
                }
                
                // Task details
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = if (isDate && isDashboard) 0.sdp() else 8.sdp())
                ) {
                    // Title with priority color
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(4.sdp())
                    ) {
                        if (isOverdue && !isDate) {
                            Icon(
                                imageVector = Icons.Default.Warning,
                                contentDescription = "Overdue Task",
                                tint = Color.Red,
                                modifier = Modifier.size(16.sdp())
                            )
                        }
                        
                        Text(
                            text = task.title,
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = if (isOverdue || (isDate && daysDifference <= 3)) FontWeight.Bold else FontWeight.Medium,
                            color = if (task.isCompleted && !(isDate && isDashboard)) {
                                // Use preserved completion color if available, otherwise use secondary text color
                                task.completionColor?.let { colorString ->
                                    try {
                                        Color(android.graphics.Color.parseColor(colorString))
                                    } catch (e: Exception) {
                                        cardSecondaryTextColor()
                                    }
                                } ?: cardSecondaryTextColor()
                            } else if (isOverdue && !isDate)
                                Color.Red
                            else
                                titleColor,
                            textDecoration = if (task.isCompleted && !(isDate && isDashboard)) TextDecoration.LineThrough else null,
                            modifier = Modifier.weight(1f)
                        )
                    }
                    
                    // Description
                    if (task.description.isNotBlank()) {
                        Text(
                            text = task.description,
                            style = MaterialTheme.typography.bodyMedium,
                            color = cardDescriptionTextColor(),
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                            modifier = Modifier.padding(top = 2.sdp())
                        )
                    }
                    
                    // Date and frequency info
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.sdp()),
                        modifier = Modifier.padding(top = 2.sdp())
                    ) {
                        // Due date with time
                        task.dueDate?.let { date ->
                            // Calculate relative date text (Today, Tomorrow, etc.)
                            val dateText = when (daysDifference) {
                                -1L -> "Yesterday"
                                0L -> "Today"
                                1L -> "Tomorrow"
                                in 2..7 -> "$daysDifference days from now" 
                                else -> dateFormat.format(date)
                            }
                            
                            // Always show time - if reminderTime is available, use it, otherwise extract time from dueDate
                            val timeText = task.reminderTime?.let { 
                                " at " + timeFormat.format(it) 
                            } ?: " at " + timeFormat.format(date) // Extract time from dueDate if reminderTime is null
                            
                            Text(
                                text = dateText + timeText,
                                style = MaterialTheme.typography.bodySmall,
                                color = if (isOverdue && !isDate) Color.Red else cardSecondaryTextColor(),
                                fontWeight = if (isOverdue || (isDate && daysDifference <= 3)) FontWeight.Medium else FontWeight.Normal
                            )
                        }
                        
                        // Show subtask completion progress for all tasks with subtasks
                        if (task.subtasks.isNotEmpty()) {
                            Text(
                                text = "${task.subtasks.count { it.isCompleted }}/${task.subtasks.size} ${if (isDate) "plans" else "subtasks"} ${if (isDate) "executed" else "completed"}",
                                style = MaterialTheme.typography.bodySmall,
                                color = Color(0xFF1565C0)  // Dark blue color for better visibility
                            )
                        }
                          
                        // Frequency info (if not one-time)
                        if (task.frequency != TaskFrequency.ONE_TIME) {
                            Text(
                                text = task.frequency.getRepeatText(),
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
                
                // Edit and Delete icons removed for cleaner UI
            }
            
            // Subtasks section (visible when expanded)
            AnimatedVisibility(
                visible = expanded,
                enter = fadeIn() + slideInHorizontally(),
                exit = fadeOut()
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 16.sdp(), end = 16.sdp(), bottom = 8.sdp())
                ) {
                    HorizontalDivider(
                        modifier = Modifier.padding(bottom = 4.sdp()),
                        color = Color(0xFF424242)  // Darker gray for better visibility
                    )
                    
                    // Show existing subtasks
                    if (task.subtasks.isNotEmpty()) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 4.sdp())
                        ) {
                            task.subtasks.forEach { subtask ->
                                var isEditingSubtask by remember { mutableStateOf(false) }
                                var editedSubtaskTitle by remember { mutableStateOf(subtask.title) }
                                
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .clickable(enabled = !isEditingSubtask && !task.isCompleted) { 
                                            if (!task.isCompleted) {
                                                isEditingSubtask = true
                                            }
                                        }
                                ) {
                                    Checkbox(
                                        checked = subtask.isCompleted,
                                        onCheckedChange = { 
                                            onSubtaskToggle?.invoke(task.id, subtask.id)
                                        },
                                        enabled = (isDate || !task.isCompleted) && onSubtaskToggle != null,
                                        colors = CheckboxDefaults.colors(
                                            uncheckedColor = Color(0xFF424242),  // Darker gray for better visibility
                                            checkedColor = MaterialTheme.colorScheme.primary,
                                            checkmarkColor = MaterialTheme.colorScheme.onPrimary
                                        )
                                    )
                                    
                                    // Editable subtask text
                                    if (isEditingSubtask) {
                                        BasicTextField(
                                            value = editedSubtaskTitle,
                                            onValueChange = { newValue -> editedSubtaskTitle = newValue },
                                            modifier = Modifier
                                                .weight(1f)
                                                .padding(start = 8.sdp(), end = 8.sdp())
                                                .onKeyEvent { keyEvent ->
                                                    if (keyEvent.key == Key.Enter) {
                                                        if (editedSubtaskTitle.isNotBlank()) {
                                                            // Update the subtask title
                                                            val updatedSubtask = subtask.copy(title = editedSubtaskTitle)
                                                            // Use the same function but with the same subtask ID to update instead of create
                                                            onAddSubtask?.invoke(task.id, updatedSubtask)
                                                            isEditingSubtask = false // Exit editing mode
                                                        }
                                                        true
                                                    } else {
                                                        false
                                                    }
                                                },
                                            singleLine = true,
                                            textStyle = MaterialTheme.typography.bodyMedium.copy(color = MaterialTheme.colorScheme.onSurface),
                                            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
                                            keyboardActions = KeyboardActions(onDone = {
                                                if (editedSubtaskTitle.isNotBlank()) {
                                                    val updatedSubtask = subtask.copy(title = editedSubtaskTitle)
                                                    onAddSubtask?.invoke(task.id, updatedSubtask)
                                                    isEditingSubtask = false
                                                }
                                            }),
                                            decorationBox = { innerTextField ->
                                                Box { innerTextField() }
                                            }
                                        )
                                    } else {
                                        Text(
                                            text = subtask.title,
                                            style = MaterialTheme.typography.bodyMedium,
                                            textDecoration = if (subtask.isCompleted) TextDecoration.LineThrough else null,
                                            color = if (subtask.isCompleted)
                                                cardSecondaryTextColor()
                                            else
                                                cardTextColor(),
                                            modifier = Modifier
                                                .weight(1f)
                                                .padding(start = 8.sdp())
                                        )
                                    }
                                }
                            }
                        }
                    }

                    // Add Subtask/Make Plan Button - hide on Dashboard screen
                    if (!isAddingSubtask && onAddSubtask != null && (!task.isCompleted || isDate) && !isDashboard) {
                        TextButton(
                            onClick = { isAddingSubtask = true },
                            modifier = Modifier.padding(start = 16.sdp(), top = 4.sdp())
                        ) {
                            Icon(
                                imageVector = Icons.Default.Add,
                                contentDescription = if (isDate) "Make Plan" else "Add Subtask",
                                tint = Color(0xFF1565C0)  // Dark blue color for better visibility
                            )
                            Spacer(modifier = Modifier.width(4.sdp()))
                            Text(
                                text = if (isDate) "Make Plan" else "Add Subtask",
                                color = Color(0xFF1565C0)  // Dark blue color for better visibility
                            )
                        }
                    }

                    // Add subtask input field
                    if (isAddingSubtask) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Checkbox(
                                checked = false,
                                onCheckedChange = null, // No action for the checkbox yet
                                modifier = Modifier.padding(start = 16.sdp())
                            )
                            BasicTextField(
                                value = newSubtaskTitle,
                                onValueChange = { newValue -> newSubtaskTitle = newValue },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(start = 8.sdp())
                                    .onKeyEvent { keyEvent ->
                                        if (keyEvent.key == Key.Enter) {
                                            if (newSubtaskTitle.isNotBlank()) {
                                                val newSubtask = Subtask(
                                                    id = UUID.randomUUID().toString(),
                                                    title = newSubtaskTitle
                                                )
                                                onAddSubtask?.invoke(task.id, newSubtask)
                                                newSubtaskTitle = "" // Clear input after adding
                                                isAddingSubtask = false // Hide input field after adding
                                            }
                                            true
                                        } else {
                                            false
                                        }
                                    },
                                singleLine = true,
                                textStyle = MaterialTheme.typography.bodyMedium.copy(color = MaterialTheme.colorScheme.onSurface),
                                decorationBox = { innerTextField ->
                                    Box {
                                        if (newSubtaskTitle.isEmpty()) {
                                            Text(
                                                "Type your subtask here",
                                                style = MaterialTheme.typography.bodyMedium,
                                                color = cardSecondaryTextColor()
                                            )
                                        }
                                        innerTextField()
                                    }
                                },
                                keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
                                keyboardActions = KeyboardActions(onDone = {
                                    if (newSubtaskTitle.isNotBlank()) {
                                        val newSubtask = Subtask(
                                            id = UUID.randomUUID().toString(),
                                            title = newSubtaskTitle
                                        )
                                        onAddSubtask?.invoke(task.id, newSubtask)
                                        newSubtaskTitle = "" // Clear input after adding
                                        isAddingSubtask = false // Hide input field after adding
                                    }
                                })
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun AddSubtaskDialog(
    onDismiss: () -> Unit,
    onAdd: (String) -> Unit
) {
    var subtaskTitle by remember { mutableStateOf("") }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Add Subtask") },
        text = {
            OutlinedTextField(
                value = subtaskTitle,
                onValueChange = { newValue -> subtaskTitle = newValue },
                label = { Text("Subtask") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true,
                keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done)
            )
        },
        confirmButton = {
            TextButton(
                onClick = { onAdd(subtaskTitle) },
                enabled = subtaskTitle.isNotBlank()
            ) {
                Text("Add")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

@Composable
fun TaskEmptyState(
    message: String = "No tasks available",
    actionLabel: String? = null,
    onAction: (() -> Unit)? = null
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(32.sdp()),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.CheckCircle,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.primary.copy(alpha = 0.5f),
            modifier = Modifier.size(80.sdp())
        )
        
        Spacer(modifier = Modifier.height(16.sdp()))
        
        Text(
            text = message,
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
            textAlign = TextAlign.Center
        )
        
        if (actionLabel != null && onAction != null) {
            Spacer(modifier = Modifier.height(16.sdp()))
            
            Button(onClick = onAction) {
                Text(text = actionLabel)
            }
        }
    }
}

@Composable
fun OverdueTaskActionDialog(
    task: Task,
    onDismiss: () -> Unit,
    onComplete: (String) -> Unit,
    onReschedule: (Task) -> Unit
) {
    val nextDate = task.getNextOccurrenceDate()
    val dateFormat = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Overdue Task Action") },
        text = {
            Column(
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("\"${task.title}\" is overdue.")
                
                Spacer(modifier = Modifier.height(8.sdp()))
                
                if (task.isRecurring() && nextDate != null) {
                    Text("Next scheduled date would be: ${dateFormat.format(nextDate)}")
                    
                    Spacer(modifier = Modifier.height(16.sdp()))
                    
                    Text(
                        "What would you like to do with this task?",
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = { 
                    onReschedule(task)
                    onDismiss()
                }
            ) {
                Text("Reschedule")
            }
        },
        dismissButton = {
            TextButton(
                onClick = { 
                    onComplete(task.id)
                    onDismiss()
                }
            ) {
                Text("Mark Complete")
            }
        }
    )
}