package com.taskiq.app.service

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.database.Cursor
import android.net.Uri
import android.provider.CalendarContract
import android.util.Log
import androidx.core.content.ContextCompat
import java.util.Date
import java.util.Calendar as JavaCalendar

data class CalendarEvent(
    val id: String,
    val title: String,
    val description: String?,
    val startDate: Date,
    val endDate: Date?,
    val isAllDay: Boolean
)

class GoogleCalendarService(private val context: Context) {
    private val TAG = "GoogleCalendarService"

    fun hasCalendarPermission(): Boolean {
        return ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.READ_CALENDAR
        ) == PackageManager.PERMISSION_GRANTED
    }

    fun isGoogleAccountSignedIn(): Boolean {
        // For device calendar access, we just need to check if we have permission
        return hasCalendarPermission()
    }


    fun getEvents(): List<CalendarEvent> {
        val events = mutableListOf<CalendarEvent>()

        if (!hasCalendarPermission()) {
            Log.e(TAG, "No calendar permission granted")
            return events
        }

        try {
            // Calculate time range (now to 30 days from now)
            val now = System.currentTimeMillis()
            val calendar = JavaCalendar.getInstance()
            calendar.add(JavaCalendar.DAY_OF_YEAR, 30)
            val thirtyDaysFromNow = calendar.timeInMillis

            Log.d(TAG, "Fetching events from device calendar for next 30 days")

            // Query device calendar for events
            val projection = arrayOf(
                CalendarContract.Events._ID,
                CalendarContract.Events.TITLE,
                CalendarContract.Events.DESCRIPTION,
                CalendarContract.Events.DTSTART,
                CalendarContract.Events.DTEND,
                CalendarContract.Events.ALL_DAY
            )

            val selection = "${CalendarContract.Events.DTSTART} >= ? AND ${CalendarContract.Events.DTSTART} <= ?"
            val selectionArgs = arrayOf(now.toString(), thirtyDaysFromNow.toString())

            val cursor: Cursor? = context.contentResolver.query(
                CalendarContract.Events.CONTENT_URI,
                projection,
                selection,
                selectionArgs,
                "${CalendarContract.Events.DTSTART} ASC"
            )

            cursor?.use { c ->
                Log.d(TAG, "Found ${c.count} total events in device calendar")

                while (c.moveToNext()) {
                    val id = c.getString(c.getColumnIndexOrThrow(CalendarContract.Events._ID))
                    val title = c.getString(c.getColumnIndexOrThrow(CalendarContract.Events.TITLE)) ?: "Untitled Event"
                    val description = c.getString(c.getColumnIndexOrThrow(CalendarContract.Events.DESCRIPTION))
                    val startTime = c.getLong(c.getColumnIndexOrThrow(CalendarContract.Events.DTSTART))
                    val endTime = c.getLong(c.getColumnIndexOrThrow(CalendarContract.Events.DTEND))
                    val isAllDay = c.getInt(c.getColumnIndexOrThrow(CalendarContract.Events.ALL_DAY)) == 1

                    val event = CalendarEvent(
                        id = id,
                        title = title,
                        description = description,
                        startDate = Date(startTime),
                        endDate = if (endTime > 0) Date(endTime) else null,
                        isAllDay = isAllDay
                    )

                    // Filter for important events
                    if (isImportantEvent(event)) {
                        events.add(event)
                        Log.d(TAG, "Important event found: ${event.title}")
                    }
                }
            }

            Log.d(TAG, "Filtered to ${events.size} important events")

        } catch (e: Exception) {
            Log.e(TAG, "Error fetching events from device calendar: ${e.message}")
            e.printStackTrace()
        }

        return events
    }

    private fun isImportantEvent(event: CalendarEvent): Boolean {
        // Check if event has any of these keywords in title or description
        val importantKeywords = listOf(
            "birthday", "anniversary", "wedding", "graduation", "holiday",
            "important", "deadline", "due", "exam", "test", "interview",
            "meeting", "appointment", "doctor", "medical", "dentist",
            "party", "celebration", "festival", "ceremony", "conference",
            "birth", "death", "memorial", "remembrance", "special",
            "event", "occasion", "milestone", "achievement", "award"
        )

        val title = event.title.lowercase()
        val description = event.description?.lowercase() ?: ""

        // Check if event has any important keywords
        val hasImportantKeyword = importantKeywords.any { keyword ->
            title.contains(keyword) || description.contains(keyword)
        }

        // Log event details for debugging
        Log.d(TAG, "Checking event: ${event.title}")
        Log.d(TAG, "Has important keyword: $hasImportantKeyword")

        // Event is considered important if it has important keywords
        val isImportant = hasImportantKeyword
        Log.d(TAG, "Final decision: ${if (isImportant) "IMPORTANT" else "NOT IMPORTANT"}")

        return isImportant
    }
} 