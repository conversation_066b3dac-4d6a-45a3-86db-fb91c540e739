package com.taskiq.app.ui.screens

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import kotlinx.coroutines.delay
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.*
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.taskiq.app.ui.theme.*
import androidx.navigation.NavController
import com.taskiq.app.R
import com.taskiq.app.ui.navigation.Routes
import com.taskiq.app.ui.components.AuthButton
import com.taskiq.app.ui.components.AuthTextField
import com.taskiq.app.ui.theme.contentModifier
import com.taskiq.app.ui.theme.rootContainerModifier
import com.taskiq.app.ui.theme.standardHorizontalPadding
import com.taskiq.app.ui.theme.ProfessionalBlue
import com.taskiq.app.ui.theme.ProfessionalWhite
import com.taskiq.app.ui.theme.Blue80
import com.taskiq.app.viewmodel.AuthViewModel
import com.taskiq.app.ui.theme.responsiveLargeSpacing
import com.taskiq.app.ui.theme.uniformHorizontalPadding
import com.taskiq.app.ui.theme.uniformVerticalPadding
import com.taskiq.app.ui.theme.uniformSectionSpacing
import com.taskiq.app.ui.theme.uniformSmallSpacing
import com.taskiq.app.ui.theme.uniformLargeSpacing

@Composable
fun RegisterScreen(
    navController: NavController,
    viewModel: AuthViewModel
) {
    var email by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    var confirmPassword by remember { mutableStateOf("") }
    var fullName by remember { mutableStateOf("") }

    // Error states
    var emailError by remember { mutableStateOf<String?>(null) }
    var passwordError by remember { mutableStateOf<String?>(null) }
    var confirmPasswordError by remember { mutableStateOf<String?>(null) }
    var fullNameError by remember { mutableStateOf<String?>(null) }

    val registerState by viewModel.registerState.collectAsState()
    val error by viewModel.error.collectAsState()



    // Validation functions
    fun validateFullName(name: String): String? {
        return when {
            name.isBlank() -> "Full name is required"
            name.length < 2 -> "Full name must be at least 2 characters"
            else -> null
        }
    }

    fun validateEmail(email: String): String? {
        return when {
            email.isBlank() -> "Email is required"
            !android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches() -> "Please enter a valid email"
            else -> null
        }
    }

    fun validatePassword(password: String): String? {
        return when {
            password.isBlank() -> "Password is required"
            password.length < 6 -> "Password must be at least 6 characters"
            else -> null
        }
    }

    fun validateConfirmPassword(password: String, confirmPassword: String): String? {
        return when {
            confirmPassword.isBlank() -> "Please confirm your password"
            password != confirmPassword -> "Passwords do not match"
            else -> null
        }
    }

    LaunchedEffect(key1 = registerState) {
        if (registerState == AuthViewModel.AuthState.SUCCESS) {
            navController.navigate(Routes.MAIN) {
                popUpTo(Routes.REGISTER) { inclusive = true }
            }
        }
    }



    Surface(
        color = ProfessionalBlue,
        modifier = rootContainerModifier
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
                    .padding(horizontal = uniformHorizontalPadding() * 2) // Uniform padding for auth screens
                    .widthIn(max = responsiveMaxContentWidth()),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                // App Logo
                Image(
                    painter = painterResource(id = R.drawable.taskiq),
                    contentDescription = "App Logo",
                    modifier = Modifier
                        .size(if (isSmallPhone()) 50.sdp() else responsiveLogoSize())
                        .padding(bottom = uniformSectionSpacing())
                )

                // Title section
                Text(
                    text = "Welcome!",
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.ExtraBold,
                    color = ProfessionalWhite,
                    modifier = Modifier.padding(bottom = uniformSmallSpacing())
                )

                Text(
                    text = "Join to organize your life",
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.Medium,
                    color = ProfessionalWhite.copy(alpha = 0.9f),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(bottom = uniformSectionSpacing())
                )

                // Registration Form
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = uniformSmallSpacing())
                ) {
                        // Full Name Field
                        AuthTextField(
                            value = fullName,
                            onValueChange = {
                                fullName = it
                                fullNameError = validateFullName(it)
                            },
                            label = "Full Name",
                            leadingIcon = Icons.Default.Person,
                            keyboardType = KeyboardType.Text,
                            isError = fullNameError != null,
                            errorMessage = fullNameError
                        )

                        Spacer(modifier = Modifier.height(uniformSectionSpacing()))

                        // Email
                        AuthTextField(
                            value = email,
                            onValueChange = {
                                email = it
                                emailError = validateEmail(it)
                            },
                            label = "Email Address",
                            leadingIcon = Icons.Default.Email,
                            keyboardType = KeyboardType.Email,
                            isError = emailError != null,
                            errorMessage = emailError
                        )

                        Spacer(modifier = Modifier.height(uniformSectionSpacing()))

                        // Password
                        AuthTextField(
                            value = password,
                            onValueChange = {
                                password = it
                                passwordError = validatePassword(it)
                                // Re-validate confirm password if it's already filled
                                if (confirmPassword.isNotBlank()) {
                                    confirmPasswordError = validateConfirmPassword(it, confirmPassword)
                                }
                            },
                            label = "Password",
                            leadingIcon = Icons.Default.Lock,
                            isPassword = true,
                            isError = passwordError != null,
                            errorMessage = passwordError
                        )

                        Spacer(modifier = Modifier.height(uniformSectionSpacing()))

                        // Confirm Password
                        AuthTextField(
                            value = confirmPassword,
                            onValueChange = {
                                confirmPassword = it
                                confirmPasswordError = validateConfirmPassword(password, it)
                            },
                            label = "Confirm Password",
                            leadingIcon = Icons.Default.Lock,
                            isPassword = true,
                            isError = confirmPasswordError != null,
                            errorMessage = confirmPasswordError
                        )

                        Spacer(modifier = Modifier.height(uniformSectionSpacing()))

                        // Register Button
                        AuthButton(
                            text = "Sign Up",
                            onClick = {
                                // Validate all fields
                                fullNameError = validateFullName(fullName)
                                emailError = validateEmail(email)
                                passwordError = validatePassword(password)
                                confirmPasswordError = validateConfirmPassword(password, confirmPassword)

                                // Submit if no errors
                                if (fullNameError == null && emailError == null &&
                                    passwordError == null && confirmPasswordError == null) {
                                    // Split full name into first and last name for the API
                                    val nameParts = fullName.trim().split(" ", limit = 2)
                                    val firstName = nameParts.getOrNull(0) ?: ""
                                    val lastName = nameParts.getOrNull(1) ?: ""
                                    viewModel.signUp(firstName, lastName, email, password)
                                }
                            },
                            isLoading = registerState == AuthViewModel.AuthState.LOADING,
                            enabled = fullName.isNotBlank() && email.isNotBlank() &&
                                     password.isNotBlank() && confirmPassword.isNotBlank()
                        )

                        // Error message
                        AnimatedVisibility(
                            visible = registerState == AuthViewModel.AuthState.ERROR && error != null
                        ) {
                            Card(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(top = uniformLargeSpacing()),
                                colors = CardDefaults.cardColors(
                                    containerColor = MaterialTheme.colorScheme.errorContainer
                                ),
                                shape = RoundedCornerShape(responsiveCornerRadius())
                            ) {
                                Text(
                                    text = error ?: "Failed to register. Please try again.",
                                    color = MaterialTheme.colorScheme.onErrorContainer,
                                    style = MaterialTheme.typography.bodyMedium,
                                    modifier = Modifier.padding(uniformSectionSpacing())
                                )
                            }
                    }
            }

                Spacer(modifier = Modifier.height(uniformSectionSpacing()))

                // Login Link
                Row(
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                Text(
                    text = "Already have an account? ",
                    color = ProfessionalWhite.copy(alpha = 0.9f)
                )
                TextButton(
                    onClick = {
                        navController.navigate(Routes.LOGIN) {
                            popUpTo(Routes.REGISTER) { inclusive = true }
                        }
                    }
                ) {
                    Text(
                        text = "Sign In",
                        color = ProfessionalWhite,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
            }
        }
    }
}