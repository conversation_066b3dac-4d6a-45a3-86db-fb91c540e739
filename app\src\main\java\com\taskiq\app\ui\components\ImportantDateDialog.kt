package com.taskiq.app.ui.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material.icons.filled.Repeat
import androidx.compose.material.icons.outlined.DateRange
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.DatePicker
import androidx.compose.material3.DatePickerDialog
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.material3.ExposedDropdownMenuDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.MenuAnchorType
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.rememberDatePickerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.taskiq.app.model.Task
import com.taskiq.app.model.TaskFrequency
import com.taskiq.app.model.TaskPriority
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.UUID
import com.taskiq.app.ui.theme.cardBackgroundColor
import com.taskiq.app.ui.theme.textFieldColors
import androidx.compose.ui.graphics.Color

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ImportantDateDialog(
    onDismiss: () -> Unit,
    onDateAdded: (Task) -> Unit,
    existingDate: Task? = null
) {
    var title by remember { mutableStateOf(existingDate?.title ?: "") }
    var description by remember { mutableStateOf(existingDate?.description ?: "") }
    var selectedDate by remember { mutableStateOf(existingDate?.dueDate ?: Calendar.getInstance().time) }
    var showDatePicker by remember { mutableStateOf(false) }
    var expandedFrequency by remember { mutableStateOf(false) }
    var selectedFrequency by remember { mutableStateOf(existingDate?.frequency ?: TaskFrequency.ONE_TIME) }
    
    // Determine if we're editing or adding
    val isEditing = existingDate != null
    val dialogTitle = if (isEditing) "Edit Important Date" else "Add Important Date"
    val confirmButtonText = if (isEditing) "Update" else "Add Date"
    
    // Form validation
    val isFormValid = title.isNotBlank()
    
    Dialog(onDismissRequest = onDismiss) {
        Card(
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.Transparent
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = 8.dp
            )
        ) {
            Column(
                modifier = Modifier
                    .padding(16.dp)
                    .fillMaxWidth()
            ) {
                Text(
                    text = dialogTitle,
                    style = MaterialTheme.typography.headlineSmall
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Title Field
                OutlinedTextField(
                    value = title,
                    onValueChange = { title = it },
                    label = { Text("Title") },
                    placeholder = { Text("Enter title") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true,
                    colors = textFieldColors()
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // Description Field (Optional)
                OutlinedTextField(
                    value = description,
                    onValueChange = { description = it },
                    label = { Text("Description (Optional)") },
                    placeholder = { Text("Enter description") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true,
                    colors = textFieldColors()
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // Date Picker
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { showDatePicker = true },
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Outlined.DateRange,
                        contentDescription = "Select Date"
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Text(
                        text = "Date: ${SimpleDateFormat("MMM dd, yyyy", Locale.getDefault()).format(selectedDate)}",
                        style = MaterialTheme.typography.bodyLarge
                    )
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // Repeat Dropdown (renamed from Frequency)
                ExposedDropdownMenuBox(
                    expanded = expandedFrequency,
                    onExpandedChange = { expandedFrequency = it }
                ) {
                    OutlinedTextField(
                        modifier = Modifier
                            .fillMaxWidth()
                            .menuAnchor(MenuAnchorType.PrimaryNotEditable, true),
                        readOnly = true,
                        value = when(selectedFrequency) {
                            TaskFrequency.ONE_TIME -> "No Repeat"
                            TaskFrequency.MONTHLY -> "Monthly"
                            TaskFrequency.YEARLY -> "Yearly"
                            TaskFrequency.CUSTOM -> "Custom"
                            else -> "No Repeat"
                        },
                        onValueChange = { },
                        label = { Text("Repeat") },
                        trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expandedFrequency) },
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Filled.Repeat,
                                contentDescription = "Repeat"
                            )
                        },
                        colors = textFieldColors()
                    )
                    
                    ExposedDropdownMenu(
                        expanded = expandedFrequency,
                        onDismissRequest = { expandedFrequency = false }
                    ) {
                        // Only show No Repeat, Monthly, Yearly, and Custom options
                        listOf(
                            TaskFrequency.ONE_TIME to "No Repeat",
                            TaskFrequency.MONTHLY to "Monthly",
                            TaskFrequency.YEARLY to "Yearly",
                            TaskFrequency.CUSTOM to "Custom"
                        ).forEach { (frequency, label) ->
                            DropdownMenuItem(
                                text = { Text(label) },
                                onClick = {
                                    selectedFrequency = frequency
                                    expandedFrequency = false
                                }
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(
                        onClick = onDismiss
                    ) {
                        Text("Cancel")
                    }
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Button(
                        onClick = {
                            val task = if (isEditing) {
                                existingDate!!.copy(
                                    title = title,
                                    description = description,
                                    dueDate = selectedDate,
                                    frequency = selectedFrequency
                                )
                            } else {
                                Task(
                                    id = UUID.randomUUID().toString(),
                                    title = title,
                                    description = description,
                                    dueDate = selectedDate,
                                    frequency = selectedFrequency,
                                    isCompleted = false,
                                    priority = TaskPriority.HIGH, // Important dates are set to HIGH priority
                                    userId = "current_user_id",
                                    createdAt = Date()
                                )
                            }
                            onDateAdded(task)
                        },
                        enabled = isFormValid
                    ) {
                        Text(confirmButtonText)
                    }
                }
            }
        }
    }
    
    // Date Picker Dialog
    if (showDatePicker) {
        val datePickerState = rememberDatePickerState(
            initialSelectedDateMillis = selectedDate.time
        )
        
        DatePickerDialog(
            onDismissRequest = { showDatePicker = false },
            confirmButton = {
                TextButton(
                    onClick = {
                        datePickerState.selectedDateMillis?.let { millis ->
                            selectedDate = Date(millis)
                        }
                        showDatePicker = false
                    }
                ) {
                    Text("OK")
                }
            },
            dismissButton = {
                TextButton(onClick = { showDatePicker = false }) {
                    Text("Cancel")
                }
            }
        ) {
            DatePicker(state = datePickerState)
        }
    }
}

// Helper function to capitalize a string
private fun String.capitalize(): String {
    return this.replaceFirstChar { if (it.isLowerCase()) it.titlecase(Locale.getDefault()) else it.toString() }
}