plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.compose)
    alias(libs.plugins.google.gms.google.services)
    alias(libs.plugins.google.firebase.crashlytics)
}

android {
    namespace 'com.taskiq.app'
    compileSdk 35

    defaultConfig {
        applicationId "com.taskiq.app"
        minSdk 29
        targetSdk 35
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = '11'
    }
    buildFeatures {
        compose true
    }
    
    // Add packaging options to handle duplicate files
    packaging {
        resources {
            excludes += '/META-INF/{DEPENDENCIES,LICENSE}'
            excludes += '/META-INF/INDEX.LIST'
        }
    }
}

dependencies {

    implementation libs.androidx.core.ktx
    implementation libs.androidx.lifecycle.runtime.ktx
    implementation libs.androidx.activity.compose
    implementation platform(libs.androidx.compose.bom)
    implementation libs.androidx.ui
    implementation libs.androidx.ui.graphics
    implementation libs.androidx.ui.tooling.preview
    implementation libs.androidx.material3
    implementation libs.androidx.material.icons.extended
    implementation libs.firebase.crashlytics
    implementation libs.firebase.auth
    implementation libs.androidx.navigation.compose
    implementation libs.firebase.database
    implementation libs.androidx.credentials
    implementation libs.androidx.credentials.play.services.auth
    implementation libs.googleid
    testImplementation libs.junit
    androidTestImplementation libs.androidx.junit
    androidTestImplementation libs.androidx.espresso.core
    androidTestImplementation platform(libs.androidx.compose.bom)
    androidTestImplementation libs.androidx.ui.test.junit4
    debugImplementation libs.androidx.ui.tooling
    debugImplementation libs.androidx.ui.test.manifest

    // WorkManager dependency
    implementation 'androidx.work:work-runtime-ktx:2.10.1'

    // Accompanist SwipeRefresh dependency
    implementation libs.accompanist.swiperefresh

    // Gson for JSON serialization/deserialization
    implementation 'com.google.code.gson:gson:2.13.1'

    // Add DataStore dependencies
    implementation 'androidx.datastore:datastore-preferences:1.1.7'
    implementation 'androidx.datastore:datastore-preferences-core:1.1.7'
    
    // Google Sign-In dependencies
    implementation 'com.google.android.gms:play-services-auth:21.3.0'
    
    // Gmail API dependencies
    implementation 'com.google.api-client:google-api-client-android:2.8.0'
    implementation 'com.google.apis:google-api-services-gmail:v1-rev20230612-2.0.0'
    implementation 'com.google.auth:google-auth-library-oauth2-http:1.37.0'
    implementation 'com.google.http-client:google-http-client-gson:1.47.0'

    // Google Calendar API
    implementation 'com.google.api-client:google-api-client-android:2.8.0'
    implementation 'com.google.apis:google-api-services-calendar:v3-rev411-1.25.0'
    implementation 'com.google.oauth-client:google-oauth-client-jetty:1.39.0'
    implementation 'com.google.auth:google-auth-library-oauth2-http:1.37.0'

    // Google Drive API dependencies
    implementation 'com.google.apis:google-api-services-drive:v3-rev20240914-2.0.0'

    // SDP library for responsive layouts
    implementation 'com.intuit.sdp:sdp-android:1.1.1'
    implementation 'com.intuit.ssp:ssp-android:1.1.1'
}

// Custom task that builds without cleaning
tasks.register('buildSkipClean') {
    dependsOn 'assembleDebug', 'assembleRelease', 'bundleRelease', 'check'
    description = 'Build the project without cleaning'
    group = 'build'
}