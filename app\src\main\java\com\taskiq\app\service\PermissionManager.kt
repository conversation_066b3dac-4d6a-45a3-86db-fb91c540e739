package com.taskiq.app.service

import android.Manifest
import android.app.Activity
import android.app.AlarmManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.Settings
import android.util.Log
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

class PermissionManager(private val context: Context) {
    private val TAG = "PermissionManager"
    
    companion object {
        const val REQUEST_NOTIFICATION_PERMISSION = 1001
        const val REQUEST_CALENDAR_PERMISSION = 1002
        const val REQUEST_EXACT_ALARM_PERMISSION = 1003
        const val REQUEST_ALL_PERMISSIONS = 1004
    }
    
    /**
     * Check if notification permission is granted
     */
    fun hasNotificationPermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            true // Permission not required for Android 12 and below
        }
    }
    
    /**
     * Check if calendar permission is granted
     */
    fun hasCalendarPermission(): Boolean {
        return ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.READ_CALENDAR
        ) == PackageManager.PERMISSION_GRANTED
    }

    /**
     * Check if GET_ACCOUNTS permission is granted
     */
    fun hasGetAccountsPermission(): Boolean {
        return ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.GET_ACCOUNTS
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * Check if exact alarm permission is granted (Android 12+)
     */
    fun hasExactAlarmPermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            alarmManager.canScheduleExactAlarms()
        } else {
            true // Permission not required for Android 11 and below
        }
    }
    
    /**
     * Request notification permission
     */
    fun requestNotificationPermission(activity: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (!hasNotificationPermission()) {
                Log.d(TAG, "Requesting notification permission")
                ActivityCompat.requestPermissions(
                    activity,
                    arrayOf(Manifest.permission.POST_NOTIFICATIONS),
                    REQUEST_NOTIFICATION_PERMISSION
                )
            }
        }
    }
    
    /**
     * Request calendar permission
     */
    fun requestCalendarPermission(activity: Activity) {
        if (!hasCalendarPermission()) {
            Log.d(TAG, "Requesting calendar permission")
            ActivityCompat.requestPermissions(
                activity,
                arrayOf(Manifest.permission.READ_CALENDAR),
                REQUEST_CALENDAR_PERMISSION
            )
        }
    }
    
    /**
     * Request exact alarm permission (opens system settings)
     */
    fun requestExactAlarmPermission(activity: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (!hasExactAlarmPermission()) {
                Log.d(TAG, "Requesting exact alarm permission")
                val intent = Intent(Settings.ACTION_REQUEST_SCHEDULE_EXACT_ALARM).apply {
                    data = Uri.fromParts("package", context.packageName, null)
                }
                activity.startActivity(intent)
            }
        }
    }
    
    /**
     * Request all required permissions at app startup
     */
    fun requestAllRequiredPermissions(activity: Activity) {
        val permissionsToRequest = mutableListOf<String>()

        // Check notification permission (Android 13+)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (!hasNotificationPermission()) {
                permissionsToRequest.add(Manifest.permission.POST_NOTIFICATIONS)
            }
        }

        // Check calendar permission
        if (!hasCalendarPermission()) {
            permissionsToRequest.add(Manifest.permission.READ_CALENDAR)
        }

        // Check GET_ACCOUNTS permission (needed for backup functionality)
        if (!hasGetAccountsPermission()) {
            permissionsToRequest.add(Manifest.permission.GET_ACCOUNTS)
        }

        // Request runtime permissions
        if (permissionsToRequest.isNotEmpty()) {
            Log.d(TAG, "Requesting permissions: ${permissionsToRequest.joinToString(", ")}")
            ActivityCompat.requestPermissions(
                activity,
                permissionsToRequest.toTypedArray(),
                REQUEST_ALL_PERMISSIONS
            )
        }
        
        // Handle exact alarm permission separately (requires system settings)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (!hasExactAlarmPermission()) {
                showExactAlarmPermissionDialog(activity)
            }
        }
    }
    
    /**
     * Show dialog for exact alarm permission
     */
    private fun showExactAlarmPermissionDialog(activity: Activity) {
        android.app.AlertDialog.Builder(activity)
            .setTitle("Permission Required")
            .setMessage("TaskIQ needs permission to schedule exact alarms for task notifications. Please grant this permission in settings.")
            .setPositiveButton("Open Settings") { _, _ ->
                requestExactAlarmPermission(activity)
            }
            .setNegativeButton("Later", null)
            .setCancelable(false)
            .show()
    }
    
    /**
     * Check if app is first run and needs permission setup
     */
    fun isFirstRun(): Boolean {
        val sharedPrefs = context.getSharedPreferences("app_preferences", Context.MODE_PRIVATE)
        return !sharedPrefs.getBoolean("permissions_requested", false)
    }
    
    /**
     * Mark that permissions have been requested
     */
    fun markPermissionsRequested() {
        val sharedPrefs = context.getSharedPreferences("app_preferences", Context.MODE_PRIVATE)
        sharedPrefs.edit().putBoolean("permissions_requested", true).apply()
        Log.d(TAG, "Marked permissions as requested")
    }
    
    /**
     * Get list of missing permissions
     */
    fun getMissingPermissions(): List<String> {
        val missing = mutableListOf<String>()
        
        if (!hasNotificationPermission()) {
            missing.add("Notifications")
        }
        
        if (!hasCalendarPermission()) {
            missing.add("Calendar")
        }
        
        if (!hasExactAlarmPermission()) {
            missing.add("Exact Alarms")
        }
        
        return missing
    }
}
